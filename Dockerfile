FROM node:22

WORKDIR /app
ENV PATH /app/node_modules/.bin:$PATH

COPY package.json /app/package.json
RUN npm install
COPY . /app

# Build-time environment variables
ARG VITE_ENV
ARG VITE_API_URL
ARG VITE_JUMIO_SUCCESS_URL
ARG VITE_JUMIO_ERROR_URL
ARG VITE_BO_ACCESS_TOKEN

ENV VITE_ENV=$VITE_ENV
ENV VITE_API_URL=$VITE_API_URL
ENV VITE_JUMIO_SUCCESS_URL=$VITE_JUMIO_SUCCESS_URL
ENV VITE_JUMIO_ERROR_URL=$VITE_JUMIO_ERROR_URL
ENV VITE_BO_ACCESS_TOKEN=$VITE_BO_ACCESS_TOKEN

RUN npm run build
