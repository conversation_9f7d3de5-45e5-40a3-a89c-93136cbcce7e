version: '3.7'
services:
  affiliate-web:
    container_name: affiliate-web
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VITE_ENV: ${VITE_ENV}
        VITE_API_URL: ${VITE_API_URL}
        VITE_BO_ACCESS_TOKEN: ${VITE_BO_ACCESS_TOKEN}
        VITE_JUMIO_SUCCESS_URL: ${VITE_JUMIO_SUCCESS_URL}
        VITE_JUMIO_ERROR_URL: ${VITE_JUMIO_ERROR_URL}
        VITE_SERVICE_AFFILIATE_INTERNAL_V1: ${VITE_SERVICE_AFFILIATE_INTERNAL_V1}
    volumes:
      - .:/app
      - /app/node_modules
    env_file:
      - ./.env
    command: sh -c "npm run dev -- --host"
    ports:
      - 8081:5173

networks:
  default:
    name: piwinet
    external: true
# Workaround for https://github.com/vitejs/vite/discussions/15532
volumes:
  node_modules:
