@import url("https://use.typekit.net/afy7ntn.css");
@import "tailwindcss" prefix(tw);

@theme {
  --color-bitcoin: #F7931A;

  --color-trc20: #C1272D;

  --color-erc20: #627FEB;
  --color-usdc: #627FEB;

  --color-danger-100: #ffcbcb;
  --color-danger: #FF6060;

  --color-success-50: #e8f4f3;
  --color-success-100: #c3eedc;
  --color-success-200: #b3ddd8;
  --color-success: #47CC95;
  --color-success-900: #1d9689;

  --color-registration-50: #E8F5E9;
  --color-registration-200: #b9e3bf;
  --color-registration-900: #4CAF50;

  --color-conversion-50: #E3F2FD;
  --color-conversion-200: #acd7f1;
  --color-conversion-900: #0084d1;

  --color-secondary: #89B8FA;

  --color-warning-50: #fff5e5;
  --color-warning-200: #ffdfac;
  --color-warning: #FFC107;
  --color-warning-900: #fd9a01;

  --color-primary: #FBCC04;

  --color-info: #539AFB;

  --color-dark: #172530;
  --color-dark-200: #2C3642;

  --color-light: #EFEFF4;
  --color-disabled: #e0e0e0;
}

@layer utilities {
  .gradient-black { background: linear-gradient(to right, #172530, #344757) !important }
  .gradient-primary { background: linear-gradient(to right, #FBCC04, #FADA4B) }
  .gradient-blue { background: linear-gradient(to right, #539AFB, #89B8FA) !important}
  .gradient-green { background: linear-gradient(to right, #47CC95, #5CCC9D) }
  .gradient-red { background: linear-gradient(to right, #FF6060, #FD7474) }
  .gradient-usdttrc20 { background: linear-gradient(to right, #C1272D, #C13A44) }
  .gradient-usdterc20 { background: linear-gradient(to right, #627FEB, #627FEB) }
  .gradient-bitcoin { background: linear-gradient(to right, #F7931A, #F7A43F) }
  .gradient-skrill { background: linear-gradient(to right, #862165, #AD4D8D) }
  .gradient-neteller { background: linear-gradient(to right, #83BA3B, #99C65D) }
  .gradient-usdc { background: linear-gradient(to right, #627FEB, #627FEB) !important; }
}

@layer components {
  .chip-primary {
    color: #FFFFFF !important;
    background: linear-gradient(to right, #FBCC04, #FADA4B) !important;
  }
  .chip-info {
    color: #FFFFFF !important;
    background: linear-gradient(to right, #539AFB, #89B8FA) !important;
  }
  .chip-success {
    color: #FFFFFF !important;
    background: linear-gradient(to right, #47CC95, #5CCC9D) !important;
  }
  .chip-error {
    color: #FFFFFF !important;
    background: linear-gradient(to right, #FF6060, #FD7474) !important;
  }
  .chip-usdttrc20 {
    color: #FFFFFF !important;
    background: linear-gradient(to right, #C1272D, #C13A44) !important;
  }
  .chip-usdterc20 {
    color: #FFFFFF !important;
    background: linear-gradient(to right, #627FEB, #627FEB) !important;
  }
  .chip-bitcoin {
    color: #FFFFFF;
    background: linear-gradient(to right, #F7931A, #F7A43F);
  }
  .chip-skrill {
    color: #FFFFFF;
    background: linear-gradient(to right, #862165, #AD4D8D);
  }
  .chip-neteller {
    color: #FFFFFF;
    background: linear-gradient(to right, #83BA3B, #99C65D);
  }
}

/* Prevent outer scroll bar in Windows machine */
html {
  overflow-y: hidden !important;
}

body {
  font-family: proxima-nova, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
}
.v-application {
  font-family: proxima-nova, sans-serif !important;
  font-style: normal;
  font-weight: 400;
  line-height: 1.2 !important;
}
.v-input--error .v-input__details .v-messages {
  margin-top: -3px !important;
}
.row {
  margin: 0 -12px !important;
}
.v-icon--size-default {
  font-size: 16px !important;
  color: theme(--color-gray-500)
}
/* pagination */
.v-pagination__item.v-pagination__item--is-active button {
  background-color: theme(--color-primary) !important;
  color: #fff !important;
  opacity: 1 !important;
}

/* Snackbar Customized General*/
.v-snack {
  font-size: 12px !important;
  line-height: 1.5;
}
.v-snack__wrapper {
  background: linear-gradient(to right, #172530, #1c2d3b) !important;
}

.withdrawal-gateway .v-expansion-panel-title:hover > .v-expansion-panel-title__overlay {
  background: inherit !important;
}

/* Pagination */
.v-pagination .v-icon--size-default {
  color: theme(--color-dark)
}

/* Chip */
.v-chip.v-size--default {
  font-size: 10px !important;
  height: auto !important;
}
.loading-wrapper {
  display: flex;
  height: 100vh;
  align-items: center;
  justify-content: center;
}
.login-bg {
  background-image: url(./src/assets/login-bg.svg);
  background-position: 30% bottom;
  background-repeat: no-repeat;
  background-size: 230%;
}
.wb-container {
  background-image: url(./src/assets/withdrawal-bg.png);
  background-position: right bottom;
  background-repeat: no-repeat;
  background-size: cover;
}
.br-0 {
  border-radius: 0 !important;
}
.br-wrapper {
  border-radius: 10px 10px 0 0;
  margin-top: -15px;
}
.logo-wrapper {
  margin-left: auto;
  margin-right: auto;
}
.custom-card {
  border-radius: 5px;
}
.custom-card-shadow {
  border-radius: 5px;
  -webkit-box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.10);
  -moz-box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.10);
  box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.10);
}
.custom-card-shadow > .v-expansion-panel__shadow {
  box-shadow: none !important;
}
.flex-container {
  display: flex;
}
.flex-inline-container {
  display: inline-flex;
}
.container {
  padding: 15px !important;
}
.v-card {
  box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0), 0px 2px 2px 0px rgba(0, 0, 0, 0), 0px 1px 5px 0px rgba(0, 0, 0, 0) !important;
}
.v-application--wrap {
  background-color: #EFEFF4;
}
.no-line {
  text-decoration: none;
}
.divider {
  height: 1px;
  width: 100%;
}
.v-btn--contained {
  box-shadow: none !important;
}
.get-code-btn {
  height: 40px !important;
  min-width: auto !important;
  padding: 0 15px !important;
  font-size: 13px !important;
  text-transform: capitalize !important;
  font-weight: 600 !important;
  margin-left: 5px;
}
.request-btn {
  height: auto !important;
  min-width: auto !important;
  padding: 10px 15px !important;
  text-transform: capitalize !important;
  font-weight: 600 !important;
  color: #fff !important;
}
.ghost-btn {
  border: 1px solid #70889a;
}
.custom-loader {
  animation: loader 1s infinite;
  display: flex;
}
.cursor-pointer {
  cursor: pointer;
}
@-moz-keyframes loader {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
@-webkit-keyframes loader {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
@-o-keyframes loader {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes loader {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
.piwiblue {
  background-color: #172530 !important;
}

.login-tab-wrapper {
  padding: 0 25px;
}
.login-tab-wrapper .v-tab {
  margin-left: 0 !important;
  text-transform: none;
}
.login-tab-wrapper a.v-tab {
  width: 50%;
}
.login-tab-wrapper .theme--dark.v-tabs .v-tabs-bar {
  background-color: #172530 !important;
}
.login-tab-wrapper .v-tabs-bar {
  height: 40px !important;
}
.login-tab-wrapper .v-label {
  font-size: 14px;
}
.login-tab-wrapper .v-tabs-slider-wrapper {
  height: 3px !important;
}
.login-form-wrapper .v-input__slot {
  padding: 0 10px !important;
}
.login-form-wrapper .v-text-field .v-input__details,
#updatePasswordForm .v-text-field .v-input__details {
  padding-inline: 0;
  align-items: flex-start;
}
.withdrawal-form-container .v-text-field .v-input__details {
  padding: 0;
  min-height: 18px;
}
.login-form-wrapper .v-messages__message {
  font-size: 10px;
  text-align: left;
}
.login-form-wrapper .v-text-field__details {
  padding: 0 !important;
}
.login-form-wrapper .v-icon.v-icon {
  color: #89B8FA;
}
.login-form-wrapper .v-text-field input {
  font-size: 14px;
}
.login-form-wrapper .v-input--selection-controls {
  margin-top: 0;
  padding-top: 0;
}
.login-form-wrapper .v-text-field__details {
  margin-bottom: 0 !important;
}
.login-form-wrapper .v-btn {
  letter-spacing: 0.25px;
}
.login-switch-wrapper .v-input__slot {
  padding: 0 !important;
  margin-bottom: 0 !important;
}
.theme--dark .v-label {
  color: rgba(255, 255, 255, 1) !important;
}
.login-phone .phone-select {
  border-radius: 5px 0 0 5px;
}
.login-phone .username-input {
  border-radius: 0 5px 5px 0;
}
.login-phone .username-input .v-input__icon {
  display: none;
}
.login-phone .username-input  .v-input__slot {
  padding: 0 10px 0 0 !important
}

/* Dashboard */
.balance-border {
  border-right: 1px solid #DEDEDE;
}
.balance-header-border {
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}
.header-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.header-content-wrapper {
  display: flex;
  align-items: center;
}
.notification-icon {
  position: relative;
}
.notification-icon-circle {
  position: absolute;
  font-size: 9px !important;
  right: 2px;
  top: -4px;
}
.profile-icon {
  display: inline-block;
  width: 30px;
  height: 30px;
  text-align: center;
  border-radius: 50%;
  padding-top: 9px;
  overflow: hidden;
  -webkit-box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.05);
  -moz-box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.05);
  box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.05);
}
.customize-panel-wrapper .v-icon.v-icon {
  font-size: 12px;
}
.customize-panel-wrapper .v-expansion-panel__shadow {
  box-shadow: none !important;
}
.v-expansion-panel {
  margin-top: 10px !important;
}
.customize-panel-wrapper .v-expansion-panel-header {
  font-size: 12px;
  padding: 10px;
  min-height: auto;
}
.customize-panel-bigger .v-expansion-panel-header {
  padding: 15px 10px;
}
.customize-panel-wrapper .v-expansion-panel--active .v-expansion-panel-header {
  min-height: auto;
}
.v-expansion-panel-content__wrap {
  padding: 0 15px 15px !important;
}
.v-expansion-panel::before {
  -webkit-box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.05) !important;
  -moz-box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.05) !important;
  box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.05) !important;
}
.v-expansion-panels:not(.v-expansion-panels--accordion) > .v-expansion-panel--active {
  border-radius: 7px !important;
  overflow: hidden;
}
.theme--light.v-expansion-panels .v-expansion-panel-header .v-expansion-panel-header__icon .v-icon {
  color: #cecece !important;
}
.dashboard-header-wrapper .col {
  padding-top: 0 !important;
  max-width: 300px !important;
}
.dashboard-footer-wrapper {
  background-color: #fff !important;
  position: fixed !important;
  bottom: 0;
  width: 100%;
  padding: 15px !important;
}
.dashboard-footer-wrapper span, h1 {
  display: block;
  width: 100%;
}
.date-covered-container {
  margin-top: -15px;
}

.profile-img {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background-image: url(./src/assets/user.svg);
}
.sidenav-footer {
  position: absolute;
  bottom: 0;
  border-top: 1px solid #DEDEDE;
}

/* Withdrawal */
.withdrawal-gateway .v-expansion-panel-title {
  padding: 15px 10px !important;
}
.gateway-wrapper .v-col {
  padding: 0 12px !important;
}
.v-messages__message {
  font-size: 10px;
}
.v-input__slot {
  margin-bottom: 3px;
}
.gateway-inner-wrapper .col {
  padding: 0 !important;
}
.gateway-inner-wrapper .v-input__slot {
  min-height: 40px !important;
}
.gateway-inner-wrapper input {
  font-size: 14px;
}
.gateway-inner-wrapper .v-label {
  font-size: 12px;
}
.gateway-inner-wrapper .v-text-field--outlined .v-label {
  top: auto;
  background-color: #fff;
  padding: 0 5px;
}
.gateway-inner-wrapper .v-label--active {
  top: 16px !important;
}
.gateway-inner-wrapper .v-input__slot {
  margin-bottom: 0 !important
}
.gateway-inner-wrapper .v-text-field.v-text-field--enclosed .v-text-field__details {
  margin-bottom: 0;
}
.gateway-inner-wrapper .v-text-field__suffix {
  font-size: 12px;
  height: 100%;
  color: #fff;
  padding: 10px !important;
  border-radius: 0 5px 5px 0;
}
.gateway-usdttrc20 .v-text-field__suffix {
  background: linear-gradient(to right, #C1272D, #C13A44);
}
.gateway-usdterc20 .v-text-field__suffix {
  background: linear-gradient(to right, #627FEB, #627FEB);
}
.gateway-usdc .v-text-field__suffix {
  background: linear-gradient(to right, #627FEB, #627FEB);
}
.gateway-bitcoin .v-text-field__suffix {
  background: linear-gradient(to right, #F7931A, #F7A43F);
}
.gateway-skrill .v-text-field__suffix {
  background: linear-gradient(to right, #862165, #AD4D8D);
  opacity: 1 !important;
}
.gateway-neteller .v-text-field__suffix {
  background: linear-gradient(to right, #83BA3B, #99C65D);
  opacity: 1 !important;
}
.usdttrc20-logo { width: 110px; }
.usdterc20-logo { width: 110px; }
.usdc-logo { width: 80px; }
.btc-logo { width: 90px; }
.skrill-logo { width: 63px; }
.neteller-logo { width: 90px; }
.withdrawal-body-wrapper .v-expansion-panel-header__icon {
    display: none !important;
  }

/* Transaction History */
.bottom-sheet-container .theme--light.v-chip:not(.v-chip--active) {
  background: #fff !important;
}
.bottom-sheet-container .v-chip-group .v-chip {
  margin: 0px 5px 5px 0;
}
.transaction-filter-date .v-text-field--outlined > .v-input__control > .v-input__slot {
  min-height: 40px;
}
.transaction-filter-date .v-text-field--outlined .v-label {
  top: 10px;
  font-size: 14px;
}
.transaction-filter-date .v-text-field.v-text-field--enclosed .v-input__prepend-inner {
  margin-top: 8px;
}
.transaction-filter-date .v-theme--light.v-icon {
  font-size: theme(--text-sm);
  color: theme(--color-gray-400);
}
.transaction-filter-date .v-theme--light.v-field--focused .v-icon {
  color: theme(--color-primary);
}
.transaction-filter-date .v-text-field.v-text-field--enclosed > .v-input__control > .v-input__slot {
  padding: 0 8px;
}
.transaction-filter-date .v-text-field__slot {
  font-size: 14px;
}
.transaction-filter-date .v-text-field.v-text-field--enclosed .v-text-field__details {
  margin-bottom: 0;
}
.transaction-filter-date label.v-label.v-label--active.theme--light {
  top: 17px;
  left: -24px !important;
}
.transaction-filter-date .v-input__slot {
  margin-bottom: 0;
}
.transaction-filter-date .p-l-0 {
  padding-left: 0;
}
.transaction-filter-date .ghost-btn {
  border: 1px solid transparent;
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: #fff;
  font-size: 12px !important;
}
.input-header-wrapper .v-text-field.v-text-field--solo .v-input__control {
  min-height: 35px;
}
.input-header-wrapper label.v-label.theme--light {
  font-size: 14px;
}
.input-header-wrapper .v-text-field.v-text-field--enclosed .v-input__prepend-inner {
  margin-top: 0;
}
.input-header-wrapper .theme--light.v-icon {
  color: #cecece;
  font-size: 18px;
}
.chip-active {
  color: #539AFB !important;
  border: 1px solid #539AFB !important;
}
.total-statement-wrapper .col {
  padding-top: 0 !important;
  padding-bottom: 3px !important;
}
.v-btn--rounded {
  border-radius: 5px;
}
.language-flag {
  border: 2px solid #DEDEDE;
  border-radius: 5px;
  padding: 10px 10px 5px 10px;
  width: 25%;
}
.active-language {
  border: 2px solid #FBCC04;
}
.modal-img {
  margin-left: auto;
  margin-right: auto;
}
aside.v-navigation-drawer.v-navigation-drawer--absolute.v-navigation-drawer--is-mobile.v-navigation-drawer--open.v-navigation-drawer--temporary.theme--light {
  height: 100vh !important;
  position: fixed;
}

/* Desktop */
.desktop-view {
  display: none;
}
.desktop-header-wrapper {
  position: fixed;
  width: 100%;
  padding: 10px 20px;
  z-index: 2000;
  height: 92px;
}
.desktop-header-inner {
  height: 72px;
}
.desktop-header-wrapper .notification-icon .fa-bell {
  font-size: 18px;
}
.desktop-header-wrapper .notification-icon-circle {
  top: -2px;
}
.desktop-header-wrapper .v-list-item__title {
  font-size: 14px;
}
.balance-avatar {
  background-color: rgba(255, 255, 255, 0.1);
}
.balance-divider {
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}
.dashboard-total-wrapper {
  position: fixed;
  padding: 20px 5% 15px 10%;
  bottom: 0;
  left: 0;
  width: 100%;
  -webkit-box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.10);
  -moz-box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.10);
  box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.10);
}
.dashboard-total-wrapper .v-btn__content {
  font-weight: 700;
  padding: 10px 20px;
}
.desktop-language {
  width: 85px;
  height: 35px;
  display: flex;
}
.desktop-language .v-text-field__details {
  display: none;
}
.desktop-language .v-text-field {
  padding-top: 0;
  margin-top: 0;
}
.desktop-language .v-icon.v-icon {
  font-size: 14px;
}
.desktop-language .v-select__selection {
  font-size: 12px;
}
.desktop-header-details {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.desktop-sidenav {
  position: fixed;
  z-index: 1000;
  top: 90px;
}
.desktop-sidenav .theme--light.v-navigation-drawer {
  background: linear-gradient(to right, #539AFB, #89B8FA);
}
.desktop-sidenav .v-navigation-drawer__content {
  height: 100vh;
}
.desktop-sidenav .theme--light.v-sheet {
  background-color: transparent !important;
}
.desktop-sidenav .v-card > *:last-child:not(.v-btn):not(.v-chip) {
  border-radius: 0;
}
.desktop-sidenav .theme--light.v-list-item:not(.v-list-item--active):not(.v-list-item--disabled) {
    color: #fff !important;
}
.desktop-sidenav .v-list-item--dense .v-list-item__icon, .v-list--dense .v-list-item .v-list-item__icon {
  margin-top: 15px !important;
  margin-bottom: 15px !important;
}
.fixed-sidenav-wrapper {
  width: 330px;
  height: 100vh;
  position: fixed;
  top: 0;
  z-index: 1000;
  background-color: #fff;
  -webkit-box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.05) !important;
  -moz-box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.05) !important;
  box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.05) !important;
}
.fixed-sidenav-wrapper .gradient-blue {
  padding: 40px 20px 60px;
}
.fixed-sidenav-wrapper .profile-img {
  width: 75px;
  height: 75px;
}
.fixed-sidenav-wrapper .profile-img .font-xxl {
    font-size: 30px;
}
.fixed-sidenav-wrapper .balance-container {
  margin: -30px 15px 0 15px;
  display: flex;
}
.fixed-sidenav-wrapper .balance-container div {
  width: 50%;
  padding: 15px 0;
}
.fixed-sidenav-wrapper .balance-container div:nth-child(1) {
  margin-right: 10px;
}
.fixed-sidenav-wrapper .theme--light.v-btn:not(.v-btn--flat):not(.v-btn--text):not(.v-btn--outlined) {
  background-color: transparent;
  text-transform: unset;
  font-weight: 600 !important;
  font-size: 14px !important;
  justify-content: flex-start;
}
.desktop-content-wrapper {
  padding-left: 375px !important;
  margin-top: 65px;
}
.v-dialog {
  box-shadow: none !important;
  width: 100%;
}
.ipad-view {
  display: none;
}

.banner-body .v-text-field--outlined > .v-input__control > .v-input__slot, .banner-body .v-text-field--outlined .v-input__control, .banner-body .v-select__slot {
  min-height: 40px;
}
.banner-body .v-select__selections {
  padding: 0 !important;
}
.banner-body .v-text-field.v-text-field--enclosed .v-input__append-inner {
  margin-top: 8px;
}
.website-validation {
  font-size: 10px !important;
  position: absolute;
  top: -22px;
  left: 12px;
}

/* Responsive */
  @media only screen and (min-width: 768px) {
  .desktop-body-wrapper {
    margin: 0;
  }
  .body-wrapper {
    margin: 0;
  }
  .mobile-view {
    display: none !important;
  }
  .ipad-view {
    display: inherit !important;
  }
  .desktop-view {
    display: inherit;
  }
  .login-bg {
    background-size: 100%;
  }
  .login-tab-wrapper {
    padding: 0 100px;
  }
  .custom-card-shadow {
    border-radius: 15px !important;
    overflow: hidden;
  }
  .v-expansion-panels {
    border-radius: 15px !important;
  }
  .v-expansion-panels:not(.v-expansion-panels--accordion) > .v-expansion-panel--active {
    border-radius: 15px !important;
  }
  .gateway-wrapper .v-col {
    padding: 0 20px !important;
    font-size: 14px;
    line-height: 1.3;
  }
  .gateway-inner-wrapper {
    font-size: 14px;
  }
  .withdrawal-summary-container {
    width: 97%;
    margin-left: 10px;
    margin-top: 20px;
  }
  .withdrawal-summary-container .text-sm {
    font-size: 14px;
  }
  .withdrawal-form-container {
    margin: 30px 10px 0 10px;
  }
  .withdrawal-form-container .v-input {
    width: 48%;
  }
  .withdrawal-form-container .v-input input {
    max-height: 40px;
    height: 40px;
  }
  .gateway-inner-wrapper .v-text-field__suffix {
    font-size: 14px;
    /* padding: 14px 15px !important; */
    height: 40px;
  }
  .verification-wrapper .get-code-btn {
    height: 40px !important;
    font-size: 14px !important;
    margin-left: -10px;
  }
  .request-btn-wrapper .request-btn {
    max-width: 200px !important;
    min-width: 200px !important;
    width: 200px;
    margin: 10px;
  }
  .usdttrc20-logo { width: 150px; }
  .usdterc20-logo { width: 150px; }
  .usdc-logo { width: 100px; }
  .btc-logo { width: 120px; }
  .skrill-logo { width: 85px; }
  .neteller-logo { width: 120px; }
  .gateway-inner-wrapper .v-text-field--outlined .v-label {
    font-size: 14px;
  }
  .filter-ipad {
    margin-left: 40px;
  }
}
@media only screen and (min-width: 960px) {
  .container {
    max-width: 100% !important;
  }
}
@media only screen and (min-width: 1024px) {
  .desktop-body-wrapper {
    margin: 0;
  }
  .body-wrapper {
    margin: 0;
  }
  .ipad-view {
    display: none !important;
  }
  .login-tab-wrapper {
    padding: 0 0;
  }
}
@media only screen and (min-width: 1030px) {
  .desktop-body-wrapper {
    margin: 0;
  }
  .body-wrapper {
    margin: 0;
  }
  .login-tab-wrapper {
    padding: 0 130px;
  }
  .withdrawal-summary-container {
    width: 46%;
  }
}
@media (min-width: 1264px) {
  .container {
    max-width: 100% !important;
  }
  .login-tab-wrapper {
    padding: 0 70px;
  }
}
@media (min-width: 1903px) {
  .login-tab-wrapper {
    padding: 0 130px;
  }
}
