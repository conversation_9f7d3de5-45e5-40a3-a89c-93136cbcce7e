{"name": "piwiaffiliate", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue src/", "lint:fix": "eslint --ext .js,.vue src/ --fix", "debug": "vite --debug"}, "dependencies": {"@date-io/date-fns": "^3.2.1", "@tailwindcss/vite": "^4.0.15", "@vee-validate/rules": "^4.15.0", "@vee-validate/yup": "^4.15.0", "@vue/compat": "^3.1.0", "@vuepic/vue-datepicker": "^11.0.2", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "decimal.js": "^10.2.0", "dotenv": "^16.4.7", "file-saver": "^2.0.5", "moment": "^2.24.0", "moment-timezone": "^0.5.27", "qs": "^6.8.0", "stylus": "^0.64.0", "stylus-loader": "^8.1.1", "tailwindcss": "^4.0.15", "vee-validate": "^4.15.0", "vite-plugin-vuetify": "^2.1.0", "vue-chartjs": "^5.3.2", "vuex-map-fields": "^1.4.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.22.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/compiler-sfc": "^3.1.0", "axios": "^1.8.4", "eslint": "^9.22.0", "eslint-plugin-vue": "^9.33.0", "eslint-plugin-vuetify": "^2.5.2", "font-awesome": "^4.7.0", "globals": "^16.0.0", "install": "^0.13.0", "npm": "^11.2.0", "sass": "^1.86.0", "sass-loader": "^16.0.5", "vite": "^6.2.2", "vue": "^3.5.13", "vue-eslint-parser": "^10.1.1", "vue-router": "^4.5.0", "vuetify": "^3.7.18", "vuex": "^4.1.0"}, "browserslist": ["> 1%", "last 2 versions"]}