node('master') {
    stage('Set agent'){
//         if (params.ENVIRONMENT != 'prod') {
            AGENT_LABEL = "piwi-ec2-fleet"
//         } else {
//             AGENT_LABEL = "master"
//         }
        print "Agent running: ${AGENT_LABEL}"
    }
}

pipeline {
    agent {
        label "${AGENT_LABEL}"
    }
    parameters {
        choice(name: 'ENVIRONMENT', choices: ['stage1', 'stage2', 'stage3', 'stage4', 'prod'], description: 'Environment to deploy to')
        string(name: 'BRANCH', defaultValue: 'master', description: 'Branch to be deployed', trim: true)
        choice(name: 'STACK', choices: ['old', 'new'], description: 'old - update image only. New - will use docker stack deploy(new service, variables update and new stack)')
    }
    environment {
        GIT_URL = "*****************:zimitech/affiliateui.git"
        ECR_REGISTRY_SPA = "025440791034.dkr.ecr.ap-northeast-1.amazonaws.com/piwi/affiliate-spa"
        AWS_REGION = "ap-northeast-1"
        AWS_S3_BUCKET = "zmt-piwi-build-artifacts"
        AWS_S3_CONF = "s3://${AWS_S3_BUCKET}/docker/affiliate-spa"
        AWS_S3_SPA_FOLDER = "s3://zmt-piwi-affiliate"
        CF_STAGE_1 = "EE71V7YVT2VKP"
        CF_STAGE_2 = "E2EL683RGF2CR2"
        CF_STAGE_3 = "E1UQ5R2N8YVDTE"
        CF_STAGE_4 = "EJ1Q25QKRNVKF"
        CF_PROD = "E3G0P3TRSR294A"

        DOCKER_BUILD_PATH = "src"
    }
    options{
        timeout(time: 1, unit: 'HOURS')
    }
    stages {
        stage("Update Job Description") {
            steps {
                script {
                    def cause = currentBuild.getBuildCauses ('hudson.model.Cause$UserIdCause')
                    def user = null
                    if (cause != null && cause.size() > 0) {
                        user = cause[0].userName
                    } else {
                        cause = currentBuild.getBuildCauses ('hudson.model.Cause$UpstreamCause')
                        if (cause != null && cause.size() > 0) {
                            def project = cause[0].upstreamProject
                            def build = cause[0].upstreamBuild
                            user = "<a href='/job/${project}/${build}/'>${project}/${build}</a>"
                        }
                    }
                    if (env.approver != null && env.approver != '') {
                        user = "${user}<br/>Approver: ${env.approver}"
                    }
                    currentBuild.description = "ENV: ${params.ENVIRONMENT}<br/>BRANCH: ${BRANCH}<br/>Run by: ${user}<br/><img src='${JENKINS_URL}/buildStatus/icon?job=${JOB_NAME}&style=flat&build=${BUILD_ID}'>"
                }
            }
        } // end update job description
        stage('Get SPA Configuration from S3') {
            steps {
                script{
                    COMMAND="aws s3 --profile piwi-prod cp --only-show-errors ${AWS_S3_CONF}/${params.ENVIRONMENT}/${params.ENVIRONMENT}.env ${WORKSPACE}/${BUILD_ID}/${params.ENVIRONMENT}.env"
                }
                sh "${COMMAND}"
                dir("${WORKSPACE}/${BUILD_ID}"){
                    stash "${JOB_NAME}-${BUILD_ID}"
                }
            }
        } // end get spa configuration
        stage('Clone, Merge and Build') {
            options {
                retry(3)
            }
            steps {
                lock(resource: "${JOB_NAME}_src") {
                    script {
                        isTag = ""
                        if (params.ENVIRONMENT != 'prod') {
                            try {
                                checkout([
                                        $class                           : 'GitSCM',
                                        branches                         : [
                                                [
                                                        name: "*/${BRANCH}"
                                                ]
                                        ],
                                        doGenerateSubmoduleConfigurations: false,
                                        extensions                       : [
                                                [
                                                        $class   : 'CloneOption',
                                                        noTags   : true,
                                                        reference: '',
                                                        shallow  : false
                                                ],
                                                [
                                                        $class           : 'RelativeTargetDirectory',
                                                        relativeTargetDir: 'src'
                                                ],
                                                [
                                                        $class : 'ChangelogToBranch',
                                                        options: [
                                                                compareRemote: 'origin',
                                                                compareTarget: 'master'
                                                        ]
                                                ],
                                                [
                                                        $class : 'PreBuildMerge',
                                                        options: [
                                                                fastForwardMode: 'NO_FF',
                                                                mergeRemote    : 'origin',
                                                                mergeTarget    : 'master'
                                                        ]
                                                ],
                                                [
                                                        $class: 'AuthorInChangelog'
                                                ],
                                                [
                                                        $class: 'CleanBeforeCheckout'
                                                ]
                                        ],
                                        submoduleCfg                     : [],
                                        userRemoteConfigs                : [
                                                [
                                                        credentialsId: 'd7cb1692-f3b6-4b88-a280-9b068c9f1088',
                                                        url          : "${GIT_URL}"
                                                ]
                                        ]
                                ])
                            } catch (Exception e) {
                                isTag = "${BRANCH}"
                                checkout([
                                    $class                           : 'GitSCM',
                                    branches                         : [[name: "${BRANCH}"]],
                                    doGenerateSubmoduleConfigurations: false,
                                    extensions                       : [
                                            [
                                                    $class   : 'CloneOption',
                                                    noTags   : false,
                                                    reference: '',
                                                    shallow  : false
                                            ],
                                            [
                                                    $class           : 'RelativeTargetDirectory',
                                                    relativeTargetDir: 'src'
                                            ],
                                            [
                                                    $class : 'ChangelogToBranch',
                                                    options: [
                                                            compareRemote: 'origin',
                                                            compareTarget: 'master'
                                                    ]
                                            ],
                                            [
                                                    $class : 'PreBuildMerge',
                                                    options: [
                                                            fastForwardMode: 'NO_FF',
                                                            mergeRemote    : 'origin',
                                                            mergeTarget    : 'master'
                                                    ]
                                            ],
                                            [
                                                    $class: 'AuthorInChangelog'
                                            ],
                                            [
                                                    $class: 'CleanBeforeCheckout'
                                            ]
                                    ],
                                    submoduleCfg                     : [],
                                    userRemoteConfigs                : [
                                            [
                                                    credentialsId: 'd7cb1692-f3b6-4b88-a280-9b068c9f1088',
                                                    url          : "${GIT_URL}"
                                            ]
                                    ]
                                ])
                            }
                        }
                    }
                    dir("${DOCKER_BUILD_PATH}"){
                        script {
                            if (params.ENVIRONMENT != 'prod') {
                                def envFile = "${WORKSPACE}/${BUILD_ID}/${params.ENVIRONMENT}.env"
                                def getEnvValue = { key ->
                                    return sh(
                                        script: "grep '^${key}=' ${envFile} | cut -d '=' -f2-",
                                        returnStdout: true
                                    ).trim()
                                }

                                def viteEnv = getEnvValue("VITE_ENV")
                                def apiUrl = getEnvValue("VITE_API_URL")
                                def boToken = getEnvValue("VITE_BO_ACCESS_TOKEN")
                                def jumioSuccess = getEnvValue("VITE_JUMIO_SUCCESS_URL")
                                def jumioError = getEnvValue("VITE_JUMIO_ERROR_URL")

                                sh """
                                    docker build --force-rm --pull --memory 4g --memory-swap -1 --shm-size 512m \\
                                    --build-arg VITE_ENV=${viteEnv} \\
                                    --build-arg VITE_API_URL=${apiUrl} \\
                                    --build-arg VITE_BO_ACCESS_TOKEN=${boToken} \\
                                    --build-arg VITE_JUMIO_SUCCESS_URL='${jumioSuccess}' \\
                                    --build-arg VITE_JUMIO_ERROR_URL='${jumioError}' \\
                                    --compress \\
                                    -t ${ECR_REGISTRY_SPA}:${BRANCH} .
                                """
                            } else {
                                sh "echo 'No docker build for prod'"
                            }
                        }
                    }
                }
                script {
                    def cause = currentBuild.getBuildCauses('hudson.model.Cause$UpstreamCause')
                    def userCause = currentBuild.getBuildCauses('hudson.model.Cause$UserIdCause')
                    def user = null
                    if(userCause != null && userCause.size() > 0){
                        user = userCause[0].userId
                    }
                    if (params.ENVIRONMENT == 'prod' && (cause == null || cause.size() == 0)) {
                        while({
                            def approver = input submitterParameter: 'approver', message: 'You are deploying to PRODUCTION, seek approval.'
                            env.approver = approver
                            env.approver == user
                        }()) continue
                    }
                }
            }
        } // end clone, merge and build
        stage('Post Build') {
            when {
                expression { params.ENVIRONMENT != 'prod' }
            }
            parallel {
                stage('Push to Amazon ECR') {
                    steps{
                        withAWS(region: "${AWS_REGION}") {
                            script{
                                def login = ecrLogin()
                                sh "${login}"
                                sh "docker push ${ECR_REGISTRY_SPA}:${BRANCH}"
                            }
                        }

                    }
                }
                stage('Check Production and Amazon ECR Push') {
                    steps{
                        withAWS(region: "${AWS_REGION}") {
                            script{
                                if (isTag.trim() != '') {
                                    def login = ecrLogin()
                                    sh "docker tag ${ECR_REGISTRY_SPA}:${BRANCH} ${ECR_REGISTRY_SPA}:latest"
                                    sh "docker push ${ECR_REGISTRY_SPA}:latest"
                                }
                            }
                        }
                    }
                }
            }
        } // end post build
        stage('Check docker tag If exists') {
            steps {
                script {
                    if (params.ENVIRONMENT == 'prod') {
                        def login = ecrLogin()
                        sh "${login}"
                        def checkTag = sh(
                                script: 'docker pull ${ECR_REGISTRY_SPA}:${BRANCH} || true',
                                returnStdout: true
                        ).trim() as Boolean
                        if (!checkTag) {
                            echo 'Build the tag before deploying on production.'
                            currentBuild.result = 'FAILED'
                            error('Aborting build')
                        }
                    }
                }
            }
        } // end check docker tag if exists
        stage('Generate assets from container'){
            steps {
                dir("${WORKSPACE}/${BUILD_ID}"){
                    unstash "${JOB_NAME}-${BUILD_ID}"
                }
                sh "mkdir -p ${WORKSPACE}/${BUILD_ID} && docker run --rm -v ${WORKSPACE}/${BUILD_ID}/dist:/dist ${ECR_REGISTRY_SPA}:${BRANCH} sh -c \"cp -r /app/dist/* /dist\""
            }
        } // end generate assets from container
        stage('Upload files to S3') {
            steps {
                script{
                		COMMAND="aws s3 --profile piwi-prod sync --only-show-errors ${WORKSPACE}/${BUILD_ID}/dist ${AWS_S3_SPA_FOLDER}/${params.ENVIRONMENT}"
                }
                sh "${COMMAND}"
            }
        } // end upload files to s3
        stage('Invalidate CloudFront') {
            steps {
                script {
                    if(params.ENVIRONMENT == 'prod'){
                        sleep time: 12, unit: 'MINUTES'
                        withAWS(region: 'ap-northeast-1') {
                            cfInvalidate(distribution: "${CF_PROD}", paths:['/*'])
                        }
                    } else if (params.ENVIRONMENT == 'stage1') {
                        sleep time: 5, unit: 'MINUTES'
                        withAWS(region: 'ap-northeast-1') {
                            cfInvalidate(distribution:  "${CF_STAGE_1}", paths:['/*'])
                        }
                    } else if (params.ENVIRONMENT == 'stage2') {
                        sleep time: 5, unit: 'MINUTES'
                        withAWS(region: 'ap-northeast-1') {
                            cfInvalidate(distribution: "${CF_STAGE_2}", paths:['/*'])
                        }
                    } else if(params.ENVIRONMENT == 'stage3') {
                        sleep time: 5, unit: 'MINUTES'
                        withAWS(region: 'ap-northeast-1') {
                            cfInvalidate(distribution: "${CF_STAGE_3}", paths:['/*'])
                        }
                    } else if(params.ENVIRONMENT == 'stage4') {
                        sleep time: 5, unit: 'MINUTES'
                        withAWS(region: 'ap-northeast-1') {
                            cfInvalidate(distribution: "${CF_STAGE_4}", paths:['/*'])
                        }
                    }
                }

            }
        } // end cfInvalidate cloudfront
    } //end stages
    post {
        always {
            script {
                def cause = currentBuild.getBuildCauses ('hudson.model.Cause$UserIdCause')
                def user = null
                if (cause != null && cause.size() > 0) {
                    user = cause[0].userName
                } else {
                    cause = currentBuild.getBuildCauses ('hudson.model.Cause$UpstreamCause')
                    if (cause != null && cause.size() > 0) {
                        def project = cause[0].upstreamProject
                        def build = cause[0].upstreamBuild
                        user = "<a href='/job/${project}/${build}/'>${project}/${build}</a>"
                    }
                }
                if (env.approver != null && env.approver != '') {
                    user = "${user}<br/>Approver: ${env.approver}"
                }
                currentBuild.description = "ENV: ${params.ENVIRONMENT}<br/>BRANCH: ${BRANCH}<br/>Run by: ${user}<br/><img src='${JENKINS_URL}/buildStatus/icon?job=${JOB_NAME}&style=flat&build=${BUILD_ID}'>"
            }
        }
        failure {
            script {
                echo "This Job has failed."
            }
        }
        success {
            script {
                echo "This Job has been successful"
            }
        }
    } // end post
}