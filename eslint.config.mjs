import { defineConfig } from "eslint/config";
import globals from "globals";
import parser from "vue-eslint-parser";
import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all
});

export default defineConfig([{
    extends: compat.extends(
        "eslint:recommended",
        "plugin:vue/vue3-recommended",
        "plugin:vue/base",
        "plugin:vuetify/base",
    ),

    languageOptions: {
        globals: {
            ...globals.node,
        },

        parser: parser,
        ecmaVersion: 2020,
        sourceType: "module",
    },

    rules: {
        "vue/no-template-shadow": "off",

        "vue/valid-v-slot": ["error", {
            allowModifiers: true,
        }],
    },
}]);