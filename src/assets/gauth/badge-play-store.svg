<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 22.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1"
	 id="PlayStore" inkscape:version="0.91 r13725" sodipodi:docname="google-play-badge.svg" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns:svg="http://www.w3.org/2000/svg"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 135.7 40"
	 style="enable-background:new 0 0 135.7 40;" xml:space="preserve">
<style type="text/css">
	.st0{fill:none;}
	.st1{clip-path:url(#SVGID_2_);}
	.st2{clip-path:url(#SVGID_4_);}
	.st3{clip-path:url(#SVGID_6_);}
	.st4{clip-path:url(#SVGID_8_);}
	.st5{fill:#FFFFFF;}
	.st6{fill:#A6A6A6;}
	.st7{fill:#FFFFFF;stroke:#FFFFFF;stroke-width:0.16;stroke-miterlimit:10;}
	.st8{fill:url(#path64_1_);}
	.st9{fill:url(#path78_1_);}
	.st10{fill:url(#path88_1_);}
	.st11{fill:url(#path104_1_);}
	.st12{filter:url(#Adobe_OpacityMaskFilter);}
	.st13{clip-path:url(#SVGID_10_);fill-opacity:0.2;}
	.st14{mask:url(#mask114_1_);}
	.st15{clip-path:url(#SVGID_12_);}
	.st16{fill:url(#path152_1_);}
	.st17{filter:url(#Adobe_OpacityMaskFilter_1_);}
	.st18{clip-path:url(#SVGID_14_);fill-opacity:0.12;}
	.st19{mask:url(#mask162_1_);}
	.st20{clip-path:url(#SVGID_16_);}
	.st21{fill:url(#path200_1_);}
	.st22{filter:url(#Adobe_OpacityMaskFilter_2_);}
	.st23{clip-path:url(#SVGID_18_);fill-opacity:0.12;}
	.st24{mask:url(#mask210_1_);}
	.st25{clip-path:url(#SVGID_20_);}
	.st26{fill:url(#path248_1_);}
	.st27{filter:url(#Adobe_OpacityMaskFilter_3_);}
	.st28{clip-path:url(#SVGID_22_);fill-opacity:0.25;}
	.st29{mask:url(#mask258_1_);}
	.st30{clip-path:url(#SVGID_24_);}
	.st31{fill:url(#path296_1_);}
</style>
<pattern  y="40" width="124" height="48" patternUnits="userSpaceOnUse" id="pattern134" viewBox="0 -48 124 48" style="overflow:visible;">
	<g>
		<rect y="-48" class="st0" width="124" height="48"/>
		<g id="g136_1_">
		</g>
		<g id="g138_1_">
			<g>
				<defs>
					<rect id="SVGID_1_" y="-48" width="124" height="48"/>
				</defs>
				<clipPath id="SVGID_2_">
					<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
				</clipPath>
				<g id="g140_1_" class="st1">
					<g id="g142_1_">
						<path id="path144_1_" d="M29.6-20.7L18-14.1c-0.6,0.4-1.2,0.3-1.6,0L16.3-14l0.1,0.1c0.4,0.3,1,0.4,1.6,0L29.6-20.7
							C29.7-20.6,29.6-20.7,29.6-20.7z"/>
					</g>
				</g>
			</g>
		</g>
	</g>
</pattern>
<pattern  y="40" width="124" height="48" patternUnits="userSpaceOnUse" id="pattern182" viewBox="0 -48 124 48" style="overflow:visible;">
	<g>
		<rect y="-48" class="st0" width="124" height="48"/>
		<g id="g184_1_">
		</g>
		<g id="g186_1_">
			<g>
				<defs>
					<rect id="SVGID_3_" y="-48" width="124" height="48"/>
				</defs>
				<clipPath id="SVGID_4_">
					<use xlink:href="#SVGID_3_"  style="overflow:visible;"/>
				</clipPath>
				<g id="g188_1_" class="st2">
					<g id="g190_1_">
						<path id="path192_1_" d="M16.3-14.1c-0.2-0.2-0.4-0.6-0.4-1.1v0.1c0,0.5,0.1,0.9,0.4,1.1V-14.1L16.3-14.1z"/>
					</g>
				</g>
			</g>
		</g>
	</g>
</pattern>
<pattern  y="40" width="124" height="48" patternUnits="userSpaceOnUse" id="pattern230" viewBox="0 -48 124 48" style="overflow:visible;">
	<g>
		<rect y="-48" class="st0" width="124" height="48"/>
		<g id="g232_1_">
		</g>
		<g id="g234_1_">
			<g>
				<defs>
					<rect id="SVGID_5_" y="-48" width="124" height="48"/>
				</defs>
				<clipPath id="SVGID_6_">
					<use xlink:href="#SVGID_5_"  style="overflow:visible;"/>
				</clipPath>
				<g id="g236_1_" class="st3">
					<g id="g238_1_">
						<path id="path240_1_" d="M33.6-23l-4,2.3l0.1,0.1l3.9-2.2c0.6-0.3,0.8-0.7,0.8-1.2C34.4-23.6,34.1-23.3,33.6-23z"/>
					</g>
				</g>
			</g>
		</g>
	</g>
</pattern>
<pattern  y="40" width="124" height="48" patternUnits="userSpaceOnUse" id="pattern278" viewBox="0 -48 124 48" style="overflow:visible;">
	<g>
		<rect y="-48" class="st0" width="124" height="48"/>
		<g id="g280_1_">
		</g>
		<g id="g282_1_">
			<g>
				<defs>
					<rect id="SVGID_7_" y="-48" width="124" height="48"/>
				</defs>
				<clipPath id="SVGID_8_">
					<use xlink:href="#SVGID_7_"  style="overflow:visible;"/>
				</clipPath>
				<g id="g284_1_" class="st4">
					<g id="g286_1_">
						<path id="path288_1_" class="st5" d="M18-33.9L33.6-25c0.5,0.3,0.8,0.7,0.8,1c0-0.4-0.3-0.8-0.8-1.2L18-34
							c-1.1-0.6-2-0.1-2,1.2v0.1C16-34,16.9-34.5,18-33.9z"/>
					</g>
				</g>
			</g>
		</g>
	</g>
</pattern>
<g id="g12" transform="matrix(1.0023923,0,0,0.99072975,-0.29664807,0)">
	<path id="path14" inkscape:connector-curvature="0" d="M130.5,40.3H5.5c-2.7,0-5-2.3-5-5v-30c0-2.7,2.3-5,5-5h125c2.7,0,5,2.3,5,5
		v30C135.5,38.1,133.3,40.3,130.5,40.3z"/>
	<path id="path16" inkscape:connector-curvature="0" class="st6" d="M130.5,1.1c2.3,0,4.2,1.9,4.2,4.2v30c0,2.3-1.9,4.2-4.2,4.2H5.5
		c-2.3,0-4.2-1.9-4.2-4.2v-30c0-2.3,1.9-4.2,4.2-4.2H130.5 M130.5,0.3H5.5c-2.7,0-5,2.3-5,5v30c0,2.7,2.3,5,5,5h125c2.7,0,5-2.3,5-5
		v-30C135.5,2.6,133.3,0.3,130.5,0.3z"/>
	<g id="g18" transform="matrix(1,0,0,-1,0,48)">
		<path id="path20" inkscape:connector-curvature="0" class="st7" d="M47.9,37.4c0-0.8-0.3-1.5-0.7-2c-0.6-0.6-1.3-0.9-2.2-0.9
			c-0.9,0-1.6,0.3-2.2,0.9c-0.6,0.6-0.9,1.3-0.9,2.2s0.3,1.6,0.9,2.2c0.6,0.6,1.3,0.9,2.2,0.9c0.4,0,0.8-0.1,1.2-0.3
			c0.4-0.2,0.7-0.4,0.9-0.7l-0.5-0.5c-0.4,0.5-0.9,0.7-1.6,0.7c-0.6,0-1.2-0.2-1.6-0.7c-0.5-0.4-0.7-1-0.7-1.7s0.2-1.3,0.7-1.7
			c0.5-0.4,1-0.7,1.6-0.7c0.7,0,1.2,0.2,1.7,0.7c0.3,0.3,0.5,0.7,0.5,1.2H45v0.7h2.9C47.9,37.7,47.9,37.6,47.9,37.4z"/>
	</g>
	<g id="g22" transform="matrix(1,0,0,-1,0,48)">
		<path id="path24" inkscape:connector-curvature="0" class="st7" d="M52.6,39.9h-2.7V38h2.5v-0.7h-2.5v-1.9h2.7v-0.7H49v6h3.5
			C52.6,40.7,52.6,39.9,52.6,39.9z"/>
	</g>
	<g id="g26" transform="matrix(1,0,0,-1,0,48)">
		<path id="path28" inkscape:connector-curvature="0" class="st7" d="M55.8,34.7H55v5.3h-1.7v0.7h4.1v-0.7h-1.7V34.7z"/>
	</g>
	<g id="g30" transform="matrix(1,0,0,-1,0,48)">
		<path id="path32" inkscape:connector-curvature="0" class="st7" d="M60.5,34.7v6h0.8v-6H60.5z"/>
	</g>
	<g id="g34" transform="matrix(1,0,0,-1,0,48)">
		<path id="path36" inkscape:connector-curvature="0" class="st7" d="M64.7,34.7h-0.8v5.3h-1.7v0.7h4.1v-0.7h-1.7
			C64.7,39.9,64.7,34.7,64.7,34.7z"/>
	</g>
	<g id="g38" transform="matrix(1,0,0,-1,0,48)">
		<path id="path40" inkscape:connector-curvature="0" class="st7" d="M74.1,35.5c-0.6-0.6-1.3-0.9-2.2-0.9c-0.9,0-1.6,0.3-2.2,0.9
			c-0.6,0.6-0.9,1.3-0.9,2.2s0.3,1.6,0.9,2.2c0.6,0.6,1.3,0.9,2.2,0.9c0.9,0,1.6-0.3,2.2-0.9c0.6-0.6,0.9-1.3,0.9-2.2
			C75,36.8,74.7,36.1,74.1,35.5z M70.3,36c0.4-0.4,1-0.7,1.6-0.7c0.6,0,1.2,0.2,1.6,0.7c0.4,0.4,0.7,1,0.7,1.7S74,39,73.6,39.4
			c-0.4,0.4-1,0.7-1.6,0.7c-0.6,0-1.2-0.2-1.6-0.7c-0.4-0.4-0.7-1-0.7-1.7S69.9,36.4,70.3,36z"/>
	</g>
	<g id="g42" transform="matrix(1,0,0,-1,0,48)">
		<path id="path44" inkscape:connector-curvature="0" class="st7" d="M76.1,34.7v6H77L80,36h0l0,1.2v3.5h0.8v-6h-0.8l-3.1,4.9h0
			l0-1.2v-3.7C76.9,34.7,76.1,34.7,76.1,34.7z"/>
	</g>
	<path id="path46" inkscape:connector-curvature="0" class="st5" d="M68.7,22.1c-2.3,0-4.3,1.8-4.3,4.3c0,2.4,1.9,4.3,4.3,4.3
		c2.4,0,4.3-1.8,4.3-4.3C72.9,23.9,71,22.1,68.7,22.1z M68.7,28.9c-1.3,0-2.4-1.1-2.4-2.6c0-1.5,1.1-2.6,2.4-2.6
		c1.3,0,2.4,1,2.4,2.6C71.1,27.8,69.9,28.9,68.7,28.9z M59.3,22.1c-2.4,0-4.3,1.8-4.3,4.3c0,2.4,1.9,4.3,4.3,4.3
		c2.4,0,4.3-1.8,4.3-4.3C63.6,23.9,61.7,22.1,59.3,22.1z M59.3,28.9c-1.3,0-2.4-1.1-2.4-2.6c0-1.5,1.1-2.6,2.4-2.6
		c1.3,0,2.4,1,2.4,2.6C61.7,27.8,60.6,28.9,59.3,28.9z M48.3,23.4v1.8h4.3c-0.1,1-0.5,1.8-1,2.3c-0.6,0.6-1.6,1.3-3.3,1.3
		c-2.7,0-4.7-2.1-4.7-4.8s2.1-4.8,4.7-4.8c1.4,0,2.5,0.6,3.3,1.3l1.3-1.3c-1.1-1-2.5-1.8-4.5-1.8c-3.6,0-6.7,3-6.7,6.6
		s3.1,6.6,6.7,6.6c2,0,3.4-0.6,4.6-1.9c1.2-1.2,1.6-2.9,1.6-4.2c0-0.4,0-0.8-0.1-1.1C54.3,23.4,48.3,23.4,48.3,23.4z M93.6,24.8
		c-0.4-1-1.4-2.7-3.6-2.7c-2.2,0-4,1.7-4,4.3c0,2.4,1.8,4.3,4.2,4.3c2,0,3.1-1.2,3.5-1.9l-1.5-1c-0.5,0.7-1.1,1.2-2.1,1.2
		c-0.9,0-1.6-0.4-2.1-1.3l5.7-2.4C93.8,25.3,93.6,24.8,93.6,24.8z M87.8,26.2c0-1.6,1.3-2.5,2.2-2.5c0.7,0,1.4,0.4,1.6,0.9
		C91.6,24.6,87.8,26.2,87.8,26.2z M83.2,30.3H85V17.8h-1.9V30.3z M80.1,23L80.1,23c-0.5-0.5-1.3-0.9-2.3-0.9c-2.1,0-4.1,1.9-4.1,4.3
		c0,2.4,1.9,4.2,4.1,4.2c1,0,1.8-0.5,2.2-1h0.1v0.6c0,1.6-0.9,2.5-2.3,2.5c-1.1,0-1.9-0.8-2.1-1.5L74,31.9c0.5,1.1,1.7,2.5,3.8,2.5
		c2.2,0,4-1.3,4-4.4v-7.6h-1.8V23z M78,28.9c-1.3,0-2.4-1.1-2.4-2.6c0-1.5,1.1-2.6,2.4-2.6c1.3,0,2.3,1.1,2.3,2.6
		C80.2,27.8,79.2,28.9,78,28.9z M102.3,17.8h-4.5v12.5h1.9v-4.7h2.6c2.1,0,4.1-1.5,4.1-3.9C106.4,19.3,104.4,17.8,102.3,17.8z
		 M102.4,23.8h-2.7v-4.3h2.7c1.4,0,2.2,1.2,2.2,2.1C104.6,22.7,103.8,23.8,102.4,23.8z M113.9,22c-1.3,0-2.7,0.6-3.3,1.9l1.7,0.7
		c0.4-0.7,1-0.9,1.7-0.9c1,0,1.9,0.6,2,1.6v0.1c-0.3-0.2-1.1-0.5-1.9-0.5c-1.8,0-3.6,1-3.6,2.8c0,1.7,1.5,2.8,3.1,2.8
		c1.3,0,1.9-0.6,2.4-1.2h0.1v1h1.8v-4.8C117.7,23.3,116.1,22,113.9,22z M113.7,28.9c-0.6,0-1.5-0.3-1.5-1.1c0-1,1.1-1.3,2-1.3
		c0.8,0,1.2,0.2,1.7,0.4C115.8,28.1,114.8,28.9,113.7,28.9z M124.3,22.3l-2.1,5.4h-0.1l-2.2-5.4h-2l3.3,7.6l-1.9,4.2h1.9l5.1-11.8
		C126.3,22.3,124.3,22.3,124.3,22.3z M107.5,30.3h1.9V17.8h-1.9V30.3z"/>
	<g id="g48">
		
			<linearGradient id="path64_1_" gradientUnits="userSpaceOnUse" x1="31.826" y1="-143.8316" x2="15.0436" y2="-127.0492" gradientTransform="matrix(1.0024 0 0 -0.9907 -9.5796 -110.8903)">
			<stop  offset="0" style="stop-color:#00A0FF"/>
			<stop  offset="6.599999e-03" style="stop-color:#00A1FF"/>
			<stop  offset="0.2601" style="stop-color:#00BEFF"/>
			<stop  offset="0.5122" style="stop-color:#00D2FF"/>
			<stop  offset="0.7604" style="stop-color:#00DFFF"/>
			<stop  offset="1" style="stop-color:#00E3FF"/>
		</linearGradient>
		<path id="path64" inkscape:connector-curvature="0" class="st8" d="M11,7.9c-0.3,0.3-0.5,0.8-0.5,1.4v22.1c0,0.6,0.2,1.1,0.5,1.4
			l0.1,0.1l12.4-12.4v-0.3L11,7.9L11,7.9z"/>
	</g>
	<g id="g66">
		
			<linearGradient id="path78_1_" gradientUnits="userSpaceOnUse" x1="43.8329" y1="-132.4347" x2="19.636" y2="-132.4347" gradientTransform="matrix(1.0024 0 0 -0.9907 -9.5796 -110.8903)">
			<stop  offset="0" style="stop-color:#FFE000"/>
			<stop  offset="0.4087" style="stop-color:#FFBD00"/>
			<stop  offset="0.7754" style="stop-color:#FFA500"/>
			<stop  offset="1" style="stop-color:#FF9C00"/>
		</linearGradient>
		<path id="path78" inkscape:connector-curvature="0" class="st9" d="M27.6,24.6l-4.1-4.1v-0.3l4.1-4.1l0.1,0.1l4.9,2.8
			c1.4,0.8,1.4,2.1,0,2.9L27.6,24.6C27.6,24.5,27.6,24.6,27.6,24.6z"/>
	</g>
	<g id="g80">
		
			<linearGradient id="path88_1_" gradientUnits="userSpaceOnUse" x1="26.5224" y1="-138.5397" x2="3.7641" y2="-115.7814" gradientTransform="matrix(1.0024 0 0 -0.9907 -9.5796 -110.8903)">
			<stop  offset="0" style="stop-color:#FF3A44"/>
			<stop  offset="1" style="stop-color:#C31162"/>
		</linearGradient>
		<path id="path88" inkscape:connector-curvature="0" class="st10" d="M27.6,24.5l-4.2-4.2L11,32.8c0.5,0.5,1.2,0.5,2.1,0.1
			L27.6,24.5"/>
	</g>
	<g id="g90">
		
			<linearGradient id="path104_1_" gradientUnits="userSpaceOnUse" x1="9.0367" y1="-144.0236" x2="19.1993" y2="-133.8611" gradientTransform="matrix(1.0024 0 0 -0.9907 -9.5796 -110.8903)">
			<stop  offset="0" style="stop-color:#32A071"/>
			<stop  offset="6.850000e-02" style="stop-color:#2DA771"/>
			<stop  offset="0.4762" style="stop-color:#15CF74"/>
			<stop  offset="0.8009" style="stop-color:#06E775"/>
			<stop  offset="1" style="stop-color:#00F076"/>
		</linearGradient>
		<path id="path104" inkscape:connector-curvature="0" class="st11" d="M27.6,16.1L13,7.8c-0.9-0.5-1.6-0.4-2.1,0.1l12.5,12.5
			L27.6,16.1z"/>
	</g>
	<g id="g106">
		<g id="g108">
		</g>
		<defs>
			<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="-9.5" y="-9.7" width="155" height="60">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="-9.5" y="-9.7" width="155" height="60" id="mask114_1_">
			<g id="g116_1_" class="st12">
				<g>
					<defs>
						<rect id="SVGID_9_" x="0.1" y="0.2" width="124" height="48"/>
					</defs>
					<clipPath id="SVGID_10_">
						<use xlink:href="#SVGID_9_"  style="overflow:visible;"/>
					</clipPath>
					<g id="g118_1_">
						<path id="path120_1_" inkscape:connector-curvature="0" class="st13" d="M0.3,0H124v48.4H0.3V0z"/>
					</g>
				</g>
			</g>
		</mask>
		<g id="g122" class="st14">
			<g id="g124">
			</g>
			<g id="g146">
				<g>
					<defs>
						<rect id="SVGID_11_" x="-9.7" y="-9.4" width="155.4" height="59.4"/>
					</defs>
					<clipPath id="SVGID_12_">
						<use xlink:href="#SVGID_11_"  style="overflow:visible;"/>
					</clipPath>
					<g id="g148" class="st15">
						<g id="g150">
							<pattern  id="path152_1_" xlink:href="#pattern134" patternTransform="matrix(1.253 0 0 -1.2384 2126.2451 -18167.3809)">
							</pattern>
							<path id="path152" inkscape:connector-curvature="0" class="st16" d="M-9.5,50.3h155v-60h-155V50.3z"/>
						</g>
					</g>
				</g>
			</g>
		</g>
	</g>
	<g id="g154">
		<g id="g156">
		</g>
		<defs>
			<filter id="Adobe_OpacityMaskFilter_1_" filterUnits="userSpaceOnUse" x="-9.5" y="-9.7" width="155" height="60">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="-9.5" y="-9.7" width="155" height="60" id="mask162_1_">
			<g id="g164_1_" class="st17">
				<g>
					<defs>
						<rect id="SVGID_13_" x="0.1" y="0.2" width="124" height="48"/>
					</defs>
					<clipPath id="SVGID_14_">
						<use xlink:href="#SVGID_13_"  style="overflow:visible;"/>
					</clipPath>
					<g id="g166_1_">
						<path id="path168_1_" inkscape:connector-curvature="0" class="st18" d="M0.3,0H124v48.4H0.3V0z"/>
					</g>
				</g>
			</g>
		</mask>
		<g id="g170" class="st19">
			<g id="g172">
			</g>
			<g id="g194">
				<g>
					<defs>
						<rect id="SVGID_15_" x="-9.7" y="-9.4" width="155.4" height="59.4"/>
					</defs>
					<clipPath id="SVGID_16_">
						<use xlink:href="#SVGID_15_"  style="overflow:visible;"/>
					</clipPath>
					<g id="g196" class="st20">
						<g id="g198">
							<pattern  id="path200_1_" xlink:href="#pattern182" patternTransform="matrix(1.253 0 0 -1.2384 2126.2451 -18167.3809)">
							</pattern>
							<path id="path200" inkscape:connector-curvature="0" class="st21" d="M-9.5,50.3h155v-60h-155V50.3z"/>
						</g>
					</g>
				</g>
			</g>
		</g>
	</g>
	<g id="g202">
		<g id="g204">
		</g>
		<defs>
			<filter id="Adobe_OpacityMaskFilter_2_" filterUnits="userSpaceOnUse" x="-9.5" y="-9.7" width="155" height="60">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="-9.5" y="-9.7" width="155" height="60" id="mask210_1_">
			<g id="g212_1_" class="st22">
				<g>
					<defs>
						<rect id="SVGID_17_" x="0.1" y="0.2" width="124" height="48"/>
					</defs>
					<clipPath id="SVGID_18_">
						<use xlink:href="#SVGID_17_"  style="overflow:visible;"/>
					</clipPath>
					<g id="g214_1_">
						<path id="path216_1_" inkscape:connector-curvature="0" class="st23" d="M0.3,0H124v48.4H0.3V0z"/>
					</g>
				</g>
			</g>
		</mask>
		<g id="g218" class="st24">
			<g id="g220">
			</g>
			<g id="g242">
				<g>
					<defs>
						<rect id="SVGID_19_" x="-9.7" y="-9.4" width="155.4" height="59.4"/>
					</defs>
					<clipPath id="SVGID_20_">
						<use xlink:href="#SVGID_19_"  style="overflow:visible;"/>
					</clipPath>
					<g id="g244" class="st25">
						<g id="g246">
							<pattern  id="path248_1_" xlink:href="#pattern230" patternTransform="matrix(1.253 0 0 -1.2384 2126.2451 -18167.3809)">
							</pattern>
							<path id="path248" inkscape:connector-curvature="0" class="st26" d="M-9.5,50.3h155v-60h-155V50.3z"/>
						</g>
					</g>
				</g>
			</g>
		</g>
	</g>
	<g id="g250">
		<g id="g252">
		</g>
		<defs>
			<filter id="Adobe_OpacityMaskFilter_3_" filterUnits="userSpaceOnUse" x="-9.5" y="-9.7" width="155" height="60">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="-9.5" y="-9.7" width="155" height="60" id="mask258_1_">
			<g id="g260_1_" class="st27">
				<g>
					<defs>
						<rect id="SVGID_21_" x="0.1" y="0.2" width="124" height="48"/>
					</defs>
					<clipPath id="SVGID_22_">
						<use xlink:href="#SVGID_21_"  style="overflow:visible;"/>
					</clipPath>
					<g id="g262_1_">
						<path id="path264_1_" inkscape:connector-curvature="0" class="st28" d="M0.3,0H124v48.4H0.3V0z"/>
					</g>
				</g>
			</g>
		</mask>
		<g id="g266" class="st29">
			<g id="g268">
			</g>
			<g id="g290">
				<g>
					<defs>
						<rect id="SVGID_23_" x="-9.7" y="-9.4" width="155.4" height="59.4"/>
					</defs>
					<clipPath id="SVGID_24_">
						<use xlink:href="#SVGID_23_"  style="overflow:visible;"/>
					</clipPath>
					<g id="g292" class="st30">
						<g id="g294">
							<pattern  id="path296_1_" xlink:href="#pattern278" patternTransform="matrix(1.253 0 0 -1.2384 2126.2451 -18167.3809)">
							</pattern>
							<path id="path296" inkscape:connector-curvature="0" class="st31" d="M-9.5,50.3h155v-60h-155V50.3z"/>
						</g>
					</g>
				</g>
			</g>
		</g>
	</g>
</g>
</svg>
