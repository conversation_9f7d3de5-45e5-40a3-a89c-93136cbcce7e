<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 201.16 218.77">
  <defs>
    <clipPath id="clippath">
      <path d="M100.98,58.73c-21.88,0-42.68-2.02-58.57-5.7-7.21-1.67-12.34-3.43-15.91-5.02-4.29-1.91-4.29-7.96,0-9.88,3.57-1.59,8.69-3.36,15.9-5.02,15.89-3.67,36.69-5.7,58.57-5.7s42.69,2.02,58.57,5.7c7.21,1.67,12.34,3.43,15.9,5.02,4.29,1.91,4.29,7.96,0,9.88-3.57,1.59-8.69,3.36-15.91,5.02-15.89,3.67-36.69,5.7-58.57,5.7Z" style="fill: none; stroke-width: 0px;"/>
    </clipPath>
    <radialGradient id="Degradado_sin_nombre_5042" data-name="Degradado sin nombre 5042" cx="-996.82" cy="-644.76" fx="-996.82" fy="-644.76" r="3.27" gradientTransform="translate(1305.04 7788.67) rotate(-4.44) scale(.56 12.09) skewX(-.09)" gradientUnits="userSpaceOnUse">
      <stop offset=".09" stop-color="#fdfc04"/>
      <stop offset=".37" stop-color="#ffa101"/>
      <stop offset=".43" stop-color="rgba(249, 151, 1, .94)" stop-opacity=".94"/>
      <stop offset=".55" stop-color="rgba(236, 126, 1, .78)" stop-opacity=".78"/>
      <stop offset=".71" stop-color="rgba(214, 87, 1, .52)" stop-opacity=".52"/>
      <stop offset=".91" stop-color="rgba(185, 32, 1, .16)" stop-opacity=".16"/>
      <stop offset="1" stop-color="#ac0801" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="Degradado_sin_nombre_5042-2" data-name="Degradado sin nombre 5042" cx="-1199.24" cy="1306.03" fx="-1199.24" fy="1306.03" r="3.27" gradientTransform="translate(10927.43 -11506.64) rotate(40.57) scale(.56 12.09) skewX(.05)" xlink:href="#Degradado_sin_nombre_5042"/>
    <radialGradient id="Degradado_sin_nombre_5042-3" data-name="Degradado sin nombre 5042" cx="2429.02" cy="2991.2" fx="2429.02" fy="2991.2" r="3.27" gradientTransform="translate(28509.47 22551.45) rotate(130.57) scale(.56 12.09) skewX(.05)" xlink:href="#Degradado_sin_nombre_5042"/>
    <radialGradient id="Degradado_sin_nombre_5041" data-name="Degradado sin nombre 5041" cx="142.56" cy="-57.71" fx="142.56" fy="-57.71" r="29.81" gradientTransform="translate(6.25 116.11) rotate(-.82)" gradientUnits="userSpaceOnUse">
      <stop offset=".09" stop-color="#fdfc04"/>
      <stop offset=".32" stop-color="#ffa101"/>
      <stop offset=".39" stop-color="rgba(249, 151, 1, .94)" stop-opacity=".94"/>
      <stop offset=".52" stop-color="rgba(236, 126, 1, .78)" stop-opacity=".78"/>
      <stop offset=".69" stop-color="rgba(214, 87, 1, .52)" stop-opacity=".52"/>
      <stop offset=".91" stop-color="rgba(185, 32, 1, .16)" stop-opacity=".16"/>
      <stop offset="1" stop-color="#ac0801" stop-opacity="0"/>
    </radialGradient>
  </defs>
  <g style="isolation: isolate;">
    <g id="Layer_1" data-name="Layer 1">
      <path d="M201.16,126.86c0,34.86-21.23,65.18-52.53,80.76-2.35,1.17-4.76,2.26-7.23,3.26-12.47,5.07-26.29,7.89-40.82,7.89-31.3,0-59.26-13.07-77.71-33.56-1.8-1.99-3.51-4.06-5.12-6.2C6.56,164.2,0,146.23,0,126.86c0-10.72,2.01-21.27,5.7-31.08,3.69-9.82,9.06-18.91,15.79-26.73l-8.21-24.1h174.81l-8.21,24.1h-.21c13.46,15.63,21.49,36.37,21.49,57.81Z" style="fill: #fbcc04; stroke-width: 0px;"/>
      <path d="M100.58,73.77c-32.19,0-93.08-5.63-93.08-26.94s60.88-26.94,93.08-26.94,93.08,5.63,93.08,26.94-60.88,26.94-93.08,26.94Z" style="fill: #fada4b; stroke-width: 0px;"/>
      <path d="M100.58,58.39c-21.88,0-42.68-2.02-58.57-5.7-7.21-1.67-12.34-3.43-15.91-5.02-4.29-1.91-4.29-7.96,0-9.88,3.57-1.59,8.69-3.36,15.9-5.02,15.89-3.67,36.69-5.7,58.57-5.7s42.69,2.02,58.57,5.7c7.21,1.67,12.34,3.43,15.9,5.02,4.29,1.91,4.29,7.96,0,9.88-3.57,1.59-8.69,3.36-15.91,5.02-15.89,3.67-36.69,5.7-58.57,5.7Z" style="fill: #539afb; stroke-width: 0px;"/>
      <g style="clip-path: url(#clippath);">
        <g>
          <g>
            <path d="M114.99,43.8c-10.86,0-19.67,4.89-19.67,10.93v6.34c0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93v-6.34c0-6.03-8.81-10.93-19.67-10.93Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M111.71,68.9c-5.24,0-10.1-1.01-14.12-2.73,3.3,3.47,9.85,5.83,17.4,5.83,10.86,0,19.67-4.89,19.67-10.93v-1.6c-3.81,5.54-12.65,9.43-22.95,9.43Z" style="fill: #f86703; stroke-width: 0px;"/>
            <g>
              <path d="M101.36,48.66v20.28c-.67-.35-1.29-.74-1.86-1.14v-19.14h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M109.07,49.93v21.57c-1.02-.18-2-.4-2.95-.67v-20.89h2.95Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M103.34,49.29v20.58c-.32-.13-.63-.26-.93-.4v-20.18h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M125.04,51.41v19.05c-.6.2-1.22.38-1.86.54v-19.6h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M131.28,49.93v17.26c-.56.47-1.18.9-1.86,1.31v-18.57h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M127.02,52.04v17.67c-.3.13-.61.26-.93.38v-18.05h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
            </g>
            <ellipse cx="114.99" cy="54.73" rx="19.67" ry="10.93" style="fill: #ffac00; stroke-width: 0px;"/>
            <path d="M130.53,48.03c-.71,5.08-9.17,9.09-19.49,9.09-6.34,0-11.97-1.51-15.54-3.86,1.29-5.35,9.52-9.47,19.49-9.47,6.32,0,11.94,1.65,15.54,4.23Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M114.99,64.52c-10.52,0-19.11-4.59-19.64-10.36-.02.19-.03.37-.03.56,0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93c0-.19,0-.38-.03-.56-.53,5.77-9.12,10.36-19.64,10.36Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M129.42,54.73c0,.54-.19,1.13-.55,1.73-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c-.36-.6-.55-1.19-.55-1.73,0-2.97,5.76-7.47,14.44-7.47s14.44,4.5,14.44,7.47Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M128.87,56.45c-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c1.63-2.73,6.78-5.74,13.89-5.74s12.26,3.02,13.89,5.74Z" style="fill: #e95500; stroke-width: 0px;"/>
            <g>
              <path d="M125.22,54.34v3.3c0,.99-.45,1.92-1.25,2.72-2.38,1.09-5.44,1.84-8.98,1.84s-6.6-.75-8.98-1.84c-.8-.8-1.25-1.73-1.25-2.72v-3.3c0-3.14,4.58-5.68,10.23-5.68s10.23,2.54,10.23,5.68Z" style="fill: #ff8200; stroke-width: 0px;"/>
              <g>
                <path d="M111.39,49.49v12.44c-.54-.08-1.06-.18-1.56-.3v-12.14h1.56Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M117.88,50.76v11.27c-.8.1-1.62.16-2.48.17v-11.44h2.48Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M113.06,50.12v12c-.27-.02-.52-.05-.78-.08v-11.92h.78Z" style="fill: #e95500; stroke-width: 0px;"/>
              </g>
              <ellipse cx="114.99" cy="54.34" rx="10.23" ry="5.68" style="fill: #ffac00; stroke-width: 0px;"/>
              <path d="M125.2,54.08c-3.56,1.88-8.59,3.05-14.16,3.05-1.84,0-3.62-.13-5.3-.36-.63-.74-.98-1.56-.98-2.42,0-3.14,4.58-5.68,10.23-5.68s9.97,2.4,10.21,5.42Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            </g>
          </g>
          <g>
            <path d="M39.36,35.15c-10.86,0-19.67,4.89-19.67,10.93v6.34c0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93v-6.34c0-6.03-8.81-10.93-19.67-10.93Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M36.09,60.24c-5.24,0-10.1-1.01-14.12-2.73,3.3,3.47,9.85,5.83,17.4,5.83,10.86,0,19.67-4.89,19.67-10.93v-1.6c-3.81,5.54-12.65,9.43-22.95,9.43Z" style="fill: #f86703; stroke-width: 0px;"/>
            <g>
              <path d="M25.73,40v20.28c-.67-.35-1.29-.74-1.86-1.14v-19.14h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M33.44,41.27v21.57c-1.02-.18-2-.4-2.95-.67v-20.89h2.95Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M27.71,40.64v20.58c-.32-.13-.63-.26-.93-.4v-20.18h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M49.42,42.75v19.05c-.6.2-1.22.38-1.86.54v-19.6h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M55.66,41.27v17.26c-.56.47-1.18.9-1.86,1.31v-18.57h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M51.4,43.39v17.67c-.3.13-.61.26-.93.38v-18.05h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
            </g>
            <ellipse cx="39.36" cy="46.07" rx="19.67" ry="10.93" style="fill: #ffac00; stroke-width: 0px;"/>
            <path d="M54.91,39.38c-.71,5.08-9.17,9.09-19.49,9.09-6.34,0-11.97-1.51-15.54-3.86,1.29-5.35,9.52-9.47,19.49-9.47,6.32,0,11.94,1.65,15.54,4.23Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M39.36,55.87c-10.52,0-19.11-4.59-19.64-10.36-.02.19-.03.37-.03.56,0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93c0-.19,0-.38-.03-.56-.53,5.77-9.12,10.36-19.64,10.36Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M53.8,46.07c0,.54-.19,1.13-.55,1.73-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c-.36-.6-.55-1.19-.55-1.73,0-2.97,5.76-7.47,14.44-7.47s14.44,4.5,14.44,7.47Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M53.25,47.8c-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c1.63-2.73,6.78-5.74,13.89-5.74s12.26,3.02,13.89,5.74Z" style="fill: #e95500; stroke-width: 0px;"/>
            <g>
              <path d="M49.59,45.68v3.3c0,.99-.45,1.92-1.25,2.72-2.38,1.09-5.44,1.84-8.98,1.84s-6.6-.75-8.98-1.84c-.8-.8-1.25-1.73-1.25-2.72v-3.3c0-3.14,4.58-5.68,10.23-5.68s10.23,2.54,10.23,5.68Z" style="fill: #ff8200; stroke-width: 0px;"/>
              <g>
                <path d="M35.77,40.84v12.44c-.54-.08-1.06-.18-1.56-.3v-12.14h1.56Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M42.25,42.1v11.27c-.8.1-1.62.16-2.48.17v-11.44h2.48Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M37.43,41.47v12c-.27-.02-.52-.05-.78-.08v-11.92h.78Z" style="fill: #e95500; stroke-width: 0px;"/>
              </g>
              <ellipse cx="39.36" cy="45.68" rx="10.23" ry="5.68" style="fill: #ffac00; stroke-width: 0px;"/>
              <path d="M49.58,45.42c-3.56,1.88-8.59,3.05-14.16,3.05-1.84,0-3.62-.13-5.3-.36-.63-.74-.98-1.56-.98-2.42,0-3.14,4.58-5.68,10.23-5.68s9.97,2.4,10.21,5.42Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            </g>
          </g>
          <g>
            <path d="M77.05,42.9c-10.86,0-19.67,4.89-19.67,10.93v6.34c0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93v-6.34c0-6.03-8.81-10.93-19.67-10.93Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M73.77,67.99c-5.24,0-10.1-1.01-14.12-2.73,3.3,3.47,9.85,5.83,17.4,5.83,10.86,0,19.67-4.89,19.67-10.93v-1.6c-3.81,5.54-12.65,9.43-22.95,9.43Z" style="fill: #f86703; stroke-width: 0px;"/>
            <g>
              <path d="M63.42,47.76v20.28c-.67-.35-1.29-.74-1.86-1.14v-19.14h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M71.13,49.03v21.57c-1.02-.18-2-.4-2.95-.67v-20.89h2.95Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M65.4,48.39v20.58c-.32-.13-.63-.26-.93-.4v-20.18h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M87.11,50.51v19.05c-.6.2-1.22.38-1.86.54v-19.6h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M93.35,49.03v17.26c-.56.47-1.18.9-1.86,1.31v-18.57h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M89.08,51.14v17.67c-.3.13-.61.26-.93.38v-18.05h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
            </g>
            <ellipse cx="77.05" cy="53.83" rx="19.67" ry="10.93" style="fill: #ffac00; stroke-width: 0px;"/>
            <path d="M92.59,47.13c-.71,5.08-9.17,9.09-19.49,9.09-6.34,0-11.97-1.51-15.54-3.86,1.29-5.35,9.52-9.47,19.49-9.47,6.32,0,11.94,1.65,15.54,4.23Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M77.05,63.62c-10.52,0-19.11-4.59-19.64-10.36-.02.19-.03.37-.03.56,0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93c0-.19,0-.38-.03-.56-.53,5.77-9.12,10.36-19.64,10.36Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M91.49,53.83c0,.54-.19,1.12-.55,1.73-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c-.36-.6-.55-1.19-.55-1.73,0-2.97,5.76-7.47,14.44-7.47s14.44,4.5,14.44,7.47Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M90.94,55.55c-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c1.63-2.73,6.78-5.74,13.89-5.74s12.26,3.02,13.89,5.74Z" style="fill: #e95500; stroke-width: 0px;"/>
            <g>
              <path d="M87.28,53.44v3.3c0,.99-.45,1.92-1.25,2.72-2.38,1.09-5.44,1.84-8.98,1.84s-6.6-.75-8.98-1.84c-.8-.8-1.25-1.73-1.25-2.72v-3.3c0-3.14,4.58-5.68,10.23-5.68s10.23,2.54,10.23,5.68Z" style="fill: #ff8200; stroke-width: 0px;"/>
              <g>
                <path d="M73.46,48.59v12.44c-.54-.08-1.06-.18-1.56-.3v-12.14h1.56Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M79.94,49.86v11.27c-.8.1-1.62.16-2.48.17v-11.44h2.48Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M75.12,49.22v12c-.27-.02-.52-.05-.78-.08v-11.92h.78Z" style="fill: #e95500; stroke-width: 0px;"/>
              </g>
              <ellipse cx="77.05" cy="53.44" rx="10.23" ry="5.68" style="fill: #ffac00; stroke-width: 0px;"/>
              <path d="M87.27,53.18c-3.56,1.88-8.59,3.05-14.16,3.05-1.84,0-3.62-.13-5.3-.36-.63-.74-.98-1.56-.98-2.42,0-3.14,4.58-5.68,10.23-5.68s9.97,2.4,10.21,5.42Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            </g>
          </g>
          <g>
            <path d="M152.44,42.39c-10.86,0-19.67,4.89-19.67,10.93v6.34c0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93v-6.34c0-6.03-8.81-10.93-19.67-10.93Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M149.16,67.49c-5.24,0-10.1-1.01-14.12-2.73,3.3,3.47,9.85,5.83,17.4,5.83,10.86,0,19.67-4.89,19.67-10.93v-1.6c-3.81,5.54-12.65,9.43-22.95,9.43Z" style="fill: #f86703; stroke-width: 0px;"/>
            <g>
              <path d="M138.81,47.25v20.28c-.67-.35-1.29-.74-1.86-1.14v-19.14h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M146.52,48.52v21.57c-1.02-.18-2-.4-2.95-.67v-20.89h2.95Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M140.79,47.88v20.58c-.32-.13-.63-.26-.93-.4v-20.18h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M162.49,50v19.05c-.6.2-1.22.38-1.86.54v-19.6h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M168.74,48.52v17.26c-.56.47-1.18.9-1.86,1.31v-18.57h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M164.47,50.63v17.67c-.3.13-.61.26-.93.38v-18.05h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
            </g>
            <ellipse cx="152.44" cy="53.32" rx="19.67" ry="10.93" style="fill: #ffac00; stroke-width: 0px;"/>
            <path d="M167.98,46.62c-.71,5.08-9.17,9.09-19.49,9.09-6.34,0-11.97-1.51-15.54-3.86,1.29-5.35,9.52-9.47,19.49-9.47,6.32,0,11.94,1.65,15.54,4.23Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M152.44,63.12c-10.52,0-19.11-4.59-19.64-10.36-.02.19-.03.37-.03.56,0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93c0-.19,0-.38-.03-.56-.53,5.77-9.12,10.36-19.64,10.36Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M166.88,53.32c0,.54-.19,1.13-.55,1.73-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c-.36-.6-.55-1.19-.55-1.73,0-2.97,5.76-7.47,14.44-7.47s14.44,4.5,14.44,7.47Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M166.33,55.04c-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c1.63-2.73,6.78-5.74,13.89-5.74s12.26,3.02,13.89,5.74Z" style="fill: #e95500; stroke-width: 0px;"/>
            <g>
              <path d="M162.67,52.93v3.3c0,.99-.45,1.92-1.25,2.72-2.38,1.09-5.44,1.84-8.98,1.84s-6.6-.75-8.98-1.84c-.8-.8-1.25-1.73-1.25-2.72v-3.3c0-3.14,4.58-5.68,10.23-5.68s10.23,2.54,10.23,5.68Z" style="fill: #ff8200; stroke-width: 0px;"/>
              <g>
                <path d="M148.85,48.08v12.44c-.54-.08-1.06-.18-1.56-.3v-12.14h1.56Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M155.33,49.35v11.27c-.8.1-1.62.16-2.48.17v-11.44h2.48Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M150.51,48.71v12c-.27-.02-.52-.05-.78-.08v-11.92h.78Z" style="fill: #e95500; stroke-width: 0px;"/>
              </g>
              <ellipse cx="152.44" cy="52.93" rx="10.23" ry="5.68" style="fill: #ffac00; stroke-width: 0px;"/>
              <path d="M162.65,52.67c-3.56,1.88-8.59,3.05-14.16,3.05-1.84,0-3.62-.13-5.3-.36-.63-.74-.98-1.56-.98-2.42,0-3.14,4.58-5.68,10.23-5.68s9.97,2.4,10.21,5.42Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            </g>
          </g>
          <g>
            <path d="M156.62,29.29c-10.86,0-19.67,4.89-19.67,10.93v6.34c0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93v-6.34c0-6.03-8.81-10.93-19.67-10.93Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M153.34,54.38c-5.24,0-10.1-1.01-14.12-2.73,3.3,3.47,9.85,5.83,17.4,5.83,10.86,0,19.67-4.89,19.67-10.93v-1.6c-3.81,5.54-12.65,9.43-22.95,9.43Z" style="fill: #f86703; stroke-width: 0px;"/>
            <g>
              <path d="M142.98,34.14v20.28c-.67-.35-1.29-.74-1.86-1.14v-19.14h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M150.69,35.41v21.57c-1.02-.18-2-.4-2.95-.67v-20.89h2.95Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M144.96,34.78v20.58c-.32-.13-.63-.26-.93-.4v-20.18h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M166.67,36.89v19.05c-.6.2-1.22.38-1.86.54v-19.6h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M172.91,35.41v17.26c-.56.47-1.18.9-1.86,1.31v-18.57h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M168.65,37.53v17.67c-.3.13-.61.26-.93.38v-18.05h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
            </g>
            <ellipse cx="156.62" cy="40.21" rx="19.67" ry="10.93" style="fill: #ffac00; stroke-width: 0px;"/>
            <path d="M172.16,33.52c-.71,5.08-9.17,9.09-19.49,9.09-6.34,0-11.97-1.51-15.54-3.86,1.29-5.35,9.52-9.47,19.49-9.47,6.32,0,11.94,1.65,15.54,4.23Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M156.62,50.01c-10.52,0-19.11-4.59-19.64-10.36-.02.19-.03.37-.03.56,0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93c0-.19,0-.38-.03-.56-.53,5.77-9.12,10.36-19.64,10.36Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M171.05,40.21c0,.54-.19,1.13-.55,1.73-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c-.36-.6-.55-1.19-.55-1.73,0-2.97,5.76-7.47,14.44-7.47s14.44,4.5,14.44,7.47Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M170.5,41.94c-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c1.63-2.73,6.78-5.74,13.89-5.74s12.26,3.02,13.89,5.74Z" style="fill: #e95500; stroke-width: 0px;"/>
            <g>
              <path d="M166.84,39.82v3.3c0,.99-.45,1.92-1.25,2.72-2.38,1.09-5.44,1.84-8.98,1.84s-6.6-.75-8.98-1.84c-.8-.8-1.25-1.73-1.25-2.72v-3.3c0-3.14,4.58-5.68,10.23-5.68s10.23,2.54,10.23,5.68Z" style="fill: #ff8200; stroke-width: 0px;"/>
              <g>
                <path d="M153.02,34.98v12.44c-.54-.08-1.06-.18-1.56-.3v-12.14h1.56Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M159.5,36.25v11.27c-.8.1-1.62.16-2.48.17v-11.44h2.48Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M154.68,35.61v12c-.27-.02-.52-.05-.78-.08v-11.92h.78Z" style="fill: #e95500; stroke-width: 0px;"/>
              </g>
              <ellipse cx="156.61" cy="39.83" rx="10.23" ry="5.68" style="fill: #ffac00; stroke-width: 0px;"/>
              <path d="M166.83,39.57c-3.56,1.88-8.59,3.05-14.16,3.05-1.84,0-3.62-.13-5.3-.36-.63-.74-.98-1.56-.98-2.42,0-3.14,4.58-5.68,10.23-5.68s9.97,2.4,10.21,5.42Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            </g>
          </g>
          <g>
            <path d="M125.04,31.3c-10.86,0-19.67,4.89-19.67,10.93v6.34c0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93v-6.34c0-6.03-8.81-10.93-19.67-10.93Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M121.76,56.39c-5.24,0-10.1-1.01-14.12-2.73,3.3,3.47,9.85,5.83,17.4,5.83,10.86,0,19.67-4.89,19.67-10.93v-1.6c-3.81,5.54-12.65,9.43-22.95,9.43Z" style="fill: #f86703; stroke-width: 0px;"/>
            <g>
              <path d="M111.41,36.15v20.28c-.67-.35-1.29-.74-1.86-1.14v-19.14h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M119.12,37.42v21.57c-1.02-.18-2-.4-2.95-.67v-20.89h2.95Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M113.39,36.79v20.58c-.32-.13-.63-.26-.93-.4v-20.18h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M135.1,38.9v19.05c-.6.2-1.22.38-1.86.54v-19.6h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M141.34,37.42v17.26c-.56.47-1.18.9-1.86,1.31v-18.57h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M137.07,39.54v17.67c-.3.13-.61.26-.93.38v-18.05h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
            </g>
            <ellipse cx="125.04" cy="42.22" rx="19.67" ry="10.93" style="fill: #ffac00; stroke-width: 0px;"/>
            <path d="M140.59,35.53c-.71,5.08-9.17,9.09-19.49,9.09-6.34,0-11.97-1.51-15.54-3.86,1.29-5.35,9.52-9.47,19.49-9.47,6.32,0,11.94,1.65,15.54,4.23Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M125.04,52.02c-10.52,0-19.11-4.59-19.64-10.36-.02.19-.03.37-.03.56,0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93c0-.19,0-.38-.03-.56-.53,5.77-9.12,10.36-19.64,10.36Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M139.48,42.22c0,.54-.19,1.12-.55,1.73-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c-.36-.6-.55-1.19-.55-1.73,0-2.97,5.76-7.47,14.44-7.47s14.44,4.5,14.44,7.47Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M138.93,43.95c-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c1.63-2.73,6.78-5.74,13.89-5.74s12.26,3.02,13.89,5.74Z" style="fill: #e95500; stroke-width: 0px;"/>
            <g>
              <path d="M135.27,41.83v3.3c0,.99-.45,1.92-1.25,2.72-2.38,1.09-5.44,1.84-8.98,1.84s-6.6-.75-8.98-1.84c-.8-.8-1.25-1.73-1.25-2.72v-3.3c0-3.14,4.58-5.68,10.23-5.68s10.23,2.54,10.23,5.68Z" style="fill: #ff8200; stroke-width: 0px;"/>
              <g>
                <path d="M121.45,36.99v12.44c-.54-.08-1.06-.18-1.56-.3v-12.14h1.56Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M127.93,38.26v11.27c-.8.1-1.62.16-2.48.17v-11.44h2.48Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M123.11,37.62v12c-.27-.02-.52-.05-.78-.08v-11.92h.78Z" style="fill: #e95500; stroke-width: 0px;"/>
              </g>
              <ellipse cx="125.04" cy="41.83" rx="10.23" ry="5.68" style="fill: #ffac00; stroke-width: 0px;"/>
              <path d="M135.26,41.58c-3.56,1.88-8.59,3.05-14.16,3.05-1.84,0-3.62-.13-5.3-.36-.63-.74-.98-1.56-.98-2.42,0-3.14,4.58-5.68,10.23-5.68s9.97,2.4,10.21,5.42Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            </g>
          </g>
          <g>
            <path d="M97.99,27.37c-10.86,0-19.67,4.89-19.67,10.93v6.34c0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93v-6.34c0-6.03-8.81-10.93-19.67-10.93Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M94.71,52.47c-5.24,0-10.1-1.01-14.12-2.73,3.3,3.47,9.85,5.83,17.4,5.83,10.86,0,19.67-4.89,19.67-10.93v-1.6c-3.81,5.54-12.65,9.43-22.95,9.43Z" style="fill: #f86703; stroke-width: 0px;"/>
            <g>
              <path d="M84.36,32.23v20.28c-.67-.35-1.29-.74-1.86-1.14v-19.14h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M92.07,33.5v21.57c-1.02-.18-2-.4-2.95-.67v-20.89h2.95Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M86.34,32.86v20.58c-.32-.13-.63-.26-.93-.4v-20.18h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M108.04,34.98v19.05c-.6.2-1.22.38-1.86.54v-19.6h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M114.29,33.5v17.26c-.56.47-1.18.9-1.86,1.31v-18.57h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M110.02,35.61v17.67c-.3.13-.61.26-.93.38v-18.05h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
            </g>
            <ellipse cx="97.99" cy="38.3" rx="19.67" ry="10.93" style="fill: #ffac00; stroke-width: 0px;"/>
            <path d="M113.53,31.6c-.71,5.08-9.17,9.09-19.49,9.09-6.34,0-11.97-1.51-15.54-3.86,1.29-5.35,9.52-9.47,19.49-9.47,6.32,0,11.94,1.65,15.54,4.23Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M97.99,48.1c-10.52,0-19.11-4.59-19.64-10.36-.02.19-.03.37-.03.56,0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93c0-.19,0-.38-.03-.56-.53,5.77-9.12,10.36-19.64,10.36Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M112.43,38.3c0,.54-.19,1.13-.55,1.73-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c-.36-.6-.55-1.19-.55-1.73,0-2.97,5.76-7.47,14.44-7.47s14.44,4.5,14.44,7.47Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M111.88,40.02c-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c1.63-2.73,6.78-5.74,13.89-5.74s12.26,3.02,13.89,5.74Z" style="fill: #e95500; stroke-width: 0px;"/>
            <g>
              <path d="M108.22,37.91v3.3c0,.99-.45,1.92-1.25,2.72-2.38,1.09-5.43,1.84-8.98,1.84s-6.6-.75-8.98-1.84c-.8-.8-1.25-1.73-1.25-2.72v-3.3c0-3.14,4.58-5.68,10.23-5.68s10.23,2.54,10.23,5.68Z" style="fill: #ff8200; stroke-width: 0px;"/>
              <g>
                <path d="M94.39,33.06v12.44c-.54-.08-1.06-.18-1.56-.3v-12.14h1.56Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M100.88,34.33v11.27c-.8.1-1.62.16-2.48.17v-11.44h2.48Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M96.06,33.7v12c-.27-.02-.52-.05-.78-.08v-11.92h.78Z" style="fill: #e95500; stroke-width: 0px;"/>
              </g>
              <ellipse cx="97.99" cy="37.91" rx="10.23" ry="5.68" style="fill: #ffac00; stroke-width: 0px;"/>
              <path d="M108.2,37.65c-3.56,1.88-8.59,3.05-14.16,3.05-1.84,0-3.62-.13-5.3-.36-.63-.74-.98-1.56-.98-2.42,0-3.14,4.58-5.68,10.23-5.68s9.97,2.4,10.21,5.42Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            </g>
          </g>
          <g>
            <path d="M77.85,15.19c-10.86,0-19.67,4.89-19.67,10.93v6.34c0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93v-6.34c0-6.03-8.81-10.93-19.67-10.93Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M74.58,40.28c-5.24,0-10.1-1.01-14.12-2.73,3.3,3.47,9.85,5.83,17.4,5.83,10.86,0,19.67-4.89,19.67-10.93v-1.6c-3.81,5.54-12.65,9.43-22.95,9.43Z" style="fill: #f86703; stroke-width: 0px;"/>
            <g>
              <path d="M64.22,20.05v20.28c-.67-.35-1.29-.74-1.86-1.14v-19.14h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M71.93,21.31v21.57c-1.02-.18-2-.4-2.95-.67v-20.89h2.95Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M66.2,20.68v20.58c-.32-.13-.63-.26-.93-.4v-20.18h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M87.91,22.79v19.05c-.6.2-1.22.38-1.86.54v-19.6h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M94.15,21.31v17.26c-.56.47-1.18.9-1.86,1.31v-18.57h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M89.89,23.43v17.67c-.3.13-.61.26-.93.38v-18.05h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
            </g>
            <ellipse cx="77.85" cy="26.12" rx="19.67" ry="10.93" style="fill: #ffac00; stroke-width: 0px;"/>
            <path d="M93.4,19.42c-.71,5.08-9.17,9.09-19.49,9.09-6.34,0-11.97-1.51-15.54-3.86,1.29-5.35,9.52-9.47,19.49-9.47,6.32,0,11.94,1.65,15.54,4.23Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M77.85,35.91c-10.52,0-19.11-4.59-19.64-10.36-.02.19-.03.37-.03.56,0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93c0-.19,0-.38-.03-.56-.53,5.77-9.12,10.36-19.64,10.36Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M92.29,26.12c0,.54-.19,1.13-.55,1.73-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c-.36-.6-.55-1.19-.55-1.73,0-2.97,5.76-7.47,14.44-7.47s14.44,4.5,14.44,7.47Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M91.74,27.84c-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c1.63-2.73,6.78-5.74,13.89-5.74s12.26,3.02,13.89,5.74Z" style="fill: #e95500; stroke-width: 0px;"/>
            <g>
              <path d="M88.08,25.73v3.3c0,.99-.45,1.92-1.25,2.72-2.38,1.09-5.44,1.84-8.98,1.84s-6.6-.75-8.98-1.84c-.8-.8-1.25-1.73-1.25-2.72v-3.3c0-3.14,4.58-5.68,10.23-5.68s10.23,2.54,10.23,5.68Z" style="fill: #ff8200; stroke-width: 0px;"/>
              <g>
                <path d="M74.26,20.88v12.44c-.54-.08-1.06-.18-1.56-.3v-12.14h1.56Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M80.74,22.15v11.27c-.8.1-1.62.16-2.48.17v-11.44h2.48Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M75.92,21.51v12c-.27-.02-.52-.05-.78-.08v-11.92h.78Z" style="fill: #e95500; stroke-width: 0px;"/>
              </g>
              <ellipse cx="77.85" cy="25.73" rx="10.23" ry="5.68" style="fill: #ffac00; stroke-width: 0px;"/>
              <path d="M88.07,25.47c-3.56,1.88-8.59,3.05-14.16,3.05-1.84,0-3.62-.13-5.3-.36-.63-.74-.98-1.56-.98-2.42,0-3.14,4.58-5.68,10.23-5.68s9.97,2.4,10.21,5.42Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            </g>
          </g>
          <g>
            <path d="M117.88,14.41c-10.86,0-19.67,4.89-19.67,10.93v6.34c0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93v-6.34c0-6.03-8.81-10.93-19.67-10.93Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M114.6,39.51c-5.24,0-10.1-1.01-14.12-2.73,3.3,3.47,9.85,5.83,17.4,5.83,10.86,0,19.67-4.89,19.67-10.93v-1.6c-3.81,5.54-12.65,9.43-22.95,9.43Z" style="fill: #f86703; stroke-width: 0px;"/>
            <g>
              <path d="M104.25,19.27v20.28c-.67-.35-1.29-.74-1.86-1.14v-19.14h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M111.96,20.54v21.57c-1.02-.18-2-.4-2.95-.67v-20.89h2.95Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M106.22,19.91v20.58c-.32-.13-.63-.26-.93-.4v-20.18h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M127.93,22.02v19.05c-.6.2-1.22.38-1.86.54v-19.6h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M134.17,20.54v17.26c-.56.47-1.18.9-1.86,1.31v-18.57h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M129.91,22.65v17.67c-.3.13-.61.26-.93.38v-18.05h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
            </g>
            <ellipse cx="117.88" cy="25.34" rx="19.67" ry="10.93" style="fill: #ffac00; stroke-width: 0px;"/>
            <path d="M133.42,18.65c-.71,5.08-9.17,9.09-19.49,9.09-6.34,0-11.97-1.51-15.54-3.86,1.29-5.35,9.52-9.47,19.49-9.47,6.32,0,11.94,1.65,15.54,4.23Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M117.88,35.14c-10.52,0-19.11-4.59-19.64-10.36-.02.19-.03.37-.03.56,0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93c0-.19,0-.38-.03-.56-.53,5.77-9.12,10.36-19.64,10.36Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M132.31,25.34c0,.54-.19,1.13-.55,1.73-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c-.36-.6-.55-1.19-.55-1.73,0-2.97,5.76-7.47,14.44-7.47s14.44,4.5,14.44,7.47Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M131.76,27.07c-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c1.63-2.73,6.78-5.74,13.89-5.74s12.26,3.02,13.89,5.74Z" style="fill: #e95500; stroke-width: 0px;"/>
            <g>
              <path d="M128.1,24.95v3.3c0,.99-.45,1.92-1.25,2.72-2.38,1.09-5.44,1.84-8.98,1.84s-6.6-.75-8.98-1.84c-.8-.8-1.25-1.73-1.25-2.72v-3.3c0-3.14,4.58-5.68,10.23-5.68s10.23,2.54,10.23,5.68Z" style="fill: #ff8200; stroke-width: 0px;"/>
              <g>
                <path d="M114.28,20.1v12.44c-.54-.08-1.06-.18-1.56-.3v-12.14h1.56Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M120.77,21.37v11.27c-.8.1-1.62.16-2.48.17v-11.44h2.48Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M115.94,20.74v12c-.27-.02-.52-.05-.78-.08v-11.92h.78Z" style="fill: #e95500; stroke-width: 0px;"/>
              </g>
              <ellipse cx="117.88" cy="24.95" rx="10.23" ry="5.68" style="fill: #ffac00; stroke-width: 0px;"/>
              <path d="M128.09,24.69c-3.56,1.88-8.59,3.05-14.16,3.05-1.84,0-3.62-.13-5.3-.36-.63-.74-.98-1.56-.98-2.42,0-3.14,4.58-5.68,10.23-5.68s9.97,2.4,10.21,5.42Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            </g>
          </g>
          <g>
            <path d="M101.36.53c-10.86,0-19.67,4.89-19.67,10.93v6.34c0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93v-6.34c0-6.03-8.81-10.93-19.67-10.93Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M98.08,25.63c-5.24,0-10.1-1.01-14.12-2.73,3.3,3.47,9.85,5.83,17.4,5.83,10.86,0,19.67-4.89,19.67-10.93v-1.6c-3.81,5.54-12.65,9.43-22.95,9.43Z" style="fill: #f86703; stroke-width: 0px;"/>
            <g>
              <path d="M87.72,5.39v20.28c-.67-.35-1.29-.74-1.86-1.14V5.39h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M95.43,6.66v21.57c-1.02-.18-2-.4-2.95-.67V6.66h2.95Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M89.7,6.02v20.58c-.32-.13-.63-.26-.93-.4V6.02h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M111.41,8.14v19.05c-.6.2-1.22.38-1.86.54V8.14h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M117.65,6.66v17.26c-.56.47-1.18.9-1.86,1.31V6.66h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M113.39,8.77v17.67c-.3.13-.61.26-.93.38V8.77h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
            </g>
            <ellipse cx="101.36" cy="11.46" rx="19.67" ry="10.93" style="fill: #ffac00; stroke-width: 0px;"/>
            <path d="M116.9,4.76c-.71,5.08-9.17,9.09-19.49,9.09-6.34,0-11.97-1.51-15.54-3.86,1.29-5.35,9.52-9.47,19.49-9.47,6.32,0,11.94,1.65,15.54,4.23Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M101.36,21.26c-10.52,0-19.11-4.59-19.64-10.36-.02.19-.03.37-.03.56,0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93c0-.19,0-.38-.03-.56-.53,5.77-9.12,10.36-19.64,10.36Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M115.79,11.46c0,.54-.19,1.13-.55,1.73-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c-.36-.6-.55-1.19-.55-1.73,0-2.97,5.76-7.47,14.44-7.47s14.44,4.5,14.44,7.47Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M115.24,13.18c-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c1.63-2.73,6.78-5.74,13.89-5.74s12.26,3.02,13.89,5.74Z" style="fill: #e95500; stroke-width: 0px;"/>
            <g>
              <path d="M111.58,11.07v3.3c0,.99-.45,1.92-1.25,2.72-2.38,1.09-5.44,1.84-8.98,1.84s-6.6-.75-8.98-1.84c-.8-.8-1.25-1.73-1.25-2.72v-3.3c0-3.14,4.58-5.68,10.23-5.68s10.23,2.54,10.23,5.68Z" style="fill: #ff8200; stroke-width: 0px;"/>
              <g>
                <path d="M97.76,6.22v12.44c-.54-.08-1.06-.18-1.56-.3V6.22h1.56Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M104.25,7.49v11.27c-.8.1-1.62.16-2.48.17V7.49h2.48Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M99.42,6.86v12c-.27-.02-.52-.05-.78-.08V6.86h.78Z" style="fill: #e95500; stroke-width: 0px;"/>
              </g>
              <ellipse cx="101.36" cy="11.07" rx="10.23" ry="5.68" style="fill: #ffac00; stroke-width: 0px;"/>
              <path d="M111.57,10.81c-3.56,1.88-8.59,3.05-14.16,3.05-1.84,0-3.62-.13-5.3-.36-.63-.74-.98-1.56-.98-2.42,0-3.14,4.58-5.68,10.23-5.68s9.97,2.4,10.21,5.42Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            </g>
          </g>
          <g>
            <path d="M62.49,29.29c-10.86,0-19.67,4.89-19.67,10.93v6.34c0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93v-6.34c0-6.03-8.81-10.93-19.67-10.93Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M59.21,54.38c-5.24,0-10.1-1.01-14.12-2.73,3.3,3.47,9.85,5.83,17.4,5.83,10.86,0,19.67-4.89,19.67-10.93v-1.6c-3.81,5.54-12.65,9.43-22.95,9.43Z" style="fill: #f86703; stroke-width: 0px;"/>
            <g>
              <path d="M48.86,34.14v20.28c-.67-.35-1.29-.74-1.86-1.14v-19.14h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M56.57,35.41v21.57c-1.02-.18-2-.4-2.95-.67v-20.89h2.95Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M50.84,34.78v20.58c-.32-.13-.63-.26-.93-.4v-20.18h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M72.54,36.89v19.05c-.6.2-1.22.38-1.86.54v-19.6h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M78.79,35.41v17.26c-.56.47-1.18.9-1.86,1.31v-18.57h1.86Z" style="fill: #e95500; stroke-width: 0px;"/>
              <path d="M74.52,37.53v17.67c-.3.13-.61.26-.93.38v-18.05h.93Z" style="fill: #e95500; stroke-width: 0px;"/>
            </g>
            <ellipse cx="62.49" cy="40.21" rx="19.67" ry="10.93" style="fill: #ffac00; stroke-width: 0px;"/>
            <path d="M78.03,33.52c-.71,5.08-9.17,9.09-19.49,9.09-6.34,0-11.97-1.51-15.54-3.86,1.29-5.35,9.52-9.47,19.49-9.47,6.32,0,11.94,1.65,15.54,4.23Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M62.49,50.01c-10.52,0-19.11-4.59-19.64-10.36-.02.19-.03.37-.03.56,0,6.03,8.81,10.93,19.67,10.93s19.67-4.89,19.67-10.93c0-.19,0-.38-.03-.56-.53,5.77-9.12,10.36-19.64,10.36Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            <path d="M76.92,40.21c0,.54-.19,1.13-.55,1.73-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c-.36-.6-.55-1.19-.55-1.73,0-2.97,5.76-7.47,14.44-7.47s14.44,4.5,14.44,7.47Z" style="fill: #ff8200; stroke-width: 0px;"/>
            <path d="M76.37,41.94c-1.62,2.73-6.78,5.75-13.89,5.75s-12.26-3.02-13.89-5.75c1.63-2.73,6.78-5.74,13.89-5.74s12.26,3.02,13.89,5.74Z" style="fill: #e95500; stroke-width: 0px;"/>
            <g>
              <path d="M72.72,39.82v3.3c0,.99-.45,1.92-1.25,2.72-2.38,1.09-5.44,1.84-8.98,1.84s-6.6-.75-8.98-1.84c-.8-.8-1.25-1.73-1.25-2.72v-3.3c0-3.14,4.58-5.68,10.23-5.68s10.23,2.54,10.23,5.68Z" style="fill: #ff8200; stroke-width: 0px;"/>
              <g>
                <path d="M58.89,34.98v12.44c-.54-.08-1.06-.18-1.56-.3v-12.14h1.56Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M65.38,36.25v11.27c-.8.1-1.62.16-2.48.17v-11.44h2.48Z" style="fill: #e95500; stroke-width: 0px;"/>
                <path d="M60.56,35.61v12c-.27-.02-.52-.05-.78-.08v-11.92h.78Z" style="fill: #e95500; stroke-width: 0px;"/>
              </g>
              <ellipse cx="62.49" cy="39.83" rx="10.23" ry="5.68" style="fill: #ffac00; stroke-width: 0px;"/>
              <path d="M72.7,39.57c-3.56,1.88-8.59,3.05-14.16,3.05-1.84,0-3.62-.13-5.3-.36-.63-.74-.98-1.56-.98-2.42,0-3.14,4.58-5.68,10.23-5.68s9.97,2.4,10.21,5.42Z" style="fill: #ffcd00; stroke-width: 0px;"/>
            </g>
          </g>
        </g>
      </g>
      <g>
        <path d="M45.06,24.07c10.84.67,19.33,6.09,18.96,12.11l-.03.53-.34,5.49-.02.32c-.37,6.02-9.46,10.37-20.3,9.7s-19.33-6.09-18.96-12.11l.02-.32.34-5.49.03-.53c.37-6.02,9.46-10.37,20.3-9.7Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M46.79,49.32c5.23.32,10.15-.39,14.26-1.86-3.5,3.26-10.19,5.22-17.72,4.75-10.84-.67-19.33-6.09-18.96-12.11l.02-.32.08-1.28c3.47,5.77,12.05,10.18,22.32,10.81Z" style="fill: #f86703; stroke-width: 0px;"/>
        <g>
          <path d="M58.37,29.76l-1.24,20.25c.69-.31,1.33-.66,1.93-1.03l1.17-19.11-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M50.59,30.55l-1.32,21.53c1.03-.11,2.03-.28,2.98-.49l1.28-20.85-2.94-.18Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M56.35,30.27l-1.26,20.54c.32-.11.65-.22.95-.34l1.24-20.14-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M34.56,31.05l-1.17,19.02c.59.24,1.19.45,1.82.65l1.2-19.56-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M28.42,29.19l-1.06,17.23c.53.5,1.13.97,1.78,1.42l1.14-18.53-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M32.54,31.56l-1.08,17.64c.3.15.6.3.91.43l1.11-18.01-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
        </g>
        <ellipse cx="44.39" cy="34.98" rx="10.93" ry="19.67" transform="translate(6.76 77.14) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
        <path d="M29.29,27.34c.4,5.12,8.6,9.64,18.9,10.27,6.33.39,12.04-.77,15.75-2.9-.96-5.41-8.93-10.03-18.88-10.64-6.31-.39-12.02.92-15.77,3.27Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M43.79,44.76c10.5.64,19.36-3.41,20.24-9.14,0,.19,0,.38,0,.56-.37,6.02-9.46,10.37-20.3,9.7-10.84-.67-19.33-6.09-18.96-12.11.01-.19.03-.38.06-.56.17,5.79,8.47,10.9,18.97,11.55Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M29.98,34.09c-.03.54.12,1.13.44,1.76,1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c.4-.58.62-1.15.65-1.69.18-2.97-5.29-7.81-13.95-8.34s-14.68,3.61-14.87,6.58Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M30.43,35.85c1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c-1.46-2.82-6.41-6.15-13.51-6.58s-12.42,2.26-14.21,4.88Z" style="fill: #e95500; stroke-width: 0px;"/>
        <g>
          <path d="M34.21,33.96l-.2,3.29c-.06.98.33,1.94,1.08,2.79,2.31,1.23,5.31,2.17,8.85,2.39s6.63-.35,9.07-1.29c.85-.75,1.36-1.65,1.42-2.64l.2-3.29c.19-3.13-4.22-5.95-9.86-6.3-5.64-.35-10.36,1.91-10.56,5.04Z" style="fill: #ff8200; stroke-width: 0px;"/>
          <g>
            <path d="M48.3,29.97l-.76,12.42c.55-.05,1.07-.12,1.58-.2l.74-12.12-1.56-.1Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M41.75,30.84l-.69,11.25c.79.15,1.61.26,2.46.32l.7-11.42-2.47-.15Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M46.6,30.5l-.73,11.98c.27,0,.53-.01.79-.03l.73-11.9-.78-.05Z" style="fill: #e95500; stroke-width: 0px;"/>
          </g>
          <ellipse cx="44.41" cy="34.59" rx="5.68" ry="10.23" transform="translate(7.17 76.8) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
          <path d="M34.23,33.71c3.44,2.09,8.39,3.57,13.95,3.91,1.84.11,3.62.09,5.31-.04.67-.7,1.07-1.49,1.12-2.36.19-3.13-4.22-5.95-9.86-6.3s-10.1,1.79-10.53,4.79Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        </g>
      </g>
      <g>
        <path d="M78.28,28c10.84.67,19.33,6.09,18.96,12.11l-.03.53-.34,5.49-.02.32c-.37,6.02-9.46,10.37-20.3,9.7s-19.33-6.09-18.96-12.11l.02-.32.34-5.49.03-.53c.37-6.02,9.46-10.37,20.3-9.7Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M80.01,53.25c5.23.32,10.15-.39,14.26-1.86-3.5,3.26-10.19,5.22-17.72,4.75-10.84-.67-19.33-6.09-18.96-12.11l.02-.32.08-1.28c3.47,5.77,12.05,10.18,22.32,10.81Z" style="fill: #f86703; stroke-width: 0px;"/>
        <g>
          <path d="M91.59,33.68l-1.24,20.25c.69-.31,1.33-.66,1.93-1.03l1.17-19.11-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M83.81,34.48l-1.32,21.53c1.03-.11,2.03-.28,2.98-.49l1.28-20.85-2.94-.18Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M89.57,34.2l-1.26,20.54c.32-.11.65-.22.95-.34l1.24-20.14-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M67.78,34.98l-1.17,19.02c.59.24,1.19.45,1.82.65l1.2-19.56-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M61.64,33.12l-1.06,17.23c.53.5,1.13.97,1.78,1.42l1.14-18.53-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M65.76,35.49l-1.08,17.64c.3.15.6.3.91.43l1.11-18.01-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
        </g>
        <ellipse cx="77.61" cy="38.91" rx="10.93" ry="19.67" transform="translate(34.02 113.99) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
        <path d="M62.5,31.27c.4,5.12,8.6,9.64,18.9,10.27,6.33.39,12.04-.77,15.75-2.9-.96-5.41-8.93-10.03-18.88-10.64-6.31-.39-12.02.92-15.77,3.27Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M77.01,48.69c10.5.64,19.36-3.41,20.24-9.14,0,.19,0,.38,0,.56-.37,6.02-9.46,10.37-20.3,9.7s-19.33-6.09-18.96-12.11c.01-.19.03-.38.06-.56.17,5.79,8.47,10.9,18.97,11.55Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M63.2,38.02c-.03.54.12,1.13.44,1.76,1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c.4-.58.62-1.15.65-1.69.18-2.97-5.29-7.81-13.95-8.34s-14.68,3.61-14.87,6.58Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M63.64,39.78c1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c-1.46-2.82-6.41-6.15-13.51-6.58s-12.42,2.26-14.21,4.88Z" style="fill: #e95500; stroke-width: 0px;"/>
        <g>
          <path d="M67.42,37.89l-.2,3.29c-.06.98.33,1.94,1.08,2.79,2.31,1.23,5.31,2.17,8.85,2.39s6.63-.35,9.07-1.29c.85-.75,1.36-1.65,1.42-2.64l.2-3.29c.19-3.13-4.22-5.95-9.86-6.3s-10.36,1.91-10.56,5.04Z" style="fill: #ff8200; stroke-width: 0px;"/>
          <g>
            <path d="M81.52,33.9l-.76,12.42c.55-.05,1.07-.12,1.58-.2l.74-12.12-1.56-.1Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M74.97,34.77l-.69,11.25c.79.15,1.61.26,2.46.32l.7-11.42-2.47-.15Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M79.82,34.43l-.73,11.98c.27,0,.53-.01.79-.03l.73-11.9-.78-.05Z" style="fill: #e95500; stroke-width: 0px;"/>
          </g>
          <ellipse cx="77.63" cy="38.52" rx="5.68" ry="10.23" transform="translate(34.43 113.65) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
          <path d="M67.45,37.64c3.44,2.09,8.39,3.57,13.95,3.91,1.84.11,3.62.09,5.31-.04.67-.7,1.07-1.49,1.12-2.36.19-3.13-4.22-5.95-9.86-6.3s-10.1,1.79-10.53,4.79Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        </g>
      </g>
      <g>
        <path d="M105.69,25.75c10.84.67,19.33,6.09,18.96,12.11l-.03.53-.34,5.49-.02.32c-.37,6.02-9.46,10.37-20.3,9.7s-19.33-6.09-18.96-12.11l.02-.32.34-5.49.03-.53c.37-6.02,9.46-10.37,20.3-9.7Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M107.43,51c5.23.32,10.15-.39,14.26-1.86-3.5,3.26-10.19,5.22-17.72,4.75-10.84-.67-19.33-6.09-18.96-12.11l.02-.32.08-1.28c3.47,5.77,12.05,10.18,22.32,10.81Z" style="fill: #f86703; stroke-width: 0px;"/>
        <g>
          <path d="M119,31.43l-1.24,20.25c.69-.31,1.33-.66,1.93-1.03l1.17-19.11-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M111.23,32.23l-1.32,21.53c1.03-.11,2.03-.28,2.98-.49l1.28-20.85-2.94-.18Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M116.99,31.95l-1.26,20.54c.32-.11.65-.22.95-.34l1.24-20.14-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M95.19,32.73l-1.17,19.02c.59.24,1.19.45,1.82.65l1.2-19.56-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M89.05,30.87l-1.06,17.23c.53.5,1.13.97,1.78,1.42l1.14-18.53-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M93.18,33.24l-1.08,17.64c.3.15.6.3.91.43l1.11-18.01-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
        </g>
        <ellipse cx="105.02" cy="36.66" rx="10.93" ry="19.67" transform="translate(62 139.24) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
        <path d="M89.92,29.02c.4,5.12,8.6,9.64,18.9,10.27,6.33.39,12.04-.77,15.75-2.9-.96-5.41-8.93-10.03-18.88-10.64-6.31-.39-12.02.92-15.77,3.27Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M104.42,46.44c10.5.64,19.36-3.41,20.24-9.14,0,.19,0,.38,0,.56-.37,6.02-9.46,10.37-20.3,9.7s-19.33-6.09-18.96-12.11c.01-.19.03-.38.06-.56.17,5.79,8.47,10.9,18.97,11.55Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M90.61,35.77c-.03.54.12,1.13.44,1.76,1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c.4-.58.62-1.15.65-1.69.18-2.97-5.29-7.81-13.95-8.34-8.66-.53-14.68,3.61-14.87,6.58Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M91.06,37.53c1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c-1.46-2.82-6.41-6.15-13.51-6.58s-12.42,2.26-14.21,4.88Z" style="fill: #e95500; stroke-width: 0px;"/>
        <g>
          <path d="M94.84,35.64l-.2,3.29c-.06.98.33,1.94,1.08,2.79,2.31,1.23,5.31,2.17,8.85,2.39s6.63-.35,9.07-1.29c.85-.75,1.36-1.65,1.42-2.64l.2-3.29c.19-3.13-4.22-5.95-9.86-6.3s-10.36,1.91-10.56,5.04Z" style="fill: #ff8200; stroke-width: 0px;"/>
          <g>
            <path d="M108.93,31.65l-.76,12.42c.55-.05,1.07-.12,1.58-.2l.74-12.12-1.56-.1Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M102.38,32.52l-.69,11.25c.79.15,1.61.26,2.46.32l.7-11.42-2.47-.15Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M107.23,32.18l-.73,11.98c.27,0,.53-.01.79-.03l.73-11.9-.78-.05Z" style="fill: #e95500; stroke-width: 0px;"/>
          </g>
          <ellipse cx="105.05" cy="36.27" rx="5.68" ry="10.23" transform="translate(62.41 138.9) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
          <path d="M94.87,35.39c3.44,2.09,8.39,3.57,13.95,3.91,1.84.11,3.62.09,5.31-.04.67-.7,1.07-1.49,1.12-2.36.19-3.13-4.22-5.95-9.86-6.3s-10.1,1.79-10.53,4.79Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        </g>
      </g>
      <g>
        <path d="M133.54,14.83c10.84.67,19.33,6.09,18.96,12.11l-.03.53-.34,5.49-.02.32c-.37,6.02-9.46,10.37-20.3,9.7-10.84-.67-19.33-6.09-18.96-12.11l.02-.32.34-5.49.03-.53c.37-6.02,9.46-10.37,20.3-9.7Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M135.27,40.07c5.23.32,10.15-.39,14.26-1.86-3.5,3.26-10.19,5.22-17.72,4.75-10.84-.67-19.33-6.09-18.96-12.11l.02-.32.08-1.28c3.47,5.77,12.05,10.18,22.32,10.81Z" style="fill: #f86703; stroke-width: 0px;"/>
        <g>
          <path d="M146.84,20.51l-1.24,20.25c.69-.31,1.33-.66,1.93-1.03l1.17-19.11-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M139.07,21.3l-1.32,21.53c1.03-.11,2.03-.28,2.98-.49l1.28-20.85-2.94-.18Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M144.83,21.02l-1.26,20.54c.32-.11.65-.22.95-.34l1.24-20.14-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M123.03,21.8l-1.17,19.02c.59.24,1.19.45,1.82.65l1.2-19.56-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M116.89,19.94l-1.06,17.23c.53.5,1.13.97,1.78,1.42l1.14-18.53-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M121.02,22.31l-1.08,17.64c.3.15.6.3.91.43l1.11-18.01-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
        </g>
        <ellipse cx="132.87" cy="25.73" rx="10.93" ry="19.67" transform="translate(99.04 156.77) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
        <path d="M117.76,18.1c.4,5.12,8.6,9.64,18.9,10.27,6.33.39,12.04-.77,15.75-2.9-.96-5.41-8.93-10.03-18.88-10.64-6.31-.39-12.02.92-15.77,3.27Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M132.27,35.51c10.5.64,19.36-3.41,20.24-9.14,0,.19,0,.38,0,.56-.37,6.02-9.46,10.37-20.3,9.7-10.84-.67-19.33-6.09-18.96-12.11.01-.19.03-.38.06-.56.17,5.79,8.47,10.9,18.97,11.55Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M118.46,24.85c-.03.54.12,1.13.44,1.76,1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c.4-.58.62-1.15.65-1.69.18-2.97-5.29-7.81-13.95-8.34s-14.68,3.61-14.87,6.58Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M118.9,26.6c1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c-1.46-2.82-6.41-6.15-13.51-6.58s-12.42,2.26-14.21,4.88Z" style="fill: #e95500; stroke-width: 0px;"/>
        <g>
          <path d="M122.68,24.72l-.2,3.29c-.06.98.33,1.94,1.08,2.79,2.31,1.23,5.31,2.17,8.85,2.39s6.63-.35,9.07-1.29c.85-.75,1.36-1.65,1.42-2.64l.2-3.29c.19-3.13-4.22-5.95-9.86-6.3s-10.36,1.91-10.56,5.04Z" style="fill: #ff8200; stroke-width: 0px;"/>
          <g>
            <path d="M136.78,20.72l-.76,12.42c.55-.05,1.07-.12,1.58-.2l.74-12.12-1.56-.1Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M130.23,21.59l-.69,11.25c.79.15,1.61.26,2.46.32l.7-11.42-2.47-.15Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M135.08,21.26l-.73,11.98c.27,0,.53-.01.79-.03l.73-11.9-.78-.05Z" style="fill: #e95500; stroke-width: 0px;"/>
          </g>
          <ellipse cx="132.89" cy="25.34" rx="5.68" ry="10.23" transform="translate(99.45 156.43) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
          <path d="M122.71,24.46c3.44,2.09,8.39,3.57,13.95,3.91,1.84.11,3.62.09,5.31-.04.67-.7,1.07-1.49,1.12-2.36.19-3.13-4.22-5.95-9.86-6.3s-10.1,1.79-10.53,4.79Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        </g>
      </g>
      <g>
        <path d="M159.95,23.8c10.84.67,19.33,6.09,18.96,12.11l-.03.53-.34,5.49-.02.32c-.37,6.02-9.46,10.37-20.3,9.7s-19.33-6.09-18.96-12.11l.02-.32.34-5.49.03-.53c.37-6.02,9.46-10.37,20.3-9.7Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M161.68,49.05c5.23.32,10.15-.39,14.26-1.86-3.5,3.26-10.19,5.22-17.72,4.75-10.84-.67-19.33-6.09-18.96-12.11l.02-.32.08-1.28c3.47,5.77,12.05,10.18,22.32,10.81Z" style="fill: #f86703; stroke-width: 0px;"/>
        <g>
          <path d="M173.26,29.48l-1.24,20.25c.69-.31,1.33-.66,1.93-1.03l1.17-19.11-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M165.48,30.27l-1.32,21.53c1.03-.11,2.03-.28,2.98-.49l1.28-20.85-2.94-.18Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M171.24,29.99l-1.26,20.54c.32-.11.65-.22.95-.34l1.24-20.14-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M149.45,30.77l-1.17,19.02c.59.24,1.19.45,1.82.65l1.2-19.56-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M143.31,28.91l-1.06,17.23c.53.5,1.13.97,1.78,1.42l1.14-18.53-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M147.43,31.28l-1.08,17.64c.3.15.6.3.91.43l1.11-18.01-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
        </g>
        <ellipse cx="159.28" cy="34.7" rx="10.93" ry="19.67" transform="translate(114.88 191.56) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
        <path d="M144.17,27.07c.4,5.12,8.6,9.64,18.9,10.27,6.33.39,12.04-.77,15.75-2.9-.96-5.41-8.93-10.03-18.88-10.64-6.31-.39-12.02.92-15.77,3.27Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M158.68,44.48c10.5.64,19.36-3.41,20.24-9.14,0,.19,0,.38,0,.56-.37,6.02-9.46,10.37-20.3,9.7s-19.33-6.09-18.96-12.11c.01-.19.03-.38.06-.56.17,5.79,8.47,10.9,18.97,11.55Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M144.87,33.82c-.03.54.12,1.13.44,1.76,1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c.4-.58.62-1.15.65-1.69.18-2.97-5.29-7.81-13.95-8.34-8.66-.53-14.68,3.61-14.87,6.58Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M145.31,35.57c1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c-1.46-2.82-6.41-6.15-13.51-6.58s-12.42,2.26-14.21,4.88Z" style="fill: #e95500; stroke-width: 0px;"/>
        <g>
          <path d="M149.09,33.69l-.2,3.29c-.06.98.33,1.94,1.08,2.79,2.31,1.23,5.31,2.17,8.85,2.39s6.63-.35,9.07-1.29c.85-.75,1.36-1.65,1.42-2.64l.2-3.29c.19-3.13-4.22-5.95-9.86-6.3s-10.36,1.91-10.56,5.04Z" style="fill: #ff8200; stroke-width: 0px;"/>
          <g>
            <path d="M163.19,29.7l-.76,12.42c.55-.05,1.07-.12,1.58-.2l.74-12.12-1.56-.1Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M156.64,30.57l-.69,11.25c.79.15,1.61.26,2.46.32l.7-11.42-2.47-.15Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M161.49,30.23l-.73,11.98c.27,0,.53-.01.79-.03l.73-11.9-.78-.05Z" style="fill: #e95500; stroke-width: 0px;"/>
          </g>
          <ellipse cx="159.3" cy="34.32" rx="5.68" ry="10.23" transform="translate(115.29 191.22) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
          <path d="M149.12,33.43c3.44,2.09,8.39,3.57,13.95,3.91,1.84.11,3.62.09,5.31-.04.67-.7,1.07-1.49,1.12-2.36.19-3.13-4.22-5.95-9.86-6.3s-10.1,1.79-10.53,4.79Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        </g>
      </g>
      <g>
        <path d="M60.59,14.07c10.84.67,19.33,6.09,18.96,12.11l-.03.53-.34,5.49-.02.32c-.37,6.02-9.46,10.37-20.3,9.7-10.84-.67-19.33-6.09-18.96-12.11l.02-.32.34-5.49.03-.53c.37-6.02,9.46-10.37,20.3-9.7Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M62.32,39.32c5.23.32,10.15-.39,14.26-1.86-3.5,3.26-10.19,5.22-17.72,4.75-10.84-.67-19.33-6.09-18.96-12.11l.02-.32.08-1.28c3.47,5.77,12.05,10.18,22.32,10.81Z" style="fill: #f86703; stroke-width: 0px;"/>
        <g>
          <path d="M73.9,19.75l-1.24,20.25c.69-.31,1.33-.66,1.93-1.03l1.17-19.11-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M66.12,20.54l-1.32,21.53c1.03-.11,2.03-.28,2.98-.49l1.28-20.85-2.94-.18Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M71.88,20.26l-1.26,20.54c.32-.11.65-.22.95-.34l1.24-20.14-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M50.09,21.04l-1.17,19.02c.59.24,1.19.45,1.82.65l1.2-19.56-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M43.95,19.18l-1.06,17.23c.53.5,1.13.97,1.78,1.42l1.14-18.53-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M48.07,21.56l-1.08,17.64c.3.15.6.3.91.43l1.11-18.01-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
        </g>
        <ellipse cx="59.92" cy="24.97" rx="10.93" ry="19.67" transform="translate(31.32 83.25) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
        <path d="M44.81,17.34c.4,5.12,8.6,9.64,18.9,10.27,6.33.39,12.04-.77,15.75-2.9-.96-5.41-8.93-10.03-18.88-10.64-6.31-.39-12.02.92-15.77,3.27Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M59.32,34.75c10.5.64,19.36-3.41,20.24-9.14,0,.19,0,.38,0,.56-.37,6.02-9.46,10.37-20.3,9.7s-19.33-6.09-18.96-12.11c.01-.19.03-.38.06-.56.17,5.79,8.47,10.9,18.97,11.55Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M45.51,24.09c-.03.54.12,1.13.44,1.76,1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c.4-.58.62-1.15.65-1.69.18-2.97-5.29-7.81-13.95-8.34s-14.68,3.61-14.87,6.58Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M45.95,25.85c1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c-1.46-2.82-6.41-6.15-13.51-6.58-7.1-.44-12.42,2.26-14.21,4.88Z" style="fill: #e95500; stroke-width: 0px;"/>
        <g>
          <path d="M49.73,23.96l-.2,3.29c-.06.98.33,1.94,1.08,2.79,2.31,1.23,5.31,2.17,8.85,2.39s6.63-.35,9.07-1.29c.85-.75,1.36-1.65,1.42-2.64l.2-3.29c.19-3.13-4.22-5.95-9.86-6.3s-10.36,1.91-10.56,5.04Z" style="fill: #ff8200; stroke-width: 0px;"/>
          <g>
            <path d="M63.83,19.97l-.76,12.42c.55-.05,1.07-.12,1.58-.2l.74-12.12-1.56-.1Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M57.28,20.84l-.69,11.25c.79.15,1.61.26,2.46.32l.7-11.42-2.47-.15Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M62.13,20.5l-.73,11.98c.27,0,.53-.01.79-.03l.73-11.9-.78-.05Z" style="fill: #e95500; stroke-width: 0px;"/>
          </g>
          <ellipse cx="59.94" cy="24.59" rx="5.68" ry="10.23" transform="translate(31.73 82.91) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
          <path d="M49.76,23.7c3.44,2.09,8.39,3.57,13.95,3.91,1.84.11,3.62.09,5.31-.04.67-.7,1.07-1.49,1.12-2.36.19-3.13-4.22-5.95-9.86-6.3s-10.1,1.79-10.53,4.79Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        </g>
      </g>
      <g>
        <path d="M88.59,12.07c10.84.67,19.33,6.09,18.96,12.11l-.03.53-.34,5.49-.02.32c-.37,6.02-9.46,10.37-20.3,9.7-10.84-.67-19.33-6.09-18.96-12.11l.02-.32.34-5.49.03-.53c.37-6.02,9.46-10.37,20.3-9.7Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M90.32,37.32c5.23.32,10.15-.39,14.26-1.86-3.5,3.26-10.19,5.22-17.72,4.75-10.84-.67-19.33-6.09-18.96-12.11l.02-.32.08-1.28c3.47,5.77,12.05,10.18,22.32,10.81Z" style="fill: #f86703; stroke-width: 0px;"/>
        <g>
          <path d="M101.9,17.75l-1.24,20.25c.69-.31,1.33-.66,1.93-1.03l1.17-19.11-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M94.12,18.54l-1.32,21.53c1.03-.11,2.03-.28,2.98-.49l1.28-20.85-2.94-.18Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M99.88,18.26l-1.26,20.54c.32-.11.65-.22.95-.34l1.24-20.14-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M78.09,19.04l-1.17,19.02c.59.24,1.19.45,1.82.65l1.2-19.56-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M71.95,17.18l-1.06,17.23c.53.5,1.13.97,1.78,1.42l1.14-18.53-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M76.07,19.56l-1.08,17.64c.3.15.6.3.91.43l1.11-18.01-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
        </g>
        <ellipse cx="87.92" cy="22.97" rx="10.93" ry="19.67" transform="translate(59.6 109.32) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
        <path d="M72.81,15.34c.4,5.12,8.6,9.64,18.9,10.27,6.33.39,12.04-.77,15.75-2.9-.96-5.41-8.93-10.03-18.88-10.64-6.31-.39-12.02.92-15.77,3.27Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M87.32,32.75c10.5.64,19.36-3.41,20.24-9.14,0,.19,0,.38,0,.56-.37,6.02-9.46,10.37-20.3,9.7s-19.33-6.09-18.96-12.11c.01-.19.03-.38.06-.56.17,5.79,8.47,10.9,18.97,11.55Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M73.51,22.09c-.03.54.12,1.13.44,1.76,1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c.4-.58.62-1.15.65-1.69.18-2.97-5.29-7.81-13.95-8.34s-14.68,3.61-14.87,6.58Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M73.95,23.85c1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c-1.46-2.82-6.41-6.15-13.51-6.58-7.1-.44-12.42,2.26-14.21,4.88Z" style="fill: #e95500; stroke-width: 0px;"/>
        <g>
          <path d="M77.73,21.96l-.2,3.29c-.06.98.33,1.94,1.08,2.79,2.31,1.23,5.31,2.17,8.85,2.39s6.63-.35,9.07-1.29c.85-.75,1.36-1.65,1.42-2.64l.2-3.29c.19-3.13-4.22-5.95-9.86-6.3s-10.36,1.91-10.56,5.04Z" style="fill: #ff8200; stroke-width: 0px;"/>
          <g>
            <path d="M91.83,17.97l-.76,12.42c.55-.05,1.07-.12,1.58-.2l.74-12.12-1.56-.1Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M85.28,18.84l-.69,11.25c.79.15,1.61.26,2.46.32l.7-11.42-2.47-.15Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M90.13,18.5l-.73,11.98c.27,0,.53-.01.79-.03l.73-11.9-.78-.05Z" style="fill: #e95500; stroke-width: 0px;"/>
          </g>
          <ellipse cx="87.94" cy="22.59" rx="5.68" ry="10.23" transform="translate(60.01 108.98) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
          <path d="M77.76,21.7c3.44,2.09,8.39,3.57,13.95,3.91,1.84.11,3.62.09,5.31-.04.67-.7,1.07-1.49,1.12-2.36.19-3.13-4.22-5.95-9.86-6.3s-10.1,1.79-10.53,4.79Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        </g>
      </g>
      <g>
        <path d="M107.59.07c10.84.67,19.33,6.09,18.96,12.11l-.03.53-.34,5.49-.02.32c-.37,6.02-9.46,10.37-20.3,9.7s-19.33-6.09-18.96-12.11l.02-.32.34-5.49.03-.53c.37-6.02,9.46-10.37,20.3-9.7Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M109.32,25.32c5.23.32,10.15-.39,14.26-1.86-3.5,3.26-10.19,5.22-17.72,4.75-10.84-.67-19.33-6.09-18.96-12.11l.02-.32.08-1.28c3.47,5.77,12.05,10.18,22.32,10.81Z" style="fill: #f86703; stroke-width: 0px;"/>
        <g>
          <path d="M120.9,5.75l-1.24,20.25c.69-.31,1.33-.66,1.93-1.03l1.17-19.11-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M113.12,6.54l-1.32,21.53c1.03-.11,2.03-.28,2.98-.49l1.28-20.85-2.94-.18Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M118.88,6.26l-1.26,20.54c.32-.11.65-.22.95-.34l1.24-20.14-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M97.09,7.04l-1.17,19.02c.59.24,1.19.45,1.82.65l1.2-19.56-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M90.95,5.18l-1.06,17.23c.53.5,1.13.97,1.78,1.42l1.14-18.53-1.86-.11Z" style="fill: #e95500; stroke-width: 0px;"/>
          <path d="M95.07,7.56l-1.08,17.64c.3.15.6.3.91.43l1.11-18.01-.93-.06Z" style="fill: #e95500; stroke-width: 0px;"/>
        </g>
        <ellipse cx="106.92" cy="10.97" rx="10.93" ry="19.67" transform="translate(89.42 117.02) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
        <path d="M91.81,3.34c.4,5.12,8.6,9.64,18.9,10.27,6.33.39,12.04-.77,15.75-2.9-.96-5.41-8.93-10.03-18.88-10.64-6.31-.39-12.02.92-15.77,3.27Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M106.32,20.75c10.5.64,19.36-3.41,20.24-9.14,0,.19,0,.38,0,.56-.37,6.02-9.46,10.37-20.3,9.7-10.84-.67-19.33-6.09-18.96-12.11.01-.19.03-.38.06-.56.17,5.79,8.47,10.9,18.97,11.55Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        <path d="M92.51,10.09c-.03.54.12,1.13.44,1.76,1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c.4-.58.62-1.15.65-1.69.18-2.97-5.29-7.81-13.95-8.34s-14.68,3.61-14.87,6.58Z" style="fill: #ff8200; stroke-width: 0px;"/>
        <path d="M92.95,11.85c1.45,2.82,6.41,6.15,13.51,6.59s12.42-2.26,14.21-4.89c-1.46-2.82-6.41-6.15-13.51-6.58s-12.42,2.26-14.21,4.88Z" style="fill: #e95500; stroke-width: 0px;"/>
        <g>
          <path d="M96.73,9.96l-.2,3.29c-.06.98.33,1.94,1.08,2.79,2.31,1.23,5.31,2.17,8.85,2.39s6.63-.35,9.07-1.29c.85-.75,1.36-1.65,1.42-2.64l.2-3.29c.19-3.13-4.22-5.95-9.86-6.3s-10.36,1.91-10.56,5.04Z" style="fill: #ff8200; stroke-width: 0px;"/>
          <g>
            <path d="M110.83,5.97l-.76,12.42c.55-.05,1.07-.12,1.58-.2l.74-12.12-1.56-.1Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M104.28,6.84l-.69,11.25c.79.15,1.61.26,2.46.32l.7-11.42-2.47-.15Z" style="fill: #e95500; stroke-width: 0px;"/>
            <path d="M109.13,6.5l-.73,11.98c.27,0,.53-.01.79-.03l.73-11.9-.78-.05Z" style="fill: #e95500; stroke-width: 0px;"/>
          </g>
          <ellipse cx="106.94" cy="10.59" rx="5.68" ry="10.23" transform="translate(89.83 116.68) rotate(-86.49)" style="fill: #ffac00; stroke-width: 0px;"/>
          <path d="M96.76,9.7c3.44,2.09,8.39,3.57,13.95,3.91,1.84.11,3.62.09,5.31-.04.67-.7,1.07-1.49,1.12-2.36.19-3.13-4.22-5.95-9.86-6.3s-10.1,1.79-10.53,4.79Z" style="fill: #ffcd00; stroke-width: 0px;"/>
        </g>
      </g>
      <g style="opacity: .5;">
        <path d="M88.6,158.2c-21.46,1.33-11.82,29.28,11.9,27.81,23.21-1.44,10.96-29.23-11.9-27.81Z" style="fill: #fff; stroke-width: 0px;"/>
        <path d="M77.46,92.67c-16.41,12.4-13.56,31.43-13.56,31.43,0,0-10.32-26.78-23.72-43.15-1.8,21.07,4.8,49,4.8,49,0,0-8.39-17.32-28.95-18.28,17.05,9.79,23.67,27.45,25.18,32.19,10.16-6.28,21.78-9.79,33.72-10.21-1.35-4.41-6.13-22.99,2.53-40.98ZM141.4,210.88c2.47-1,4.88-2.09,7.23-3.26-.62-18.12-8.23-34.47-20.2-46.44-12.51-12.51-29.8-20.25-48.89-20.25-27.01,0-50.41,15.5-61.79,38.08,1.61,2.14,3.32,4.21,5.12,6.2,9.57-21.78,31.34-36.99,56.65-37,34.18,0,61.88,27.69,61.89,61.87h0c0,.27,0,.54,0,.8Z" style="fill: #fff; stroke-width: 0px;"/>
      </g>
      <g>
        <g>
          <path d="M150.44,55.65c1.76,22.68,2.11,41.14.78,41.25-1.33.1-3.83-18.2-5.59-40.87-1.76-22.68-2.11-41.14-.78-41.25,1.33-.1,3.83,18.2,5.59,40.87Z" style="fill: url(#Degradado_sin_nombre_5042); mix-blend-mode: color-dodge; stroke-width: 0px;"/>
          <path d="M149.86,57.41c-14.79,17.28-27.6,30.58-28.61,29.72-1.01-.87,10.16-15.58,24.95-32.85,14.79-17.28,27.6-30.58,28.61-29.72,1.01.87-10.16,15.58-24.95,32.85Z" style="fill: url(#Degradado_sin_nombre_5042-2); mix-blend-mode: color-dodge; stroke-width: 0px;"/>
          <path d="M146.46,57.67c-17.28-14.79-30.58-27.6-29.72-28.61.87-1.01,15.58,10.16,32.85,24.95,17.28,14.79,30.58,27.6,29.72,28.61-.87,1.01-15.58-10.16-32.85-24.95Z" style="fill: url(#Degradado_sin_nombre_5042-3); mix-blend-mode: color-dodge; stroke-width: 0px;"/>
        </g>
        <circle cx="147.97" cy="55.91" r="30.99" style="fill: url(#Degradado_sin_nombre_5041); mix-blend-mode: screen; stroke-width: 0px;"/>
      </g>
    </g>
  </g>
</svg>