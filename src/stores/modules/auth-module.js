import authApi from '../../api/auth-api'
import { isAfter } from 'date-fns'
import storage from '../../utility/storage'

export default {
  namespaced: true,
  state: {
    status: '',
    token: '',
    expiresAt: null,
    refreshToken: storage.getString('refresh_token') || '',
    user: storage.getObject('user') || null
  },
  mutations: {
    authRequest (state) {
      state.status = 'loading'
    },
    authSuccess (state, { token, expiresAt, refreshToken }) {
      state.status = 'success'
      state.token = token
      state.expiresAt = expiresAt
      state.refreshToken = refreshToken

      storage.setString('refresh_token', state.refreshToken)
    },
    authError (state) {
      state.status = 'error'
    },
    logout (state) {
      state.status = ''
      state.token = ''
      state.expiresAt = null
      state.refreshToken = ''
      state.user = null

      storage.removeItem('refresh_token')
      storage.removeItem('settings')
      storage.removeItem('member')
      storage.removeItem('notifications')
    },
    user (state, data) {
      state.user = data
      storage.setObject('user', data)
    }
  },
  actions: {
    login ({ commit }, user) {
      commit('authRequest')
      return authApi.login(user).then((response) => {
        commit('authSuccess', response)
        return response
      }).catch(err => {
        commit('authError')
        return Promise.reject(err)
      })
    },
    loadCurrentUser({ commit }) {
      return authApi.loadUser().then((response) => {
        commit('user', response)
      })
    },
    refreshToken ({ commit }) {
      return authApi.refreshToken(storage.getString('refresh_token')).then((response) => {
        commit('authSuccess', response)
      })
    },
    logout ({ commit }) {
      return new Promise((resolve) => {
        commit('logout')
        resolve({ 'success': true })
      })
    },
    authenticated({ commit, state }) {
      return new Promise((resolve, reject) => {
        if (state.expiresAt !== null && state.token !== '') {
          let dateNow = new Date()
          if (!isAfter(dateNow, state.expiresAt)) {
            reject(new Error('Expired Token!'))
          } else {
            resolve(true)
          }
        } else {
          reject(new Error('No Token!'))
        }
      }).catch(() => {
        if (state.refreshToken === '') {
          return false
        } else {
          return authApi.refreshToken(state.refreshToken).then((response) => {
            commit('authSuccess', response)
            return true
          }).catch(() => {
            return false
          })
        }
      })
    },
  },
  getters: {
    isLoggedIn (state) {
      return !!state.refreshToken
    },
    status: state => state.status,
    token: state => state.token,
    user: state => state.user,
  }
}
