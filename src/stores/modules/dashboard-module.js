import dashboardApi from "../../api/dashboard-api";

export default {
  namespaced: true,
  state: {
    summary: ''
  },
  mutations: {
    done (state, data) {
      state.summary = data
    }
  },
  actions: {
    list ({ commit }, data) {
      return dashboardApi.listAction(data).then((response) => {
        commit('done', response.data)
        return response
      }).catch(err => {
        return Promise.reject(err)
      })
    }
  },
  getters: {
    summary: state => state.summary
  }
}