import withdrawalApi from '../../api/withdrawal-api'

export default {
  namespaced: true,
  state: {
    status: '',
    message: '',
    customerPaymentOptions: [],
    bitcoinExchangeRate: null,
    usdtTrc20ExchangeRate: null,
    usdtErc20ExchangeRate: null,
    usdcExchangeRate: null,
    apiErrors: {}
  },
  mutations: {
    done (state, { status, message }) {
      state.status = status
      state.message = message
    },
    setApiErrors (state, { paymentOption, errors }) {
      let apiErrors = state.apiErrors
      apiErrors[paymentOption] = errors
      state.apiErrors = apiErrors
    },
    setCustomerPaymentOptions (state, customerPaymentOptions) {
      state.customerPaymentOptions = customerPaymentOptions
    },
    setBitcoinExchangeRate (state, bitcoinExchangeRate) {
      state.bitcoinExchangeRate = bitcoinExchangeRate
    },
    setUsdtTrc20ExchangeRate (state, usdtTrc20ExchangeRate) {
      state.usdtTrc20ExchangeRate = usdtTrc20ExchangeRate
    },
    setUsdtErc20ExchangeRate (state, usdtErc20ExchangeRate) {
      state.usdtErc20ExchangeRate = usdtErc20ExchangeRate
    },
    setUsdcExchangeRate (state, usdcExchangeRate) {
      state.usdcExchangeRate = usdcExchangeRate
    },
  },
  actions: {
    fetchPaymentOptions({ commit, rootGetters }) {
      const memberDetails = rootGetters['member/member']
      const queryParams = {
        transactionType: 'Withdrawal',
        country: memberDetails.country,
        currency: memberDetails.currency.code,
        enabled: memberDetails.details.enabled ? 1 : 0,
        registered: !memberDetails.details.enabled ? 1 : 0,
        firstDeposit: 1,
        isAffiliate: 1,
        customerId: +memberDetails.id
      }

      withdrawalApi.fetchCustomerPaymentOptions(queryParams).then(customerPaymentOptions => commit('setCustomerPaymentOptions', customerPaymentOptions))
    },
    fetchAdjustedBitcoinExchangeRates({ commit }) {
      withdrawalApi.fetchAdjustedBitcoinExchangeRates().then(bitcoinExchangeRate => commit('setBitcoinExchangeRate', bitcoinExchangeRate))
    },
    fetchAdjustedUsdtTrc20ExchangeRates({ commit }) {
      withdrawalApi.fetchAdjustedUsdtTrc20ExchangeRates().then(exchangeRate => commit('setUsdtTrc20ExchangeRate', exchangeRate))
    },
    fetchAdjustedUsdtErc20ExchangeRates({ commit }) {
      withdrawalApi.fetchAdjustedUsdtErc20ExchangeRates().then(exchangeRate => commit('setUsdtErc20ExchangeRate', exchangeRate))
    },
    fetchAdjustedUsdcExchangeRates({ commit }) {
      withdrawalApi.fetchAdjustedUsdcExchangeRates().then(exchangeRate => commit('setUsdcExchangeRate', exchangeRate))
    },
    withdraw ({ commit }, data) {
      return withdrawalApi.withdrawAction(data).then(() => {
        const response = { status: 'success', message: 'Your request has been submitted' }
        commit('done', response)
        return response
      }).catch(err => {
        commit('setApiErrors', { paymentOption: data.payment_option_type, errors: err.data.errors })
        return Promise.reject(err)
      })
    },
  },
  getters: {
    apiErrors: state => state.apiErrors,
    customerPaymentOptions: state => state.customerPaymentOptions,
    bitcoinExchangeRate: state => state.bitcoinExchangeRate,
    usdtTrc20ExchangeRate: state => state.usdtTrc20ExchangeRate,
    usdtErc20ExchangeRate: state => state.usdtErc20ExchangeRate,
    usdcExchangeRate: state => state.usdcExchangeRate,
  }
}