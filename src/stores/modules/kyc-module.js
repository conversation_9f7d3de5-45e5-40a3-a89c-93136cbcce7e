import kyc from "@/api/kyc-api";
import { getField, updateField } from "vuex-map-fields";

export default {
    namespaced: true,
    state: {
        confirm1: false,
        confirm2: false,
        loading: false,
        initiating: false,
        kycDetails: null
    },
    mutations: {
        updateLoading (state, loading) {
            state.loading = loading
        },
        updateInitiating (state, initiating) {
            state.initiating = initiating
        },
        updateKycDetails (state, kycDetails) {
            state.kycDetails = kycDetails
        },
        updateField
    },
    actions: {
        initiate ({ commit, rootState }) {
            const customerId = +rootState.member.member.id
            commit('updateInitiating', true)
            kyc.initiate(customerId).then(response =>  {
                commit('updateInitiating', false)
                window.location.href = response.redirectUrl
            })
        },
        getKYCDetails ({ commit, rootState }) {
            const customerId = +rootState.member.member.id
            commit('updateLoading', true)
            kyc.getKYCDetails(customerId).then(kycDetails => {
                commit('updateLoading', false)
                commit('updateKycDetails', kycDetails)
            })
        },
    },
    getters: {
        status: (state) => {
            const getParameterByName = (name, url = window.location.href) => {
                name = name.replace(/[[]]/g, '\\$&');
                const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                    results = regex.exec(url);
                if (!results) { return null; }
                if (!results[2]) { return ''; }
                return decodeURIComponent(results[2].replace(/\+/g, ' '));
            }

            const callbackResult = getParameterByName('result');
            const statusDetails = state.kycDetails

            if (callbackResult !== null) {
                if (callbackResult === 'success') { return 'pending' } else { return 'failed' }
            }

            if  (statusDetails) {
                if (statusDetails.requires_manual) {
                    return 'manual_verification';
                }

                if (statusDetails.verified) {
                    return 'success';
                }

                const lastAttempt = statusDetails.attempts[0];

                if (lastAttempt) {
                    if (lastAttempt.details.scan.status === 'PENDING' || !Object.prototype.hasOwnProperty.call(lastAttempt.details, 'callback')) {
                        return 'pending';
                    }

                    if (lastAttempt.details.scan.status === 'FAILED') {
                        return 'failed';
                    }

                    if (lastAttempt.details.callback.idScanStatus === 'ERROR') {
                        return 'failed';
                    }
                }
            }

            return 'for-verification'
        },
        loading: state => state.loading,
        initiating: state => state.initiating,
        getField
    }
}
