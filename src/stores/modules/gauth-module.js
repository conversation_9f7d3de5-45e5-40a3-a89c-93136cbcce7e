import gAuthApi from "../../api/gauth-api";
import storage from '@/utility/storage'

export default {
  namespaced: true,
  state: {
    status: '',
    setting: {},
    setup: {},
    confirm: {},
    reset: '',
  },
  mutations: {
    statusDone (state, data) {
      state.status = data
    },
    settingDone (state, data) {
      state.setting = data
      storage.setObject('gauth_setting', data)
    },
    setupDone (state, data) {
      state.setup = data
    },
    confirmDone (state, data) {
      state.confirm = data
    },
    resetDone (state, data) {
      state.reset = data
      storage.setObject('gauth_setting', {"status":"off"})
    },
    enableDone (state, data) {
      state.setting = data
      storage.setObject('gauth_setting', data)
    },
    disableDone (state, data) {
      state.setting = data
      storage.setObject('gauth_setting', data)
    },
  },
  actions: {
    status ({ commit }, data) {
      return gAuthApi.statusAction(data).then((response) => {
        commit('statusDone', response.status)
        return response
      }).catch(err => {
        return Promise.reject(err)
      })
    },
    setting ({ commit }, data) {
      return gAuthApi.settingAction(data).then((response) => {
        commit('settingDone', response)
        return response
      }).catch(err => {
        return Promise.reject(err)
      })
    },
    setup ({ commit }, data) {
      return gAuthApi.setupAction(data).then((response) => {
        commit('setupDone', response)
        return response
      }).catch(err => {
        return Promise.reject(err)
      })
    },
    confirm ({ commit }, data) {
      return gAuthApi.confirmAction(data).then((response) => {
        commit('confirmDone', response)
        return response
      }).catch(err => {
        return Promise.reject(err)
      })
    },
    reset ({ commit }, data) {
      return gAuthApi.resetAction(data).then((response) => {
        commit('resetDone', response)
        return response
      }).catch(err => {
        return Promise.reject(err)
      })
    },
    enable ({ commit }, data) {
      return gAuthApi.enableAction(data).then((response) => {
        commit('enableDone', response)
        return response
      }).catch(err => {
        return Promise.reject(err)
      })
    },
    disable ({ commit }, data) {
      return gAuthApi.disableAction(data).then((response) => {
        commit('disableDone', response)
        return response
      }).catch(err => {
        return Promise.reject(err)
      })
    },
  },
  getters: {
    getStatus: state => state.status,
    getSetting: state => state.setting,
    getSetup: state => state.setup,
    getConfirm: state => state.confirm,
  }
}
