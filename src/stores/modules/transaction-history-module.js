import transactionHistoryApi from "../../api/transaction-history-api";

export default {
  namespaced: true,
  state: {
    transactionList: ''
  },
  mutations: {
    done (state, data) {
      state.transactionList = data
    }
  },
  actions: {
    list ({ commit }, data) {
      return transactionHistoryApi.listAction(data).then((response) => {
        commit('done', response.data)
        return response
      }).catch(err => {
        return Promise.reject(err)
      })
    }
  },
  getters: {
    getTransactionList: state => state.transactionList
  }
}