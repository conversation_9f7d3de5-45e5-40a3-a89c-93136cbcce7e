import accountApi from '../../api/account-api'

export default {
  namespaced: true,
  state: {
    status: '',
    codeStatus: '',
    message: ''
  },
  mutations: {
    done (state, { status, message }) {
      state.status = status
      state.message = message
    },
    doneGetCode (state, status) {
      state.codeStatus = status
    },
  },
  actions: {
    forgotPassword({ commit }, data) {
      return accountApi.forgotPasswordAction(data).then(() => {
        const response = { status: 'success', message: 'Your password have been updated' }
        commit('done', response)
        return response
      }).catch((err) => {
        const errorMessage = err.data.errors[0].message
        commit('done', { status: 'error', message: errorMessage })
        return Promise.reject(err)
      })
    },
    updatePassword({ commit }, data) {
      return accountApi.updatePasswordAction(data).then(() => {
        const response = { status: 'success', message: 'Your password have been updated' }
        commit('done', response)
        return response
      }).catch((err) => {
        const errorMessage = err.data.errors[0].message
        commit('done', { status: 'error', message: errorMessage })
        return Promise.reject(err)
      })
    },
    getCode({ commit }, data) {
      return accountApi.getCodeAction(data).then((response) => {
        commit('doneGetCode', 'success')
        return response
      }).catch((err) => {
        commit('doneGetCode', 'error')
        return Promise.reject(err)
      })
    }
  },
  getters: {
    getCodeStatus: state => state.codeStatus,
    getForgotPasswordStatus: state => state.status,
    getForgotPasswordMessage: state => state.message
  }
}
