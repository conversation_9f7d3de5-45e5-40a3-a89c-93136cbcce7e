import memberApi from '../../api/member-api'
import storage from '../../utility/storage'

export default {
  namespaced: true,
  state: {
    member: storage.getObject('member') || null,
    settings: storage.getObject('settings') || null,
    notifications: storage.getObject('notifications') || null,
  },
  mutations: {
    member (state, data) {
      state.member = data,
      storage.setObject('member', data)
    },
    products (state, data) {
      state.products = data.items[0],
      storage.setObject('products', data.items[0])
    },
    settings (state, data) {
      state.settings = data,
      storage.setObject('settings', data['data'])
    },
    notifications (state, data) {
      state.notifications = data,
      storage.setObject('notifications', data)
    }
  },
  actions: {
    getMember ({ commit }) {
      return memberApi.getMemberAction().then((response) => {
        commit('member', response.data)
        return response
      })
    },
    getSettings ({ commit }) {
      return memberApi.getSettingsAction().then((response) => {
        commit('settings', response)
        return response
      })
    },
    getProcessedPaymentOptions (data) {
      return memberApi.getProcessedPaymentOptionsAction(data).then((response) => {
        return response
      })
    },
    getActivePaymentOptions (data) {
      return memberApi.getActivePaymentOptionsAction(data).then((response) => {
        return response
      })
    },
    getProducts ({ commit }) {
      return memberApi.getProductsAction().then((response) => {
        commit('products', response)
        return response
      })
    },
    getNotifications ({ commit }, data) {
      return memberApi.getNotificationsAction(data).then((response) => {
        commit('notifications', response)
        return response
      })
    },
    readNotification () {
      return memberApi.readNotificationAction().then((response) => {
        return response
      })
    }
  },
  getters: {
    member: state => state.member,
    settings: state => state.settings,
    notifications: state => state.notifications
  }
}