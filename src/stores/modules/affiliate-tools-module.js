import banners from '@/api/banner-api'
import affiliate from '@/api/affiliate-api'
import { getField, updateField } from 'vuex-map-fields'

export default {
  namespaced: true,
  state: {
    activeTab: 'generateTab',
    componentKey: 0,
    archivedBanners: {
      data: [],
      pagination: {
        page: 1,
        pageCount: 0
      }
    },
    banners: [],
    form: {
      campaign_name: '',
      website: '',
      referral_code: '',
      type: null,
      language: null,
      banner: null,
      size: null,
    },
    apiErrors: {
      website: [],
      referral_code: [],
    },
    showPreviewModal: false,
    previewedBanner: null,
    valid: false,
    status: '',
    codeStatus: '',
    message: ''
  },
  mutations: {
    updateArchivedBanners (state, archivedBanners) {
      state.archivedBanners = archivedBanners
    },
    clearForm (state) {
      updateField(state, { path: 'form', value: {
        campaign_name: '',
        website: '',
        referral_code: '',
        type: null,
        language: null,
        banner: null,
        size: null,
      }}),
      updateField(state, { path: 'banners', value: [] }),
      updateField(state, { path: 'apiErrors', value: {} })
    },
    updateApiErrors (state, errors) {
      const apiErrors = {
        website: [],
        referral_code: [],
      }

      if (errors.website) apiErrors.website = errors.website
      if (errors.referral_code) apiErrors.referral_code = errors.referral_code
      updateField(state, { path: 'apiErrors', value: apiErrors })
    },
    updateComponentKey (state) {
      state.componentKey++
    },
    done (state, { status, message }) {
      state.status = status
      state.message = message
    },
    updateField
  },
  actions: {
    getBannersForTypeAndLanguage ({ commit }, { type, language }) {
      banners.fetch({ type, language, per_page: 1000 })
        .then(banners => commit('updateField', { path: 'banners', value: banners }))
    },
    preview ({ commit }, banner) {
      commit('updateField', { path: 'previewedBanner', value: banner })
    },
    generateRandomReferralCode ({ commit }) {
      commit('generateRandomReferralCode')
    },
    saveBanner ({ commit, dispatch, rootState }, payload) {
      affiliate.saveBanner(rootState.auth.user.id, payload).then(() => {
        commit('updateField', { path: 'activeTab', value: 'archiveTab' })
        commit('updateField', { path: 'showPreviewModal', value: true })
        commit('clearForm')
        dispatch('preview', payload)
        dispatch('fetchArchivedBanners')
      }).catch((response) => {
        if (response.status === 422) commit('updateApiErrors', response.data.errors)
      })
    },
    fetchArchivedBanners ({ commit, rootState }, page = 1) {
      affiliate.fetchBanners(rootState.auth.user.id, page).then(data => {
        let perPage = 10
        let pageCount = Math.ceil(data.length / perPage) || 1

        let archivedBanners = {
          data,
          pagination: {
            page: page,
            per_page: perPage,
            page_count: pageCount,
          }
        }
        commit('updateArchivedBanners', archivedBanners)
      })
    },
    updateShowPreviewModal ({ commit }) {
      commit('updateField', { path: 'showPreviewModal', value: true })
    },
    clearPreview ({ commit }) {
      commit('updateField', { path: 'previewedBanner', value: null })
    },
    resetForm ({ commit }) {
      commit('clearForm')
    },
    clearApiErrors ({ commit }) {
      commit('updateField', { path: 'apiErrors', value: {} })
    },
    deleteBanner({ commit, rootState }, referralCode) {
      affiliate.deleteBanner(rootState.auth.user.id, referralCode).then(() => {
        const response = { status: 'success', message: 'Banner successfully deleted.' }
        commit('done', response)
        return response
      }).catch((err) => {
        const errorMessage = err.response.data.errors[0].message
        commit('done', { status: 'error', message: errorMessage })
        return Promise.reject(err)
      })
    },
  },
  getters: {
    componentKey: state => state.componentKey,
    banners: state => state.banners,
    sizes: (state) => {
      let banner = state.banners.filter(banner => banner.id === state.form.banner)

      if  (banner.length === 1) {
        banner = banner[0]
        return banner.sizes.split(',')
      }

      return []
    },
    messageModel: state => state.showMessage,
    previewedBanner: state => state.previewedBanner,
    showPreviewModal: state => state.preview,
    bannerHtml: state => state.bannerHtml,
    archivedBanners: state => state.archivedBanners,
    apiErrors: state => state.apiErrors,
    getField
  }
}
