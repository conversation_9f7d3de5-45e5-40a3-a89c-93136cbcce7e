import { createStore } from 'vuex'
import authModule from './modules/auth-module'
import memberModule from './modules/member-module'
import accountModule from './modules/account-module'
import withdrawalModule from './modules/withdrawal-module'
import transactionHistoryModule from './modules/transaction-history-module'
import dashBoardModule from './modules/dashboard-module'
import affiliateToolsModule from './modules/affiliate-tools-module'
import kycModule from './modules/kyc-module'
import gAuthModule from './modules/gauth-module'

const store = createStore({
  modules: {
    auth: authModule,
    account: accountModule,
    member: memberModule,
    withdrawal: withdrawalModule,
    transactionHistory: transactionHistoryModule,
    dashboard: dashBoardModule,
    affiliateTools: affiliateToolsModule,
    kyc: kycModule,
    gAuth: gAuthModule,
  }
})

export default store
