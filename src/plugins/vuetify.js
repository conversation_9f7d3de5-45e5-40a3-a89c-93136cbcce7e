import 'vuetify/styles'
import { createVuetify } from 'vuetify'
import { aliases, fa } from 'vuetify/iconsets/fa'
import 'font-awesome/css/font-awesome.min.css'
import { mdi } from 'vuetify/iconsets/mdi'

import colors from 'vuetify/util/colors'
import { enUS } from 'date-fns/locale'
import DateFnsAdapter from "@date-io/date-fns"
import { VBtn, VChip } from 'vuetify/components'

const buttonVariants = ['Primary', 'Secondary', 'Info', 'Success', 'Warning', 'Error', 'Light']
const buttonDefaults = Object.fromEntries(
  buttonVariants.map(variant => [
    `Btn${variant}`,
    {
      color: variant.toLowerCase(),
      variant: 'elevated',
      class: variant === 'Success' ? 'tw:!font-semibold tw:!tracking-normal tw:!text-white tw:disabled:!text-success-900' : variant === 'Primary' ? 'tw:!font-bold tw:disabled:!bg-disabled tw:disabled:!text-dark/20 tw:!tracking-normal' : variant === 'Light' ? 'tw:!font-semibold tw:!tracking-normal tw:!text-dark/50' : 'tw:!font-semibold tw:!tracking-normal',
    }
  ])
)

const chipVariants = ['Primary', 'Info', 'Success', 'Error', 'Light', 'Bitcoin', 'Neteller', 'USDTTRC20', 'USDTERC20', "USDC"]
const chipDefaults = Object.fromEntries(
  chipVariants.map(variant => [
    `Chip${variant}`,
    {
      color: variant.toLowerCase(),
      variant: 'elevated',
      class: 'tw:!text-white',
      density: 'compact',
    }
  ])
)

export default createVuetify({
  date: {
    adapter: DateFnsAdapter,
    locale: {
      af: enUS,
    },
    firstDayOfWeek: 1,
  },
  display: {
    // Match Tailwind's breakpoints
    thresholds: {
      xs: 0,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      xxl: 1536,
    },
    mobileBreakpoint: 'sm',
  },
  theme: {
    defaultTheme: 'light',
    themes: {
      light: {
        colors: {
          primary: '#FBCC04',
          secondary: '#89B8FA',
          accent: '#8D8F96',
          error: '#FF6060',
          info: '#539AFB',
          success: '#47CC95',
          warning: '#FFC107',
          light: '#EFEFF4',
          dark: '#172530',
          sub: '#99a1af', // this is the same as tw:text-gray-400
          neteller: '#83BA3B',
          skrill: '#862165',
          bitcoin: '#F7931A',
          usdttrc20: '#C1272D',
          usdterc20: '#627FEB',
          usdc: '#627FEB'
        },
      },
      dark: {
        colors: {
          primary: colors.blue.base,
          secondary: colors.amber.base,
          accent: colors.lightBlue.base,
          error: colors.red.base,
          info: colors.blue.lighten3,
          success: colors.green.base,
          warning: colors.orange.base,
          background: colors.indigo.base,
          neteller: '#83BA3B',
          skrill: '#862165',
          bitcoin: '#F7931A',
          usdttrc20: '#C1272D',
          usdterc20: '#627FEB',
          usdc: '#627FEB',
        },
      },
    },
  },
  icons: {
    defaultSet: 'fa',
    aliases,
    sets: {
      fa,
      mdi,
    },
  },
  aliases: {
    BtnPrimary: VBtn,
    BtnSecondary: VBtn,
    BtnInfo: VBtn,
    BtnSuccess: VBtn,
    BtnWarning: VBtn,
    BtnError: VBtn,
    BtnLight: VBtn,
    ChipPrimary: VChip,
    ChipInfo: VChip,
    ChipSuccess: VChip,
    ChipError: VChip,
    ChipLight: VChip,
    ChipBitcoin: VChip,
    ChipNeteller: VChip,
    ChipSkrill: VChip,
    ChipTrc20: VChip,
    ChipErc20: VChip,
    ChipUsdc: VChip,
  },
  defaults: {
    ...buttonDefaults,
    ...chipDefaults,
    VCol: {
      class: "tw:!py-0"
    },
    VMain: {
      class: "tw:!p-0"
    }
  },
})
