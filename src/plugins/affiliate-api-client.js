import axios from 'axios'
import env from '../config'
import store from '../stores'
import Qs from 'qs'

const affiliateApiClient = axios.create({
    baseURL: env.api.affiliateService
})

affiliateApiClient.interceptors.request.use(config => {
  config.paramsSerializer = params => {
    return Qs.stringify(params, {
      arrayFormat: 'brackets',
      encode: false
    })
  }

  return config
})

affiliateApiClient.interceptors.request.use(function (config) {
  let token = store.getters['auth/token']

  if (token === '') {
    token = env.bo.accessToken
  }

  config.headers.authorization = `Bearer ${token}`

  return config
}, function (error) {
  return Promise.reject(error)
})

affiliateApiClient.interceptors.response.use(function (response) {
  return response
}, function (error) {
  if (401 === error.response?.status || 20102 === error.response?.status) {
    const currentPath = window.location.pathname;
    const targetPath = '/auth/login';

    //prevent reloading if already on /auth/login
    if (currentPath !== targetPath) {
      store.dispatch('auth/logout').then(() => {
        location.assign(targetPath)
      })
    }

  } else {
    return Promise.reject(error.response)
  }
})

export default affiliateApiClient;