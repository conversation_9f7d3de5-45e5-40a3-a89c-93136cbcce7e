<template>
  <div>
    <v-data-table-server
      :headers="headers"
      :items="referrals"
      :items-length="paginationData.total"
      :items-per-page="itemsPerPage"
      :page="paginationData.current_page"
      class="tw:border tw:border-gray-100"
    >
      <template #headers="{ columns }">
        <tr class="tw:!border-b-2 tw:!border-b-info">
          <th
            v-for="column in columns"
            :key="column.value"
            class="tw:text-left tw:!font-bold tw:text-info"
          >
            {{ column.title }}
          </th>
        </tr>
      </template>
      <template #item="{ item, index, columns }">
        <tr :class="index % 2 === 0 ? 'tw:bg-gray-100' : 'tw:bg-white'">
          <td
            v-for="column in columns"
            :key="column.value"
            class="tw:!px-4 tw:!py-2 tw:!border-0 tw:!text-sm"
          >
            <span>{{ item[column.value] }}</span>
          </td>
        </tr>
      </template>

      <template #bottom>
        <Pagination
          :data="paginationData"
          @page-change="onPageChange"
        />
      </template>
    </v-data-table-server>
  </div>
</template>

<script setup>
import Pagination from '@/components/shared/LaravelPagination.vue'

defineProps({
  headers: {
    type: Array,
    required: true
  },
  referrals: {
    type: Array,
    required: true
  },
  paginationData: {
    type: Object,
    required: true
  },
  itemsPerPage: {
    type: Number,
    default: 10
  }
})

const emit = defineEmits(['page-change'])

const onPageChange = (newUrl) => {
  emit('page-change', newUrl)
}
</script>