<template>
  <div>
    <v-card
      v-for="(item, index) in referrals"
      :key="index"
      class="tw:!mb-1 tw:shadow-sm tw:rounded-lg"
      :class="index % 2 === 0 ? 'tw:!bg-gray-100' : 'tw:!bg-white'"
    >
      <v-card-text>
        <div class="tw:!py-1 tw:text-xs">
          <div class="tw:font-bold tw:text-info">
            MEMBER ID
          </div>
          <div class="tw:text-sm tw:font-medium">
            {{ item.memberId }}
          </div>
        </div>

        <div class="tw:flex tw:justify-between tw:!py-1 tw:text-xs">
          <div>
            <div class="tw:font-bold tw:text-info">
              DATE REGISTERED
            </div>
            <div>
              {{ item.dateRegistered }}
            </div>
          </div>
          <div class="tw:text-right">
            <div class="tw:font-bold tw:text-info">
              DATE JOINED
            </div>
            <div>
              {{ item.dateJoined }}
            </div>
          </div>
        </div>
      </v-card-text>
    </v-card>

    <div class="tw:!mt-4 tw:!pb-2 tw:flex tw:justify-end tw:w-full">
      <Pagination
        class="tw:w-full"
        :data="paginationData"
        @page-change="onPageChange"
      />
    </div>
  </div>
</template>

<script setup>
import Pagination from '@/components/shared/LaravelPagination.vue'

defineProps({
  referrals: {
    type: Array,
    required: true,
  },
  paginationData: {
    type: Object,
    required: true,
  },
  itemsPerPage: {
    type: Number,
    default: 10
  },
  onPageChange: {
    type: Function,
    default: () => {}
  }
})
</script>