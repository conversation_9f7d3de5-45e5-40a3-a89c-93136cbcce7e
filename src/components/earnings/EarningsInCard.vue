<template>
  <div>
    <v-card
      v-for="(item, index) in commissions"
      :key="index"
      class="tw:!mb-1 tw:shadow-sm tw:rounded-lg"
      :class="index % 2 === 0 ? 'tw:!bg-gray-100' : 'tw:!bg-white'"
    >
      <v-card-text>
        <div class="tw:!py-1 tw:text-xs">
          <div
            class="tw:flex tw:justify-between tw:font-bold"
            :class="type === 'revenue' ? 'tw:text-warning-900' : 'tw:text-success-900'"
          >
            <span>DATE FROM</span>
            <span>DATE TO</span>
          </div>
          <div class="tw:flex tw:justify-between">
            <span>{{ item.dateFrom }}</span>
            <span>{{ item.dateTo }}</span>
          </div>
        </div>

        <div class="tw:!py-1 tw:flex tw:justify-between tw:items-center tw:text-xs">
          <v-chip
            :color="item.status === 'Condition Met' ? 'success' : 'error'"
            :size="$vuetify.display.xs ? 'x-small' : 'small'"
            label
            class="tw:!font-semibold"
            :class="item.status === 'Condition Met' ? 'tw:!border-success-100 tw:!border' : 'tw:!border-danger-100 tw:!border'"
          >
            {{ item.status.toUpperCase() }}
          </v-chip>
          <div class="tw:font-semibold">
            {{ item.commission }}
          </div>
        </div>
      </v-card-text>
    </v-card>

    <div class="tw:!mt-4 tw:!pb-2 tw:flex tw:justify-end tw:w-full">
      <Pagination
        class="tw:w-full"
        :data="paginationData"
        @page-change="onPageChange"
      />
    </div>
  </div>
</template>

<script setup>
import Pagination from '@/components/shared/LaravelPagination.vue'

defineProps({
  commissions: {
    type: Array,
    required: true,
  },
  paginationData: {
    type: Object,
    required: true,
  },
  itemsPerPage: {
    type: Number,
    default: 10
  },
  type: {
    type: String,
    default: 'commission'
  },
  onPageChange: {
    type: Function,
    default: () => {}
  }
})
</script>