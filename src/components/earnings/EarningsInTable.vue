<template>
  <v-data-table-server
    :headers="headers"
    :items="commissions"
    :items-length="paginationData.total"
    :items-per-page="itemsPerPage"
    :page="paginationData.current_page"
    class="tw:border tw:border-gray-100"
  >
    <template #headers="{ columns }">
      <tr
        class="tw:!border-b-2"
        :class="type === 'revenue' ? 'tw:!border-b-warning-900' : 'tw:!border-b-success-900'"
      >
        <th
          v-for="column in columns"
          :key="column.value"
          class="tw:text-left tw:!font-bold"
          :class="type === 'revenue' ? 'tw:text-warning-900' : 'tw:text-success-900'"
        >
          {{ column.title }}
        </th>
      </tr>
    </template>
    <template #item="{ item, index, columns }">
      <tr :class="index % 2 === 0 ? 'tw:bg-gray-100' : 'tw:bg-white'">
        <td
          v-for="column in columns"
          :key="column.value"
          class="tw:!px-4 tw:!py-2 tw:!border-0 tw:!text-sm"
        >
          <v-chip
            v-if="column.value === 'status'"
            :color="item.status === 'Condition Met' ? 'success' : 'error'"
            :size="$vuetify.display.lgAndUp ? 'small' : 'x-small'"
            label
            class="tw:!font-semibold"
            :class="item.status === 'Condition Met' ? 'tw:!border-success-100 tw:!border' : 'tw:!border-danger-100 tw:!border'"
          >
            {{ item.status.toUpperCase() }}
          </v-chip>
          <span v-else>
            {{ item[column.value] }}
          </span>
        </td>
      </tr>
    </template>

    <template #bottom>
      <Pagination
        :data="paginationData"
        @page-change="onPageChange"
      />
    </template>
  </v-data-table-server>
</template>

<script setup>
import Pagination from '@/components/shared/LaravelPagination.vue'

defineProps({
  headers: {
    type: Array,
    default: () => []
  },
  commissions: {
    type: Array,
    required: true,
  },
  paginationData: {
    type: Object,
    required: true,
  },
  itemsPerPage: {
    type: Number,
    default: 10
  },
  type: {
    type: String,
    default: 'commission'
  },
  onPageChange: {
    type: Function,
    default: () => {}
  }
})
</script>