<template>
  <v-sheet>
    <div class="tw:flex tw:items-center">
      <div class="tw:w-full">
        <template v-if="status!=='off'">
          <!-- Reset GAuth Setup -->
          <h2
            v-if="status==='enabled'"
            class="tw:text-lg tw:!mb-[5px] tw:font-bold"
          >
            Google Authenticator Enabled
          </h2>
          <h2
            v-else
            class="tw:text-lg tw:!mb-[5px] tw:font-bold"
          >
            Google Authenticator Disabled
          </h2>
          <span class="gauth-status tw:inline-flex tw:items-center tw:text-sm tw:bg-success tw:text-white tw:font-bold tw:!mb-3 tw:!mr-5">
            <i class="fas fa-shield-check tw:!mr-[5px]" />
            Google Authenticator is activated and set up.
          </span>
          <p
            class="tw:text-sm tw:text-info tw:font-semibold tw:underline tw:cursor-pointer"
            @click="$emit('reset')"
          >
            Reset Authenticator
          </p>
          <!-- End Reset GAuth Setup -->
        </template>
        <template v-else>
          <!-- Enable GAuth Setup -->
          <h2 class="tw:text-lg tw:!mb-[5px] tw:font-semibold">
            Enable Google Authenticator
          </h2>
          <p class="tw:text-sm tw:text-gray-500">
            Set up authenticator
          </p>
          <!-- End Enable GAuth Setup -->
        </template>
      </div>
      <template v-if="loading">
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </template>
      <v-switch
        v-else
        v-model="enabled"
        color="success"
        @click="toggled"
      />
    </div>
  </v-sheet>
</template>

<script>

export default {
  name: 'GAuthStatus',
  props: {
    status: {
      type: String,
      default: 'off'
    },
  },
  emits: ['initiate', 'reset', 'statusToggled'],
  data() {
    return {
      enabled: false,
      loading: false,
    }
  },
  watch: {
    status: {
      immediate: true,
      handler(val) {
        this.enabled = val === 'enabled'
      },
    },
  },
  methods: {
    toggled() {
      this.enabled = !this.enabled;
      if (this.status === 'off') {
        this.$emit('statusToggled', this.enabled)
      } else {
         
        this.loading = true
        const dispatchType = this.enabled ? 'enable' : 'disable'
        this.$store.dispatch(`gAuth/${dispatchType}`)
            .then(() => {
              this.$emit('statusToggled', this.enabled)
            })
            .finally(() => {
              this.loading = false
            })
      }
    },
  },
}
</script>

<style scoped>
.gauth-status {
  border-radius: 25px;
  padding: 5px 10px;
}
</style>
