<template>
  <v-sheet>
    <!-- Reset GAuth Setup -->
    <div v-if="status!=='off' && showReset">
      <p class="tw:text-sm">
        In order to reset Google Authenticator you have to input OTP that will be sent to your email.
      </p>
      <div
        id="verificationForm"
        class="gateway-inner-wrapper mt-5"
      >
        <v-form
          ref="form"
          @submit.prevent
        >
          <v-text-field
            ref="textField"
            v-model="resetVerificationCode"
            :disabled="loadingGetOtp || loadingReset"
            type="text"
            required
            variant="outlined"
            label="Enter OTP Code"
            class="verification-field"
            density="compact"
          />
          <btn-primary
            type="button"
            class="tw:!font-bold tw:!absolute tw:right-0 tw:top-0"
            :disabled="loadingGetOtp || loadingReset"
            @click.prevent="getOtp"
          >
            Get OTP
          </btn-primary>
          <div class="tw:float-right">
            <btn-success
              block
              class="request-btn"
              :loading="loadingReset"
              :disabled="!resetVerificationCode || loadingReset"
              @click="reset()"
            >
              Reset Authenticator
            </btn-success>
          </div>
        </v-form>
      </div>
    </div>
    <!-- End Reset GAuth Setup -->

    <get-code-success-dialog
      ref="childGetCodeSuccess"
      title="Check your email"
      message="We have sent the verification code to"
      :recipient="email"
      :open="dialog"
    />
    <success-dialog
      ref="childSuccess"
      title="Success!"
      message="Google Authenticator has been successfully reset."
      :open="successDialog"
      to-page="/settings"
      :is-persistent="true"
    />
    <error-dialog
      ref="childError"
      title="Error!"
      message="Invalid code. Google Authenticator reset failed."
      :open="errorDialog"
      to-page=""
    />
  </v-sheet>
</template>

<script>
  import storage from '@/utility/storage'
  import ErrorDialog from '@/components/elements/dialog/ErrorDialog.vue'
  import GetCodeSuccessDialog from '@/components/elements/dialog/GetCodeSuccessDialog.vue'
  import SuccessDialog from '@/components/elements/dialog/SuccessDialog.vue'

  export default {
    name: 'GAuthReset',

    components: {
      ErrorDialog,
      GetCodeSuccessDialog,
      SuccessDialog
    },
    props: {
      status: {
        type: String,
        default: 'off'
      },
      qrCode: {
        type: String,
        default: ''
      },
      secretKey: {
        type: String,
        default: ''
      },
      showReset: {
        type: Boolean,
        default: false
      },
      resetSuccess: {
        type: Boolean,
        default: false
      },
    },
    data () {
      return {
        showGetCodeDialog: false,
        showErrorDialog: false,
        errorMessage: null,
        showSuccessDialog: false,
        verificationCode: null,
        resetVerificationCode: null,
        loadingReset: false,
        loadingGetOtp: false,
        email: storage.getObject('member').user.email,
      }
    },
    methods: {
      getOtp() {
        this.loadingGetOtp = true
        const email=this.email
        const purpose='reset-totp'
        this.$store.dispatch('account/getCode', {email, purpose}).then(() => {
          this.showGetCodeDialog = true
          this.showGetCodeModal()
        }).catch(() => {
          this.showErrorDialog = true
          this.errorMessage = 'Invalid code.'
          this.$refs.childError.setOpen(true)
        }).finally(() => {
          this.loadingGetOtp = false
        })
      },
      reset() {
        this.loadingReset = true
        const otp = this.resetVerificationCode
        this.$store.dispatch('gAuth/reset', { otp }).then(() => {
          this.showSuccessModal()
        }).catch(() => {
          this.showErrorDialog = true
          this.$refs.childError.setOpen(true)
        }).finally(() => {
          this.loadingReset = false
        })
      },
      resetValidation() {
        this.$refs.textField.resetValidation();
        this.showErrorDialog = false
        this.errorMessage = null
      },
      showGetCodeModal() {
        this.showGetCodeDialog = true
        this.$refs.childGetCodeSuccess.setOpen(true)
      },
      showSuccessModal() {
        this.showSuccessDialog = true
        this.$refs.childSuccess.setOpen(true)
      },
    }
  }

</script>
<style scoped>
.gateway-inner-wrapper {
  position: relative;
}

@media only screen and (min-width: 768px) {
  .request-btn {
    max-width: 200px !important;
    min-width: 200px !important;
    width: 200px;
    display: inline-block;
  }
}
</style>
