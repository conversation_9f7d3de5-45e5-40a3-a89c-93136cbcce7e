<template>
  <v-sheet>
    <template v-if="status==='off'">
      <!-- End Enable GAuth Setup -->
      <!-- Step 1 -->
      <div class="steps-container tw:flex">
        <span class="steps-icon tw:flex tw:items-center tw:justify-center tw:text-lg tw:bg-warning/20 tw:text-warning tw:!mr-5">
          <i class="fas fa-download" />
        </span>
        <div class="tw:!pb-5 tw:border-b-gray-200 tw:border-b tw:w-full">
          <p class="tw:text-sm tw:text-gray-500 tw:font-semibold tw:uppercase">
            Step 1
          </p>
          <p class="tw:text-sm">
            Download and install Google Authenticator on your mobile phone.
          </p>
          <div class="step-badge tw:flex tw:!mt-5">
            <a
              class="mr-[5px]"
              href="https://apps.apple.com/us/app/google-authenticator/id388497605"
              target="_blank"
            >
              <img
                src="../../assets/gauth/badge-app-store.svg"
                alt="Download in App Store"
              >
            </a>
            <a
              href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2"
              target="_blank"
            >
              <img
                src="../../assets/gauth/badge-play-store.svg"
                alt="Download in Google Play Store"
              >
            </a>
          </div>
        </div>
      </div>
      <!-- End Step 1 -->

      <!-- Step 2 -->
      <div class="steps-container tw:flex tw:!pt-5">
        <span class="steps-icon tw:flex tw:items-center tw:justify-center tw:text-lg tw:bg-info/20 tw:text-info tw:!mr-5">
          <i class="fas fa-qrcode" />
        </span>
        <div class="tw:!pb-5 tw:border-b-gray-200 tw:border-b tw:w-full">
          <p class="tw:text-sm tw:text-gray-500 tw:font-semibold tw:uppercase">
            Step 2
          </p>
          <p class="tw:text-sm">
            Scan the QR code or enter the secret key on your Google Authenticator app.
          </p>
          <div class="step-qr-code tw:flex tw:!mt-5">
            <div class="tw:flex tw:flex-col">
              <span class="tw:text-sm tw:text-gray-500 tw:font-semibold tw:uppercase tw:!mb-[5px]">Scan QR Code</span>
              <img
                v-if="qrCode"
                :src="qrCode"
                alt="Sample QR Code"
              >
              <div
                v-else
                class="tw:w-[20%] tw:float-right"
              >
                <v-progress-circular
                  indeterminate
                  color="primary"
                />
              </div>
            </div>
            <div class="tw:flex tw:items-center tw:justify-center tw:text-sm tw:text-gray-500 tw:font-semibold tw:uppercase tw:!p-5">
              OR
            </div>
            <div class="tw:flex tw:flex-col">
              <span class="tw:text-sm tw:text-gray-500 tw:font-semibold tw:uppercase tw:!mb-[5px]">Secret Key</span>
              <p
                v-if="secretKey"
                class="step-secret-key tw:text-sm tw:bg-info/20 tw:text-info tw:font-bold"
              >
                {{ secretKey }}
              </p>
              <div
                v-else
                class="tw:w-[20%] tw:float-right"
              >
                <v-progress-circular
                  indeterminate
                  color="primary"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- End Step 2 -->

      <!-- Step 3 -->
      <div class="steps-container tw:flex tw:!pt-5">
        <span class="steps-icon tw:flex tw:items-center tw:justify-center tw:text-lg tw:bg-success/20 tw:text-success tw:!mr-5">
          <i class="fas fa-shield-check" />
        </span>
        <div class="tw:w-full">
          <p class="tw:text-sm tw:text-gray-500 tw:font-semibold tw:uppercase">
            Step 3
          </p>
          <p class="tw:text-sm">
            Input the 6-digit code generated on your Google Authenticator app.
          </p>
          <div
            id="verificationForm"
            class="gateway-inner-wrapper tw:!mt-5"
          >
            <v-form
              ref="form"
              v-model="valid"
              @submit.prevent
            >
              <v-text-field
                ref="textField"
                v-model="verificationCode"
                :disabled="loadingVerifyCode"
                type="text"
                required
                variant="outlined"
                density="compact"
                label="Enter 6-digit code"
                class="verification-field"
              />
              <btn-primary
                type="submit"
                class="get-code-btn"
                :loading="loadingVerifyCode"
                @click="onVerifyCode()"
              >
                Verify Code
              </btn-primary>
            </v-form>
          </div>
        </div>
      </div>
      <!-- End Step 3 -->
      <!-- End Enable GAuth Setup -->
    </template>

    <g-auth-setup-dialog
      ref="childReminder"
      title="Google Authenticator has been enabled on your withdrawal transactions"
      message="Please save the backup codes:"
      :backup-codes="recoveryCodes"
    />
    <error-dialog
      ref="childError"
      title="Error!"
      message="Invalid code. Please check and try again."
      :open="showErrorDialog"
      to-page=""
    />
  </v-sheet>
</template>

<script>
import GAuthSetupDialog from '@/components/elements/dialog/GAuthSetupDialog.vue'
import { mapGetters } from 'vuex'
import storage from '@/utility/storage'
import ErrorDialog from '@/components/elements/dialog/ErrorDialog.vue'

export default {
  name: 'GAuthSetup',
  components: {
    ErrorDialog,
    GAuthSetupDialog,
  },
  props: {
    status: {
      type: String,
      default: 'off'
    },
    qrCode: {
      type: String,
      default: ''
    },
    secretKey: {
      type: String,
      default: ''
    },
    showReset: {
      type: Boolean,
      default: false
    },
    resetSuccess: {
      type: Boolean,
      default: false
    },
  },
  data () {
    return {
      valid: true,
      gAuthSetupDialog: false,
      showErrorDialog: false,
      errorMessage: null,
      loadingVerifyCode: false,
      verificationCode: null,
      recoveryCodes: null,
      resetVerificationCode: null,
      loadingReset: false,
      loadingGetOtp: false,
      email: storage.getObject('member').user.email,
    }
  },
  computed: {
    ...mapGetters('gAuth', ['getConfirm']),
  },
  watch: {
    resetSuccess (value) {
      if (value) {
        this.showSuccessDialog = true
      }
    }
  },
  methods: {
    onVerifyCode () {
      this.loadingVerifyCode = true
      this.$store
          .dispatch('gAuth/confirm', { 'code': this.verificationCode })
          .then(() => {
            this.recoveryCodes = this.getConfirm['recovery_codes'].join(', ')
            this.showGAuthSetupSuccessModal()
          })
          .catch(() => {
            this.showErrorDialog = true
            this.errorMessage = 'Invalid code.'
            this.$refs.childError.setOpen(true)
          })
          .finally(() => {
            this.loadingVerifyCode = false
          })
    },
    showGAuthSetupSuccessModal () {
      this.gAuthSetupDialog = true
      this.$refs.childReminder.setOpen(true)
    },
    resetValidation() {
      this.$refs.textField.resetValidation();
      this.showErrorDialog = false
      this.errorMessage = null
    },
  },
}
</script>

<style scoped>
.steps-container .steps-icon {
  min-width: 40px;
  width: 40px;
  min-height: 40px;
  height: 40px;
  border-radius: 50%;
}

.steps-container .step-badge img {
  height: 40px;
}

.steps-container .step-qr-code img {
  width: 120px;
  height: auto;
}

.step-secret-key {
  border-radius: 25px;
  padding: 5px 10px;
}

.step-qr-code {
  flex-direction: column;
}

.gateway-inner-wrapper {
  position: relative;
}

.get-code-btn {
  position: absolute;
  right: 0;
  top: 0;
  border-radius: 0 5px 5px 0;
}

@media only screen and (min-width: 768px) {
  .step-qr-code {
    flex-direction: row;
  }

  .request-btn {
    max-width: 200px !important;
    min-width: 200px !important;
    width: 200px;
    display: inline-block;
  }
}
</style>
