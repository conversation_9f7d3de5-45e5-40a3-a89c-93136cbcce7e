<template>
  <v-container class="tw:h-auto tw:bg-white tw:!p-5 tw:rounded-lg">
    <!-- pending screen -->
    <div
      v-if="status === 'pending'"
      class="screen-container tw:text-center"
    >
      <div class="screen-img-container">
        <img src="../../assets/kyc-loading.svg">
      </div>
      <h1 class="tw:!mt-4 pending-text tw:font-bold">
        Verification is in progress.
      </h1>
      <p class="tw:!mt-3 pending-subtext">
        Please wait for a few minutes. Account status will automatically update once verification is completed.
      </p>
    </div>
    <!-- pending screen end -->

    <!-- success screen -->
    <div
      v-if="status === 'success'"
      class="screen-container tw:text-center"
    >
      <div class="screen-img-container">
        <img src="../../assets/kyc-success.svg">
      </div>
      <h1 class="tw:!mt-4 success-text tw:font-bold">
        Congratulations!
      </h1>
      <p class="success-subtext">
        Your account is verified.
      </p>
    </div>
    <!-- success screen end -->

    <!-- denied screen -->
    <div
      v-if="status === 'failed'"
      class="screen-container"
    >
      <div class="screen-img-container">
        <img src="../../assets/kyc-denied.svg">
      </div>
      <h1 class="tw:!mt-4 text-black tw:text-center tw:font-semibold">
        We’re sorry, your account verification is denied.
      </h1>
      <p class="black-subtext tw:!mt-4 tw:!mb-3">
        You may resubmit your application any time. Before you do, please review and carefully follow our requirements to have your account successfully verified next time.
      </p>
      <ul class="faqs">
        <li class="blue-redirect tw:!mb-2">
          <a
            href="https://www.piwi247.com/faq/why-is-my-account-verification-denied"
            class="blue-redirect tw:font-bold"
            target="”_blank”"
          >Why is my account verification denied?</a>
        </li>
        <li class="blue-redirect tw:!mb-2">
          <a
            href="https://www.piwi247.com/faq/what-are-the-kyc-verification-requirements-and-guidelines"
            class="blue-redirect tw:font-bold"
            target="”_blank”"
          >What are the KYC verification requirements and guidelines?</a>
        </li>
        <li class="blue-redirect tw:!mb-2">
          <a
            href="https://www.piwi247.com/faq/how-can-i-have-my-account-verified-as-affiliate"
            class="blue-redirect"
            target="”_blank”"
          >How can I have my account verified?</a>
        </li>
      </ul>
      <!-- verify button -->
      <div class="tw:text-center tw:!mt-5">
        <btn-primary
          :disabled="disableVerifyButton"
          class="tw:!m-auto"
          @click="$emit('initiate')"
        >
          VERIFY NOW
        </btn-primary>
      </div>
      <!-- verify button end -->
    </div>
    <!-- denied screen end -->

    <!-- manual verification screen -->
    <div
      v-if="status === 'manual_verification'"
      class="screen-container"
    >
      <div class="screen-img-container">
        <img src="../../assets/kyc-manual-verification.svg">
      </div>
      <h1 class="tw:!mt-4 text-black tw:text-center tw:font-semibold">
        Thank you for your patience. Your account needs further verification.
      </h1>
      <p class="black-subtext tw:!mt-4 tw:!mb-3">
        We need additional information from you to complete the verification of your account. Our KYC team will reach out to you to request the additional information or documents needed. Alternatively, you may contact our Support Team if you don’t hear from us in 24hrs.
      </p>
    </div>
    <!-- manual verification end -->
  </v-container>
</template>

<script>
export default {
  name: 'KycStatus',
  props: {
    status: {
      type: String,
      default: 'pending'
    },
    disableVerifyButton: {
      type: Boolean,
      default: false
    }
  },
  emits: ['initiate'],
}
</script>

<style scoped>
.theme--light.v-sheet{
  background-color: unset
}

.verification-process-content{
  border-radius: 15px;
  box-shadow: 1px 2px 8px #eaeaea!important;
}

.verification-process-title{
  padding: unset;
  font-weight: bold;
  font-size: 20px;
}

.verification-requirements{
  margin: 30px 60px;
}

.step-container{
  margin-bottom: 50px;
  height: 110px;
  width: 215px;
  border: 3px solid white;
  border-radius: 20px;
  background: #EDF6FE !important;
  box-shadow: 1px 2px 8px #eaeaea!important;
  color: #172530;
  font-size: 13px;
}

.step-container p{
  padding: 28px 20px;
}

.icon-container{
  display: flex;
  justify-content: center;
  position: absolute;
  width:50px;
  height:50px;
  margin-top: -30px;
  margin-left: 20px;
  border-radius: 15px!important;
  background: #2196F3;
  border: 3px solid #97D1FF;
  text-align: center;
}

.icon-container img{
  width: 25px;
}

.red-icon .icon-container{
  background: #F32121;
  border: 3px solid #FF9B9B;
}

.kyc-personal-info-container{
  margin: 20px 60px;
  padding: 2% 2%;
  background: #EDF6FE !important;
  border: 2px solid white;
  border-radius: 15px;
  box-shadow: 1px 2px 8px #eaeaea!important;
  font-size: 13px;
  color: #20303C;
}

.personal-info{
  margin-bottom: 10px;
}

/* checkbox css */
.checkbox-container{
  display: flex;
  align-items: center;
  margin: auto;
  padding: 10px 20px;
  width: 300px;
  border-radius: 10px;
  background-color: #D1EBFE;
}

.checkbox-container label {
  font-size: 14px;
  font-style: italic;
  font-weight: 600;
  color: #66A4FA;
}

.checkbox-container input{
  margin-right: 10px;
  margin-top: -2px;
}
/* checkbox css end */

.blue-redirect{
  font-size: 15px;
  font-weight: bold;
  text-decoration: none;
  color: #66A4FA;
}

/* verify button css */
.theme--light.v-btn:not(.v-btn--flat):not(.v-btn--text):not(.v-btn--outlined){
  padding: 0 40px;
  background-color: #FBCB36;
  border-radius: 10px;
  color: white;
}
/* verify button css end */

.screen-img-container{
  display: flex;
  justify-content: center;
  margin-top: 70px;
}

.screen-img-container img{
  width: 500px;
}

.pending-text{
  font-size: 20px;
  color: #66A4FA;
}

.pending-subtext, .text-black{
  color: #20303C;
}

.success-text, .success-subtext{
  color:#4E9A7A;
}

.success-text, .text-black{
  font-size: 20px;
}

.black-subtext {
  color: #324555;
  width: 725px;
  margin: auto;
}

.faqs{
  display: block;
  justify-content: center;
  width: 740px;
  margin: auto;
}

@media screen and (max-width:600px) {
  .verification-process-content-container, .step-col{
    padding: unset !important;
  }

  .verification-requirements{
    margin: 20px;
  }

  .step-container{
    height: 90px;
    margin-bottom: unset;
    margin: 5px;
    font-size: 12px ;
  }

  .step-container p{
    padding: 25px 10px;
  }

  .step-col{
    display: inline-flex;
    margin-top: 20px;
    margin-bottom: 5px;
  }

  .checkbox-container,.black-subtext,.faqs{
    width: 100%;
  }

  .kyc-personal-info-container{
    margin: 10px 0px;
    padding: 15px;
    font-size: 12px;
  }

  .personal-info{
    margin: unset;
  }

  .pending-subtext{
    font-size: 13px;
  }

}

@media screen and (min-width: 600px) and (max-width: 768px) {
  .step-col{
    display: inline-flex;
    max-width: 100% !important;
    padding: 5px;
  }

  .step-col .step-container{
    margin: 20px 10px;
  }

  .verification-requirements{
    margin: 10px 30px;
  }

  .kyc-personal-info-container{
    margin: 20px 20px;
  }

  .black-subtext{
    width:100%;
  }

  .v-btn__content{
    font-weight: bold;
    font-size: 15px;
  }
}
</style>