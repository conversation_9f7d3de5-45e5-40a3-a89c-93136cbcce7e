<template>
  <v-container class="verification-content tw:bg-white tw:!p-5 tw:rounded-lg">
    <div class="verification-status">
      <div class="verification-process">
        <v-row>
          <v-col
            cols="12"
            lg="7"
          >
            <v-container class="verification-process-content-container">
              <!-- first container -->
              <v-card class="verification-process-content">
                <div class="verification-process-title-container tw:bg-info tw:!p-4">
                  <v-card-title class="verification-process-title tw:text-white">
                    VERIFICATION PROCESS
                  </v-card-title>
                  <v-card-subtitle class="verification-process-subtitle tw:text-white tw:!text-wrap">
                    To start the verification process, please confirm the following:
                  </v-card-subtitle>
                </div>
                <div class="verification-requirements">
                  <v-row class="verification-requirements-row tw:!mb-3">
                    <v-col
                      cols="6"
                      md="4"
                      class="step-col"
                    >
                      <v-card class="step-container">
                        <div class="icon-container">
                          <img src="../../assets/camera-web-solid.svg">
                        </div>
                        <p>I have a webcam either on my phone or laptop.</p>
                      </v-card>
                    </v-col>

                    <v-col
                      cols="6"
                      md="4"
                      class="step-col"
                    >
                      <v-card class="step-container">
                        <div class="icon-container">
                          <img src="../../assets/id-card-solid.svg">
                        </div>
                        <p>I have my national ID card, passport, or driving license with me.</p>
                      </v-card>
                    </v-col>

                    <v-col
                      cols="6"
                      md="4"
                      class="step-col"
                    >
                      <v-card class="step-container">
                        <div class="icon-container">
                          <img src="../../assets/lightbulb-on-solid.svg">
                        </div>
                        <p>I have adequate lighting.</p>
                      </v-card>
                    </v-col>

                    <v-col
                      cols="6"
                      md="4"
                      class="step-col"
                    >
                      <v-card class="step-container">
                        <div class="icon-container">
                          <img src="../../assets/face-viewfinder-solid.svg">
                        </div>
                        <p>My appearance is similar to the photo on my document.</p>
                      </v-card>
                    </v-col>

                    <v-col
                      cols="6"
                      md="4"
                      class="step-col"
                    >
                      <v-card class="step-container">
                        <div class="icon-container">
                          <img src="../../assets/image-user-solid.svg">
                        </div>
                        <p>There are no other people in my background.</p>
                      </v-card>
                    </v-col>

                    <v-col
                      cols="6"
                      md="4"
                      class="step-col"
                    >
                      <v-card class="step-container red-icon">
                        <div class="icon-container">
                          <img src="../../assets/glasses-solid.svg">
                        </div>
                        <p>I am not wearing a hat or eye glasses.</p>
                      </v-card>
                    </v-col>
                  </v-row>
                  <!-- checkbox  -->
                  <div class="checkbox-container">
                    <input
                      id="checkbox1"
                      v-model="confirm1"
                      type="checkbox"
                      name="checkbox1"
                      value="checked1"
                    >
                    <label for="checkbox1">I confirm all of the above</label><br>
                  </div>
                  <!-- checkbox end -->
                  <div class="tw:!mt-2">
&nbsp;
                  </div>
                </div>
              </v-card>
              <!-- first container end -->
              <!-- second container -->
              <v-container class="verification-process-content tw:!mt-8">
                <v-card class="kyc-personal-info-container">
                  <p class="tw:!mb-3">
                    The personal information we have collected will be shared with a third-party GDPR compliant fraud prevention agency who will use it to prevent fraud and verify your identity. We will also compare your selfie video with the photo on your identity document to confirm that they match.
                  </p>
                  <p>Please note that you must give consent as described in order for us to verify your identity and continue using our service.</p>
                </v-card>
                <div class="personal-info verification-requirements">
                  <!-- checkbox -->
                  <div class="checkbox-container">
                    <input
                      id="checkbox2"
                      v-model="confirm2"
                      type="checkbox"
                      name="checkbox2"
                      value="checked2"
                    >
                    <label for="checkbox2">I confirm all of the above</label><br>
                  </div>
                  <!-- checkbox end -->
                </div>
              </v-container>
              <!-- second container end -->
            </v-container>
          </v-col>
          <!-- left containers end -->
          <!-- right container -->
          <v-col
            cols="12"
            lg="5"
          >
            <img src="../../assets/kycpage.svg">
            <p class="my-5">
              You may also want to visit the <b><a
                class="blue-redirect"
                href="https://www.piwi247.com/faq/tag/kyc-verification"
                target="”_blank”"
              >FAQ > KYC Verification</a></b> section if you have any questions on our KYC process.
            </p>
            <p class="my-5">
              You may also want to check:
            </p>
            <a
              href="https://www.piwi247.com/faq/what-are-the-kyc-verification-requirements-and-guidelines"
              class="blue-redirect"
              target="”_blank”"
            >What are the KYC Verification requirements and guidelines?</a>
            <br>
            <!-- verify button -->
            <div class="tw:text-center tw:!mt-5 verify-btn-desk tw:hidden tw:sm:block">
              <btn-primary
                class="tw:!m-auto"
                :disabled="disabled"
                @click="$emit('initiate')"
              >
                VERIFY NOW
              </btn-primary>
            </div>
            <!-- verify button end -->
          </v-col>
          <!-- right container -->
        </v-row>
        <!-- verify button -->
        <div class="tw:text-center tw:!mt-7 tw:w-full tw:block tw:sm:hidden">
          <btn-primary
            class="tw:!m-auto"
            block
            :disabled="disabled"
            @click="$emit('initiate')"
          >
            VERIFY NOW
          </btn-primary>
        </div>
        <!-- verify button end -->
      </div>
    </div>
  </v-container>
</template>

<script>
import {createHelpers} from 'vuex-map-fields';

const { mapFields } = createHelpers({
  getterType: 'kyc/getField',
  mutationType: 'kyc/updateField',
})

export default {
  name: 'KycForm',
  props: {
    disableVerifyButton: {
      type: Boolean,
      default: false
    }
  },
  emits: ['initiate'],
  computed: {
    ...mapFields([
      'confirm1',
      'confirm2',
    ]),
    disabled () {
      return this.confirm1 === false || this.confirm2 === false || this.disableVerifyButton
    }
  },
}
</script>

<style scoped>
.theme--light.v-sheet{
  background-color: unset
}

.verification-process-content{
  border-radius: 15px;
  box-shadow: 1px 2px 8px #eaeaea!important;
}

.verification-process-title{
  padding: unset;
  font-weight: bold;
  font-size: 20px;
}

.verification-process-subtitle{
  padding: 0;
  margin-top: -5px;
}

.verification-requirements {
  margin: 20px 30px 0; /* Reduced side margins */
}

.step-col {
  display: flex;
  justify-content: center;
  padding: 15px; /* Add padding for spacing */
  margin-top: 45px; /* Space for icon overflow */
}

.step-container {
  margin-bottom: 0; /* Remove bottom margin */
  min-height: 110px;
  height: 130px;
  width: 100%; /* Make it responsive */
  max-width: 215px;
  border: 3px solid white;
  border-radius: 20px;
  background: #EDF6FE !important;
  box-shadow: 1px 2px 8px #eaeaea!important;
  color: #172530;
  font-size: 13px;
  overflow: visible;
  display: flex;
  align-items: center;
  position: relative;
}

@media screen and (max-width: 640px) {
  .step-container {
    margin: 0 2px !important;
  }

  .icon-container {
    margin-top: -110px !important;
  }
}

.step-container p {
  padding: 0 10px;
  margin: 0;
  width: 100%;
  text-align: center;
}

.icon-container{
  display: flex;
  justify-content: center;
  position: absolute;
  width:50px;
  height:50px;
  margin-top: -130px;
  left: 15px;
  border-radius: 15px!important;
  background: #2196F3;
  border: 3px solid #97D1FF;
  text-align: center;
}

.icon-container img{
  width: 25px;
}

.red-icon .icon-container{
  background: #F32121;
  border: 3px solid #FF9B9B;
}

.kyc-personal-info-container{
  margin: 20px 60px;
  padding: 2% 2%;
  background: #EDF6FE !important;
  border: 2px solid white;
  border-radius: 15px;
  box-shadow: 1px 2px 8px #eaeaea!important;
  font-size: 13px;
  color: #20303C;
}

.personal-info{
  margin-bottom: 10px;
}

/* checkbox css */
.checkbox-container{
  display: flex;
  align-items: center;
  margin: 20px auto 0;
  padding: 10px 20px;
  width: 300px;
  border-radius: 10px;
  background-color: #D1EBFE;
}

.checkbox-container label {
  font-size: 14px;
  font-style: italic;
  font-weight: 600;
  color: #66A4FA;
}

.checkbox-container input{
  margin-right: 10px;
  margin-top: -2px;
}
/* checkbox css end */

.blue-redirect{
  font-size: 15px;
  font-weight: bold;
  text-decoration: none;
  color: #66A4FA;
}

/* verify button css */
.theme--light.v-btn:not(.v-btn--flat):not(.v-btn--text):not(.v-btn--outlined){
  padding: 0 40px;
  background-color: #FBCB36;
  border-radius: 10px;
  color: white;
}
/* verify button css end */

.screen-img-container{
  display: flex;
  justify-content: center;
  margin-top: 70px;
}

.screen-img-container img{
  width: 500px;
}

.pending-text{
  font-size: 20px;
  color: #66A4FA;
}

.pending-subtext, .text-black{
  color: #20303C;
}

.success-text, .success-subtext{
  color:#4E9A7A;
}

.success-text, .text-black{
  font-size: 20px;
}

.black-subtext {
  color: #324555;
  width: 725px;
  margin: auto;
}

.faqs{
  display: block;
  justify-content: center;
  width: 740px;
  margin: auto;
}

button{
  font-weight: bold !important;
}

@media screen and (max-width:600px) {
  .verification-process-content-container, .step-col{
    padding: unset !important;
  }

  .verification-requirements {
    margin: 30px 30px;
  }

  .step-col {
    padding: 10px;
    margin-top: 25px;
  }

  .step-container {
    min-height: 90px;
    height: 90px;
    font-size: 12px;
    margin: 0;
  }

  .step-container p {
    padding: 0 10px;
  }

  .step-col{
    display: inline-flex;
    margin: 50px 0 5px;
  }

  .checkbox-container,.black-subtext,.faqs{
    width: 100%;
  }

  .kyc-personal-info-container{
    margin: 10px 0px;
    padding: 15px;
    font-size: 12px;
  }

  .personal-info{
    margin: unset;
  }

  .pending-subtext{
    font-size: 13px;
  }

  .verify-btn-mob{
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100%;
    padding: 2%;
    margin: auto;
  }

  .verify-btn-mob button{
  width: 90%;
  }

}

@media screen and (min-width: 600px) and (max-width: 768px) {
  .verification-requirements {
    margin: 30px 15px;
  }

  .step-col {
    padding: 10px;
    margin-top: 10px;
  }

  .step-container {
    margin: 0;
  }

  .step-col{
    display: inline-flex;
    max-width: 100% !important;
    padding: 5px;
  }

  .step-col .step-container{
    margin: 20px 10px;
  }

  .kyc-personal-info-container{
    margin: 20px 20px;
  }

  .black-subtext{
    width:100%;
  }

  .v-btn__content{
    font-weight: bold;
    font-size: 15px;
  }
}

@media screen and (min-width: 768px) and (max-width: 1024px) {
  .verification-requirements {
    margin: 40px 20px 0;
  }

  .step-col {
    padding: 12px;
    margin-top: 35px;
  }
}
</style>
