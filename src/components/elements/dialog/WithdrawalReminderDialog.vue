<template>
  <v-dialog
    v-model="withdrawalReminderDialog"
    persistent
  >
    <div class="get-code-wrapper custom-card-shadow tw:bg-white">
      <div class="tw:!p-5 tw:text-center tw:flex tw:flex-col tw:items-center">
        <img
          src="../../../assets/withdrawal-reminder.svg"
          class="tw:!mt-3"
        >
        <h1 class="tw:text-lg tw:!mt-5 tw:!mb-3 tw:font-bold">
          {{ title }}
        </h1>
        <p class="tw:text-sm">
          {{ message }}
        </p>
        <p class="tw:!mt-5">
          <span
            class="tw:text-gray-500 tw:text-sm tw:underline tw:cursor-pointer"
            @click="withdrawalReminderDialog = false"
          >Dismiss</span>
        </p>
      </div>
    </div>
  </v-dialog>
</template>

<script>
  export default {
    name: "WithdrawalReminderDialog",
    props: {
      open: {
        type: Boolean,
        default: false
        
      },
      title: {
        type: String,
        default: ''
      },
      message: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        withdrawalReminderDialog: false,
      }
    },
    methods: {
      setOpen (state) {
        this.withdrawalReminderDialog = state
      }
    }
  }
</script>

<style scoped>
  .get-code-wrapper {
    max-width: 70%;
    width: 70%;
    margin: 0 auto;
  }
  .get-code-wrapper img {
    width: 100px;
  }
  @media only screen and (min-width: 768px) {
    .get-code-wrapper {
      width: 25%;
    }
    .get-code-wrapper img {
      width: 120px;
    }
  }
</style>
