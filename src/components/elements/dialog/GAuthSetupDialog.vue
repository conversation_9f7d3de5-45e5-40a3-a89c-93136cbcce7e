<template>
  <v-dialog
    v-model="gAuthSetupDialog"
    persistent
  >
    <div class="tw:!mx-auto tw:max-w-[75%] custom-card-shadow tw:bg-white get-code-wrapper">
      <div class="tw:!p-5 tw:flex tw:justify-center tw:flex-col tw:items-center">
        <img
          src="../../../assets/gauth/gauth-resetup-icon.svg"
          class="tw:!mt-3"
        >
        <h1 class="tw:text-base tw:!mt-5 tw:!mb-3 tw:font-bold tw:text-center">
          {{ title }}
        </h1>
        <p class="tw:text-sm tw:text-center">
          {{ message }}
        </p>
        <div class="tw:!mt-5">
          <span class="step-secret-key tw:text-sm tw:text-info tw:bg-info/20 tw:font-bold">{{ backupCodes }}</span>
        </div>
        <div class="tw:!mt-[15px]">
          <span
            class="gray-d-text tw:text-sm tw:underline tw:cursor-pointer"
            @click="onClose"
          >Dismiss</span>
        </div>
      </div>
    </div>
  </v-dialog>
</template>

<script>
  export default {
    name: "GAuthSetupDialog",
    props: {
      open: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: ''
      },
      message: {
        type: String,
        default: ''
      },
      backupCodes: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        gAuthSetupDialog: false,
      }
    },
    methods: {
      setOpen (state) {
        this.gAuthSetupDialog = state
      },
      onClose() {
        this.gAuthSetupDialog = false
        this.$router.push({name: 'settings'})
      }
    }
  }
</script>

<style scoped>
  .get-code-wrapper {
    max-width: 70%;
    width: 70%;
    margin: 0 auto;
  }
  .get-code-wrapper img {
    width: 100px;
  }
  .step-secret-key {
    border-radius: 25px;
    padding: 5px 10px;
  }
  @media only screen and (min-width: 768px) {
    .get-code-wrapper {
      width: 25%;
    }
    .get-code-wrapper img {
      width: 120px;
    }
  }
</style>
