<template id="childGetCodeSuccess">
  <v-dialog
    v-model="dialog"
    persistent
  >
    <div class="get-code-wrapper custom-card-shadow tw:bg-white">
      <div class="tw:!p-5 tw:text-center tw:flex tw:flex-col tw:items-center">
        <img
          src="../../../assets/success_inbox.svg"
          class="tw:!mt-3"
        >
        <h1 class="tw:text-lg tw:!mt-5 tw:!mb-3 tw:font-bold">
          {{ title }}
        </h1>
        <p class="tw:text-sm">
          {{ message }} <span class="tw:font-bold">{{ recipient }}</span>
        </p>
        <p class="tw:!mt-5">
          <span
            class="tw:text-gray-500 tw:text-sm tw:underline tw:cursor-pointer"
            @click="dialog = false"
          >Dismiss</span>
        </p>
      </div>
    </div>
  </v-dialog>
</template>

<script>
  export default {
    name: "GetCodeSuccessDialog",
    props: {
      open: {
        type: Boolean,
        default: false
      },
      recipient: {
        type: String,
        default: ''
      },
      title: {
        type: String,
        default: 'Success'
      },
      message: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        dialog: false,
        openDialog: ''
      }
    },
    methods: {
      setOpen (state) {
        this.dialog = state
      }
    }
  }
</script>

<style scoped>
  .get-code-wrapper {
    max-width: 70%;
    width: 70%;
    margin: 0 auto;
  }
  .get-code-wrapper img {
    width: 100px;
  }
  @media only screen and (min-width: 768px) {
    .get-code-wrapper {
      width: 25%;
    }
  }
</style>
