<template>
  <v-dialog
    v-model="errorDialog"
    persistent
  >
    <div class="error-wrapper custom-card-shadow tw:bg-white">
      <div class="tw:!p-5 tw:text-center tw:flex tw:flex-col tw:items-center">
        <img
          src="../../../assets/decline-general.svg"
          class="tw:!mt-3"
        >
        <h1 class="tw:text-lg tw:!my-3 tw:font-bold">
          {{ title }}
        </h1>
        <p class="tw:text-sm">
          {{ message }}
        </p>
        <p
          v-if="toPage"
          class="tw:!mt-5"
        >
          <router-link
            :to="toPage"
            class="tw:text-gray-500 tw:text-sm tw:underline tw:cursor-pointer"
          >
            Dismiss
          </router-link>
        </p>
        <p
          v-else
          class="tw:!mt-2"
        >
          <span
            class="tw:text-gray-500 tw:text-sm tw:underline tw:cursor-pointer"
            @click="errorDialog = false"
          >Dismiss</span>
        </p>
      </div>
    </div>
  </v-dialog>
</template>

<script>
  export default {
    name: "ErrorDialog",
    props: {
      toPage: {
        type: String,
        default: ''
      },
      open: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: ''
      },
      message: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        errorDialog: false
      }
    },
    methods: {
      setOpen (state) {
        this.errorDialog = state
      }
    }
  }
</script>

<style scoped>
  .error-wrapper {
    max-width: 70%;
    width: 70%;
    margin: 0 auto;
  }
  .error-wrapper img {
    width: 80px;
  }
  @media only screen and (min-width: 768px) {
    .error-wrapper {
      width: 25%;
    }
    .error-wrapper img {
      width: 110px;
    }
  }
</style>
