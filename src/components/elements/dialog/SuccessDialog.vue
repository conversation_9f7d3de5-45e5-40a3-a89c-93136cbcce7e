<template>
  <v-dialog
    v-model="successDialog"
    :persistent="isPersistent"
  >
    <div class="success-wrapper custom-card-shadow tw:bg-white">
      <div class="tw:!p-5 tw:text-center tw:flex tw:flex-col tw:items-center">
        <img
          src="../../../assets/success-general.svg"
          class="tw:!mt-3"
        >
        <h1 class="tw:text-lg tw:!mt-5 tw:!mb-3 tw:font-bold">
          {{ title }}
        </h1>
        <p class="tw:text-sm">
          {{ message }}
        </p>
        <p
          v-if="toPage"
          class="tw:!mt-5"
        >
          <router-link
            :to="toPage"
            class="tw:text-gray-500 tw:text-sm tw:underline tw:cursor-pointer"
          >
            Dismiss
          </router-link>
        </p>
        <p
          v-else
          class="tw:!mt-5"
        >
          <span
            class="tw:text-gray-500 tw:text-sm tw:underline"
            @click="successDialog = false"
          >Dismiss</span>
        </p>
      </div>
    </div>
  </v-dialog>
</template>

<script>
  export default {
    name: "SuccessDialog",
    props: {
      toPage: {
        type: String,
        default: ''
      },
      open: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: ''
      },
      message: {
        type: String,
        default: ''
      },
      isPersistent: {
        type: Boolean,
        default: () => false
      }
    },
    data() {
      return {
        successDialog: false
      }
    },
    methods: {
      setOpen (state) {
        this.successDialog = state
      }
    }
  }
</script>

<style scoped>
  .success-wrapper {
    max-width: 70%;
    width: 70%;
    margin: 0 auto;
  }
  .success-wrapper img {
    width: 80px;
  }
  @media only screen and (min-width: 768px) {
    .success-wrapper {
      width: 25%;
    }
    .success-wrapper img {
      width: 110px;
    }
  }
</style>
