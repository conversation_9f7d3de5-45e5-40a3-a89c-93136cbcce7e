<template>
  <div class="tw:drop-shadow-xs tw:bg-white tw:!px-2 tw:!py-3 tw:flex tw:justify-between tw:items-center">
    <!-- <PERSON><PERSON> (Mobile) -->
    <button 
      class="tw:drop-shadow-sm tw:!p-1 tw:md:hidden tw:!bg-primary tw:rounded-full tw:w-7 tw:h-7 tw:grow-0 tw:shrink-0 tw:flex tw:items-center tw:justify-center"
      @click="emit('open-drawer', true)"
    >
      <i class="fas fa-bars tw:text-xs tw:text-dark" />
    </button>

    <!-- Left side - Page Title -->
    <h1 class="tw:text-dark tw:text-base tw:md:text-lg tw:font-bold tw:md:!pl-3 tw:!pl-2">
      {{ title }}
    </h1>

    <!-- Right side - Actions -->
    <div class="tw:flex tw:items-center tw:md:!space-x-5 tw:!space-x-3">
      <!-- Language Selector -->
      <div class="tw:items-center tw:justify-center tw:md:flex tw:hidden">
        <img
          src="@/assets/language/flag-en.svg"
          alt="English"
          class="tw:w-6 tw:h-4 tw:!mr-2"
        >
        <v-select
          v-model="selectedLanguage"
          :items="languages"
          item-title="title"
          item-value="value"
          slim
          density="compact"
          hide-details
          class="tw:w-[70px] tw:!-mt-[8px] tw:!text-sm"
          color="primary"
          base-color="sub"
          variant="underlined"
        />
      </div>

      <!-- Notification Bell -->
      <div class="tw:relative tw:cursor-pointer">
        <i
          class="fas fa-bell tw:text-gray-400 tw:md:text-lg tw:text-sm"
          @click="readNotification"
        />
        <div 
          v-if="counter > 0"
          class="tw:absolute tw:top-[-3px] tw:right-[-10px] tw:z-10 tw:flex tw:items-center" 
        >
          <div class="tw:w-[10px] tw:h-[10px] tw:bg-warning tw:rounded-full" />
          <span class="tw:text-warning tw:text-xs">{{ counter }}</span>
        </div>
      </div>

      <!-- Profile Menu -->
      <v-menu>
        <template #activator="{ props }">
          <div
            class="tw:flex tw:items-center tw:text-gray-500"
            v-bind="props"
          >
            <v-icon
              icon="fas fa-user-circle"
              :size="$vuetify.display.mdAndUp ? 30 : 22"
              color="secondary"
            />
            <div class="tw:!mx-2 tw:md:flex tw:hidden tw:flex-col">
              <span class="tw:text-sm tw:font-semibold tw:text-dark">{{ member?.full_name }}</span>
              <span class="tw:!-mt-0.5 tw:text-xs">{{ member?.user.email }}</span>
            </div>
            <v-icon
              class="tw:md:!ml-0 tw:!ml-1"
              icon="fas fa-caret-down"
              :size="$vuetify.display.mdAndUp ? 15 : 12"
              color="sub"
            />
          </div>
        </template>
        <v-list
          class="tw:!mt-2"
          density="compact"
        >
          <v-list-item :to="{ name: 'settings' }">
            <v-list-item-title>
              <i class="fas fa-cog tw:!mr-2" /> Settings
            </v-list-item-title>
          </v-list-item>
          <v-list-item @click="logout">
            <v-list-item-title>
              <i class="fas fa-sign-out-alt tw:!mr-2" /> Logout
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import storage from '../../utility/storage'

const store = useStore()
const router = useRouter()
const member = ref(null)
const counter = ref(0)
const selectedLanguage = ref('en')
const languages = [
  { title: 'EN', value: 'en' }
]

defineProps({
  title: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['open-drawer'])

onMounted(() => {
  loadMemberData()
  countNotifications([])
})

const loadMemberData = async () => {
  try {
    const memberData = localStorage.getItem('member')
    member.value = memberData ? JSON.parse(memberData) : null
  } catch (error) {
    console.error('Error loading member data:', error)
  }
}

const countNotifications = (data) => {
  let count = 0
  let notifs = data
  
  if (!data || !data.length) {
    notifs = storage.getObject('notifications')
  }
  
  if (notifs && notifs.length) {
    for (let value of notifs) {
      if (!value.read) {
        count++
      }
    }
  }
  
  counter.value = count
}

watch(() => store.getters['member/notifications'], (newNotifications) => {
  countNotifications(newNotifications)
}, { immediate: true })

const readNotification = () => {
  router.push('/notification')
  if (counter.value > 0) {
    store.dispatch('member/readNotification')
  }
}

const logout = () => {
  store.dispatch('auth/logout')
    .then(() => router.push({ name: 'auth-login' }))
}
</script>
