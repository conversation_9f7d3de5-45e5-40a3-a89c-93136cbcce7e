<template>
  <v-navigation-drawer
    class="tw:h-screen tw:flex-shrink-0 tw:md:relative tw:fixed tw:z-50"
    color="dark"
    :temporary="!$vuetify.display.mdAndUp"
    :permanent="$vuetify.display.mdAndUp"
    width="245"
    @update:model-value="emit('close-drawer', false)"
  >
    <!-- Logo -->
    <div class="tw:!p-4">
      <img
        src="@/assets/piwi-logo.svg"
        alt="PIWI247"
        class="tw:w-32"
      >
    </div>

    <!-- Main Navigation -->
    <v-list
      nav
      class="tw:!mt-3"
    >
      <v-list-item
        v-for="item in mainNavItems"
        :key="item.name"
        :to="{ name: item.name }"
        :prepend-icon="item.icon + ' tw:!text-primary'"
        :title="item.title"
        class="tw:text-white tw:!mb-2 hover:tw:bg-dark-200"
        active-class="tw:bg-dark-200"
      />
    </v-list>

    <!-- Divider -->
    <div class="tw:!px-4 tw:!py-3">
      <div class="tw:text-gray-400 tw:text-xs tw:font-medium">
        TRANSACTION
      </div>
    </div>

    <!-- Secondary Navigation -->
    <v-list nav>
      <v-list-item
        v-for="item in transactionNavItems"
        :key="item.name"
        :to="{ name: item.name }"
        :prepend-icon="item.icon + ' tw:!text-primary'"
        :title="item.title"
        class="tw:text-white tw:!mb-2 hover:tw:bg-dark-200"
        active-class="tw:bg-dark-200"
      />
    </v-list>

    <!-- Divider -->
    <div class="tw:!px-4 tw:!py-3">
      <div class="tw:text-gray-400 tw:text-xs tw:font-medium">
        TOOLS AND SETTINGS
      </div>
    </div>

    <!-- Secondary Navigation -->
    <v-list nav>
      <v-list-item
        v-for="item in toolsNavItems"
        :key="item.name"
        :to="{ name: item.name }"
        :prepend-icon="item.icon + ' tw:!text-primary'"
        :title="item.title"
        class="tw:text-white tw:!mb-2 hover:tw:bg-dark-200"
        active-class="tw:bg-dark-200"
      />
    </v-list>
  </v-navigation-drawer>
</template>

<script setup>
const emit = defineEmits(['close-drawer'])

const mainNavItems = [
  {
    name: 'dashboard',
    title: 'Dashboard',
    icon: 'fas fa-columns'
  },
  {
    name: 'earnings',
    title: 'Earnings by Period',
    icon: 'fas fa-calendar-alt'
  },
  {
    name: 'commission',
    title: 'Commission',
    icon: 'fas fa-percentage'
  },
  {
    name: 'revenue-share',
    title: 'Revenue Share',
    icon: 'fas fa-chart-pie'
  },
  {
    name: 'referrals',
    title: 'Referrals',
    icon: 'fas fa-user-plus'
  },
]

const transactionNavItems = [
  {
    name: 'withdrawal',
    title: 'Withdrawal',
    icon: 'fas fa-wallet'
  },
  {
    name: 'transaction-history',
    title: 'Transaction History',
    icon: 'fas fa-history'
  },
  {
    name: 'statement-of-account',
    title: 'Statement of Account',
    icon: 'fas fa-file-invoice'
  }
]

const toolsNavItems = [
  {
    name: 'affiliate-tools',
    title: 'Affiliate Tools',
    icon: 'fas fa-tools'
  },
  {
    name: 'kyc',
    title: 'KYC',
    icon: 'fas fa-user-shield'
  },
  {
    name: 'settings',
    title: 'Settings',
    icon: 'fas fa-cog'
  },
  {
    name: 'contact',
    title: 'Contact Us',
    icon: 'fas fa-envelope'
  }
]
</script>

<style scoped>
:deep(.v-navigation-drawer) {
  transform: translateX(0) !important;
}

:deep(.v-list-item__prepend) {
  width: 24px;
  margin-right: 12px;
}

:deep(.v-list-item) {
  min-height: 40px;
  padding: 0 16px;
}

/* Custom scrollbar styling */
:deep(.v-navigation-drawer__content) {
  scrollbar-width: thin;
  scrollbar-color: #172530 #172530;
}

:deep(.v-navigation-drawer__content::-webkit-scrollbar) {
  width: 8px;
}

:deep(.v-navigation-drawer__content::-webkit-scrollbar-track) {
  background: #172530;
}

:deep(.v-navigation-drawer__content::-webkit-scrollbar-thumb) {
  background-color: #172530;
  border-radius: 4px;
  border: 2px solid #172530;
}
</style>
