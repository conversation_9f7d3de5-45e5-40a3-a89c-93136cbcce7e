<template>
  <div 
    :class="[colorVariants[type].card]"
    class="tw:lg:text-base tw:text-sm tw:relative tw:rounded-lg tw:!px-4 tw:!py-3 tw:flex tw:items-start tw:justify-between tw:gap-2"
  >
    <div class="tw:z-2 tw:max-w-[80%]">
      <div class="tw:font-semibold">
        {{ title }}
      </div>
      <div class="tw:font-bold">
        {{ value }} <span class="tw:text-xs tw:font-semibold">{{ useCurrency ? 'EUR' : '' }}</span>
      </div>
    </div>
    <div
      v-if="useCurrency || icon"
      class="tw:flex tw:items-center tw:justify-center tw:absolute tw:top-1/2 tw:-translate-y-1/2"
      :class="[
        icon
          ? 'tw:sm:w-14 tw:sm:h-14 tw:h-8 tw:w-8 tw:right-2'
          : 'tw:!pr-0.5 tw:sm:w-12 tw:sm:h-12 tw:w-8 tw:h-8 tw:rounded-full tw:right-4',
        !icon ? colorVariants[type].iconBg : ''
      ]"
    >
      <v-icon
        :icon="icon || 'far fa-euro-sign'"
        :class="[
          icon ? `${colorVariants[type].otherIcon} tw:sm:!text-4xl tw:!text-2xl` : colorVariants[type].currencyIcon
        ]"
        :size="!icon && $vuetify.display.smAndUp ? 'x-large' : 'medium'"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: String,
    required: true
  },
  useCurrency: {
    type: Boolean,
    default: true
  },
  icon: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'commission'
  }
})

const colorVariants = computed(() => ({
  commission: {
    card: 'tw:bg-success-50 tw:text-success-900',
    iconBg: 'tw:bg-success-200',
    currencyIcon: 'tw:!text-success-50',
    otherIcon: 'tw:!text-success-200',
  },
  revenue: {
    card: 'tw:bg-warning-50 tw:text-warning-900',
    iconBg: 'tw:bg-warning-200',
    currencyIcon: 'tw:!text-warning-50',
    otherIcon: 'tw:!text-warning-200',
  },
  registration: {
    card: 'tw:bg-registration-50 tw:text-registration-900',
    iconBg: 'tw:bg-registration-200',
    currencyIcon: 'tw:!text-registration-50',
    otherIcon: 'tw:!text-registration-200',
  },
  conversion: {
    card: 'tw:bg-conversion-50 tw:text-conversion-900',
    iconBg: 'tw:bg-conversion-200',
    currencyIcon: 'tw:!text-conversion-50',
    otherIcon: 'tw:!text-conversion-200',
  }

}))
</script>
