<template>
  <vue-date-picker
    v-model="date"
    :range="useRange"
    :placeholder="useRange ? 'Start Date - End Date' : undefined"
    :enable-time-picker="!useRange"
    :multi-calendars="useRange"
    :teleport-center="useRange && viewportWidth < 768"
  />
</template>

<script setup>
import { onMounted, ref } from 'vue';
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'

defineProps({
  useRange: {
    type: Boolean,
    default: false
  }
})

const viewportWidth = ref(window.innerWidth)

onMounted(() => {
  window.addEventListener('resize', () => {
    viewportWidth.value = window.innerWidth
  })
})

const date = ref();
</script>