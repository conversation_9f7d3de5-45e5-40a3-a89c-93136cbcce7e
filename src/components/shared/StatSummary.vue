<template>
  <div class="tw:flex tw:justify-between tw:items-end">
    <div class="tw:hidden tw:sm:flex tw:!space-x-2 tw:flex-1">
      <stat-card
        v-for="item in summaryData"
        :key="item.title"
        class="tw:max-w-[280px] tw:sm:flex-1"
        :title="item.title"
        :value="item.value"
        :icon="item.icon"
        :use-currency="item.useCurrency"
        :type="type"
      />
    </div>
    <div 
      :class="[colorVariants[type].card]"
      class="tw:rounded-lg tw:!px-4 tw:!py-3 tw:flex tw:sm:hidden tw:flex-1 tw:flex-col tw:!space-y-1.5 tw:justify-center"
    >
      <div
        v-for="item in summaryData"
        :key="item.title"
        :class="[colorVariants[type].text]"
        class="tw:flex tw:justify-between tw:items-center tw:text-xs tw:font-bold"
      >
        <div class="tw:flex tw:items-center">
          <div 
            :class="item.useCurrency ? `${colorVariants[type].iconBg} tw:w-6 tw:h-6 tw:rounded-full tw:flex tw:items-center tw:justify-center` : ''"
          >
            <v-icon
              :icon="item.useCurrency ? 'far fa-euro-sign' : item.icon"
              class="tw:!mr-1"
              :class="[item.useCurrency ? colorVariants[type].currencyIcon : colorVariants[type].otherIcon]"
              :size="item.useCurrency ? 'small' : 'large'"
            />
          </div>
          <div class="tw:font-bold tw:!ml-1">
            {{ item.title }}
          </div>
        </div>
        <div class="tw:font-bold tw:w-1/3">
          {{ item.value }} <span class="tw:text-xs tw:font-semibold">{{ item.useCurrency ? 'EUR' : '' }}</span>
        </div>
      </div>
      <export-json
        class="tw:sm:!hidden tw:!block tw:!mt-1.5"
        :file-name="exportName"
        :data="formatAndExport()"
        block
        flat
        size="default"
      >
        Export CSV
      </export-json>
    </div>
    <export-json
      class="tw:!ml-2 tw:sm:!block tw:!hidden"
      :file-name="exportName"
      :data="formatAndExport()"
      size="small"
      flat
    >
      Export CSV
    </export-json>
  </div>
</template>

<script setup>
import StatCard from './StatCard.vue';
import ExportJson from './ExportJson.vue';
import { computed } from 'vue';

const props = defineProps({
  summaryData: {
    type: Array,
    default: () => []
  },
  exportName: {
    type: String,
    default: 'Summary'
  },
  type: {
    type: String,
    default: 'commission'
  }
})

const colorVariants = computed(() => ({
  commission: {
    card: 'tw:bg-success-50',
    text: 'tw:text-success-900',
    iconBg: 'tw:bg-success-200',
    currencyIcon: 'tw:!text-success-50',
    otherIcon: 'tw:!text-success-200',
  },
  revenue: {
    card: 'tw:bg-warning-50',
    text: 'tw:text-warning-900',
    iconBg: 'tw:bg-warning-200',
    currencyIcon: 'tw:!text-warning-50',
    otherIcon: 'tw:!text-warning-200',
  }
}))

const formatAndExport = () => {
  const data = {};

  props.summaryData.forEach(item => {
    data[item.title] = item.value;
  });

  return [data];
}
</script>
