<template>
  <div class="tw:flex tw:items-start tw:!space-x-2 tw:text-gray-500 tw:text-xs">
    <div class="tw:!mt-2.5">
      SHOW
    </div>
    <div class="tw:min-w-[70px]">
      <v-select
        v-model="itemsPerPage"
        :items="options"
        item-title="title"
        item-value="value"
        slim
        density="compact"
        hide-details
        class="tw:!text-sm"
        color="primary"
        variant="outlined"
      />
    </div>
    <div class="tw:!mt-2.5">
      ENTRIES
    </div>
  </div>
</template>

<script setup>
const itemsPerPage = defineModel({ type: Number, default: 10 });

defineProps({
  options: {
    type: Array,
    default: () => [10, 20, 50, 100]
  }
})
</script>
