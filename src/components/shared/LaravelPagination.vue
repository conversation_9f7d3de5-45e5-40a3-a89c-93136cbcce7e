<template>
  <nav class="tw:flex tw:flex-wrap tw:items-end tw:sm:items-center tw:sm:flex-row tw:flex-col tw:justify-end tw:sm:justify-between tw:bg-white tw:!px-4 tw:!py-4 tw:sm:!px-6">
    <div class="tw:sm:!mb-0 tw:!mb-2 tw:self-center">
      <p class="tw:text-xs tw:text-gray-500">
        Showing
        <span class="tw:font-medium">{{ data.from }}</span>
        to
        <span class="tw:font-medium">{{ data.to }}</span>
        of
        <span class="tw:font-medium">{{ data.total }}</span>
        results
      </p>
    </div>
    <div class="tw:flex">
      <!-- Previous Button -->
      <button
        v-if="data.prev_page_url"
        class="tw:!cursor-pointer tw:rounded-s-md tw:relative tw:inline-flex tw:items-center tw:!px-2.5 tw:!py-1 tw:sm:!px-3 tw:sm:!py-2 tw:text-sm tw:font-semibold tw:text-gray-900 tw:ring-1 tw:ring-inset tw:ring-gray-300 tw:hover:!bg-gray-50"
        @click="navigatePage(data.prev_page_url)"
      >
        <v-icon
          icon="fas fa-chevron-left"
          class="tw:!text-gray-400"
          size="x-small"
        />
      </button>

      <template
        v-for="(page, index) in formattedPages"
      >
        <button
          v-if="page.type === 'page'"
          :disabled="!page.url"
          :class="[
            'tw:!cursor-pointer tw:relative tw:inline-flex tw:items-center tw:!px-2.5 tw:!py-1 tw:sm:!px-3 tw:sm:!py-2 tw:text-sm tw:font-semibold',
            page.active
              ? 'tw:z-10 tw:!bg-info tw:!text-white tw:focus:z-20 tw:focus-visible:outline tw:focus-visible:outline-offset-2 tw:focus-visible:outline-indigo-600'
              : 'tw:text-gray-900 tw:ring-1 tw:ring-inset tw:ring-gray-300 tw:hover:!bg-gray-50 tw:focus:z-20 tw:focus:outline-offset-0',
            !data.prev_page_url && index === 0 ? 'tw:rounded-s-md' : 'tw:rounded-none',
            !data.next_page_url && index === formattedPages.length - 1 ? 'tw:rounded-e-md' : 'tw:rounded-none',
          ]"
          @click="navigatePage(page.url)"
        >
          {{ page.label }}
        </button>
        
        <!-- Ellipsis -->
        <button
          v-else
          class="tw:!px-2.5 tw:!py-1 tw:sm:!px-3 tw:sm:!py-2 tw:text-sm tw:font-semibold tw:text-gray-900 tw:ring-1 tw:ring-inset tw:ring-gray-300 tw:hover:!bg-gray-50 tw:focus:z-20 tw:focus:outline-offset-0"
        >
          ...
        </button>
      </template>

      <!-- Next Button -->
      <button
        v-if="data.next_page_url"
        class="tw:rounded-e-md tw:relative tw:inline-flex tw:items-center tw:!px-2.5 tw:!py-1 tw:sm:!px-4 tw:sm:!py-2 tw:text-sm tw:font-semibold tw:text-gray-900 tw:ring-1 tw:ring-inset tw:ring-gray-300 tw:hover:!bg-gray-50"
        @click="navigatePage(data.next_page_url)"
      >
        <v-icon
          icon="fas fa-chevron-right"
          class="tw:!text-gray-400"
          size="x-small"
        />
      </button>
    </div>
  </nav>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['page-change']);

/**
 * Navigates to a new page.
 * @param url - The URL of the new page.
 */
const navigatePage = (url) => {
  if (url) emit('page-change', url);
};

/**
 * Compute the formatted pagination links with "..."
 */
const formattedPages = computed(() => {
  const totalPages = props.data.last_page;
  const currentPage = props.data.current_page;
  const links = props.data.links;

  const displayedPages = 1;
  const result = [];

  const firstPageLink = links.find(link => link.label === '1');
  result.push({ label: '1', url: firstPageLink?.url || null, active: currentPage === 1, type: 'page' });

  if (currentPage > displayedPages + 2) {
    result.push({ label: '...', url: null, active: false, type: 'ellipsis' });
  }

  // Show current page, previous, and next (but not first/last)
  for (let i = Math.max(2, currentPage - displayedPages); i <= Math.min(totalPages - 1, currentPage + displayedPages); i++) {
    const link = links.find(link => link.label === `${i}`);
    result.push({ label: `${i}`, url: link?.url || null, active: currentPage === i, type: 'page' });
  }

  if (currentPage < totalPages - displayedPages - 1) {
    result.push({ label: '...', url: null, active: false, type: 'ellipsis' });
  }

  if (totalPages > 1) {
    const lastPageLink = links.find(link => link.label === `${totalPages}`);
    result.push({ label: `${totalPages}`, url: lastPageLink?.url || null, active: currentPage === totalPages, type: 'page' });
  }

  return result;
});
</script>
