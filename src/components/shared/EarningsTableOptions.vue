<template>
  <div>
    <div
      v-if="$vuetify.display.smAndUp"
      class="tw:flex tw:justify-between tw:items-center tw:!mb-3 tw:flex-wrap tw:!space-y-1"
    >
      <div class="tw:flex tw:items-center tw:gap-1.5 tw:flex-wrap">
        <v-select
          v-model="selectedYear"
          :items="years"
          label="Year"
          class="tw:w-24"
          density="compact"
          variant="outlined"
          hide-details
        />

        <v-select
          v-model="selectedPeriod"
          :items="periods"
          label="Period"
          class="tw:w-40"
          density="compact"
          hide-details
          variant="outlined"
        />

        <div class="tw:flex tw:items-center tw:!space-x-2">
          <btn-success
            flat
            @click="applyFilter"
          >
            APPLY FILTER
          </btn-success>

          <btn-light
            flat
            @click="resetFilter"
          >
            RESET FILTER
          </btn-light>
        </div>
      </div>
      <div class="tw:w-50">
        <v-text-field
          v-model="search"
          prepend-inner-icon="fas fa-search"
          label="Search"
          class="tw:flex-1"
          density="compact"
          hide-details
          variant="outlined"
          @blur="searchCommissions"
          @keyup.enter="searchCommissions"
        />
      </div>
    </div>
    <div
      v-else
      class="tw:flex tw:flex-col tw:!space-y-2.5 tw:!mb-3 tw:flex-wrap"
    >
      <div class="tw:flex tw:justify-between">
        <btn-info
          flat
          prepend-icon="fas fa-sliders-h tw:!text-white"
          @click="showFilters = !showFilters"
        >
          {{ showFilters ? "Hide" : "Show" }} FILTERS
        </btn-info>
        <div class="tw:flex-1 tw:!ml-5 tw:max-w-50">
          <v-text-field
            v-model="search"
            prepend-inner-icon="fas fa-search"
            label="Search"
            class="tw:flex-1"
            density="compact"
            hide-details
            variant="outlined"
            @blur="searchCommissions"
            @keyup.enter="searchCommissions"
          />
        </div>
      </div>
      <div
        v-if="showFilters"
        class="tw:flex tw:flex-col tw:!space-y-2"
      >
        <div class="tw:flex tw:items-center tw:gap-1.5 tw:flex-wrap tw:!mb-3">
          <v-select
            v-model="selectedYear"
            :items="years"
            label="Year"
            class="tw:w-24"
            density="compact"
            variant="outlined"
            hide-details
          />

          <v-select
            v-model="selectedPeriod"
            :items="periods"
            label="Period"
            class="tw:w-40"
            density="compact"
            hide-details
            variant="outlined"
          />
        </div>
        <div class="tw:flex tw:items-center tw:!space-x-2">
          <btn-success
            class="tw:flex-1"
            flat
            @click="applyFilter"
          >
            APPLY FILTER
          </btn-success>

          <btn-light
            class="tw:flex-1"
            flat
            @click="resetFilter"
          >
            RESET FILTER
          </btn-light>
        </div>
      </div>
    </div>
    <div class="tw:flex tw:justify-between tw:items-center tw:!mb-3">
      <v-checkbox
        :model-value="shouldHideZeroTurnover"
        label="Hide Zero Turnover"
        class="tw:!ml-0 tw:!mr-4"
        density="compact"
        hide-details
        @update:model-value="shouldHideZeroTurnover = !shouldHideZeroTurnover"
      />
      <number-of-items-select
        v-model="itemsPerPage"
        :options="[10, 20, 50, 100]"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import NumberOfItemsSelect from '@/components/shared/NumberOfItemsSelect.vue'

const props = defineProps({
  years: {
    type: Array,
    default: () => []
  },
  periods: {
    type: Array,
    default: () => []
  },
})

const emit = defineEmits(['filter', 'search'])

const showFilters = ref(false)
const selectedYear = defineModel('selectedYear', { default: new Date().getFullYear().toString(), type: String })
const selectedPeriod = defineModel('selectedPeriod', { default: 'All', type: String })
const itemsPerPage = defineModel('itemsPerPage', { default: 10, type: Number })
const search = defineModel('search', { default: '', type: String })
const shouldHideZeroTurnover = defineModel('shouldHideZeroTurnover', { default: false, type: Boolean })

const applyFilter = () => {
  emit('filter', {
    year: selectedYear.value,
    period: selectedPeriod.value,
    itemsPerPage: itemsPerPage.value,
    search: search.value,
  })
}

const resetFilter = async () => {
  selectedYear.value = new Date().getFullYear().toString()
  selectedPeriod.value = props.periods[0]
  itemsPerPage.value = 10
  search.value = ''
  shouldHideZeroTurnover.value = false

  await nextTick()
  applyFilter()
}

const searchCommissions = () => {
  emit('search', search.value)
}
</script>
