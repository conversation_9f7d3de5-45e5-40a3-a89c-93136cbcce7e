<template>
  <v-data-table-server
    :headers="headers"
    :items="commissions"
    :items-length="paginationData.total"
    :items-per-page="itemsPerPage"
    :page="paginationData.current_page"
    class="tw:border tw:border-gray-100"
  >
    <template #headers="{ columns }">
      <tr
        :class="['tw:!border-b-2', colorVariants[type].border]"
      >
        <th
          v-for="column in columns"
          :key="column.value"
          :class="['tw:text-left', 'tw:!font-bold', colorVariants[type].text]"
        >
          {{ column.title }}
        </th>
      </tr>
    </template>
    <template #item="{ item, index, columns }">
      <tr :class="index % 2 === 0 ? 'tw:bg-gray-100' : 'tw:bg-white'">
        <td
          v-for="column in columns"
          :key="column.value"
          class="tw:!px-4 tw:!py-2 tw:!border-0 tw:!text-sm"
        >
          <template v-if="column.value === 'product'">
            <div class="tw:flex tw:items-center tw:gap-2">
              <div class="tw:w-6.5 tw:h-6.5 tw:rounded tw:bg-white tw:flex tw:items-center tw:justify-center tw:border tw:border-gray-50">
                <img
                  :src="item.productIcon"
                  :alt="item[column.value]"
                  class="tw:w-4.5 tw:h-4.5"
                >
              </div>
              <span class="tw:font-medium">{{ item[column.value] }}</span>
            </div>
          </template>
          <template v-else>
            <span>
              {{ item[column.value] }}
            </span>
          </template>
        </td>
      </tr>
    </template>

    <template #bottom>
      <Pagination
        :data="paginationData"
        @page-change="onPageChange"
      />
    </template>
  </v-data-table-server>
</template>

<script setup>
import Pagination from '@/components/shared/LaravelPagination.vue'
import { computed } from 'vue'

defineProps({
  headers: {
    type: Array,
    default: () => []
  },
  commissions: {
    type: Array,
    required: true,
  },
  paginationData: {
    type: Object,
    required: true,
  },
  itemsPerPage: {
    type: Number,
    default: 10
  },
  onPageChange: {
    type: Function,
    default: () => {}
  },
  type: {
    type: String,
    default: 'commission'
  }
})

const colorVariants = computed(() => ({
  commission: {
    border: 'tw:!border-b-success-900',
    text: 'tw:text-success-900',
  },
  revenue: {
    border: 'tw:!border-b-warning-900',
    text: 'tw:text-warning-900',
  }
}))
</script>
