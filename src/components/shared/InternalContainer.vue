<template>
  <div class="tw:flex tw:h-screen tw:overflow-x-hidden">
    <!-- Navigation Drawer -->
    <nav-drawer
      v-model="drawer"
      class="tw:flex-shrink-0 tw:hidden tw:md:block"
      @close-drawer="onCloseDrawer"
    />
    
    <!-- Main Content Area -->
    <div class="tw:flex-1 tw:flex tw:flex-col tw:md:!ml-[245px]">
      <!-- Top Bar -->
      <header>
        <top-bar
          :title="title"
          :is-inner="isInner"
          class="tw:w-full"
          @open-drawer="onOpenDrawer"
        />
      </header>

      <!-- Main Content -->
      <div 
        ref="mainEl" 
        class="tw:overflow-auto tw:flex tw:flex-col tw:bg-gray-100 tw:!px-2 tw:flex-1 tw:sm:!px-4 tw:md:!px-16 tw:!pb-[70px] tw:!py-4 tw:sm:!py-6 tw:md:!py-14 tw:md:!mb-0"
      >
        <slot name="main" />
        <div class="tw:!mx-12 tw:sm:!mx-5 tw:flex-1 tw:items-end tw:justify-center tw:flex tw:text-center tw:!mt-10 tw:!pb-5 tw:text-xs tw:sm:text-sm tw:text-gray-400">
          PIWI Affiliate | &copy; Copyright 2019 - {{ new Date().getFullYear() }}. All rights reserved.
        </div>
      </div>

      <!-- Footer -->
      <footer>
        <slot name="footer" />
      </footer>

      <btn-primary
        v-show="showScrollToTopBtn"
        class="tw:!fixed tw:!text-white tw:!m-4 tw:!z-50"
        :size="$vuetify.display.mdAndUp ? 'small' : 'x-small'"
        :width="$vuetify.display.mdAndUp ? 40 : 20"
        :height="$vuetify.display.mdAndUp ? 45 : 35"
        rounded="circle"
        location="bottom right"
        @click="toTop"
      >
        <i class="fas fa-chevron-up" />
      </btn-primary>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import TopBar from '@/components/shared/TopBar.vue'
import NavDrawer from '@/components/shared/NavDrawer.vue'
import { useDisplay } from 'vuetify'

defineProps({
  title: {
    type: String,
    required: true
  },
  isInner: {
    type: Boolean,
    default: true
  }
})

const showScrollToTopBtn = ref(false)
const mainEl = ref(null)

onMounted(() => {
  if (mainEl.value) {
    mainEl.value.addEventListener('scroll', (e) => {
      const top = window.pageYOffset || e.target.scrollTop || 0
      showScrollToTopBtn.value = top > 150
    })
  }
})

const toTop = () => {
  if (!mainEl.value) return
  mainEl.value.scrollTo({ top: 0, behavior: 'smooth' })
}

const { mdAndUp } = useDisplay()
const drawer = ref(mdAndUp.value)

const onOpenDrawer = () => {
  drawer.value = true
}

const onCloseDrawer = () => {
  drawer.value = false
}
</script>
