<template>
  <v-menu id="profileMenu">
    <template #activator="{ props }">
      <v-avatar
        class="gradient-blue"
        v-bind="props"
      >
        <i class="fas fa-user tw:text-sm tw:text-white" />
      </v-avatar>
    </template>
    <v-list class="tw:!mt-3 tw:!pt-6 tw:!pr-5 tw:!pl-6 tw:text-center">
      <v-list-item v-if="member">
        <span class="profile-img tw:!mb-3 tw:text-center tw:md:!hidden tw:!block" />
        <h1 class="tw:text-lg tw:font-bold">
          {{ member.full_name }}
        </h1>
        <p class="tw:text-sm tw:text-gray-500">
          {{ member.user?.email }}
        </p>
      </v-list-item>
      <v-divider class="tw:my-3 tw:md:!hidden tw:!block" />
      <v-list-item
        :to="{ name: 'settings' }"
        class="m-t-5"
      >
        <v-list-item-title class="tw:!text-sm tw:md:block tw:hidden">
          <i class="tw:text-gray-500 fas fa-cog" /> Settings
        </v-list-item-title>
      </v-list-item>
      <v-list-item>
        <v-list-item-title class="tw:!text-sm tw:md:!block tw:!hidden">
          <i class="tw:text-gray-500 fas fa-sign-out-alt" /> Logout
        </v-list-item-title>
        <v-btn
          class="tw:md:!hidden tw:!block"
          @click="emit('logout')"
        >
          <i class="fas fa-sign-out-alt tw:!mr-2" /> Logout
        </v-btn>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<script setup>
defineProps({
  member: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['logout'])
</script>
