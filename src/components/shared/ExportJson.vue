<template>
  <btn-primary
    :disabled="!data.length"
    :class="{ 'opacity-50 cursor-not-allowed': !data.length }"
    v-bind="$attrs"
    size="small"
    @click="downloadExcel"
  >
    <slot>
      Export
    </slot>
  </btn-primary>
</template>

<script setup>
import { jsonToCsv } from '@/utility/json-to-csv';

const props = defineProps({
  data: {
    type: Array,
    required: true,
    default: () => []
  },
  fileName: {
    type: String,
    required: true,
  },
  sheetName: {
    type: String,
    default: 'Sheet1'
  },
  format: {
    type: String,
    validator: (value) => ['csv', 'xlsx'].includes(value),
    default: 'csv'
  }
})

const downloadExcel = () => {
  if (!props.data.length) {
    return;
  }

  jsonToCsv({
    data: props.data,
    fileName: props.fileName,
    sheetName: props.sheetName,
    format: props.format
  });
};
</script>