<template>
  <div class="tw:text-center">
    <h1
      v-if="member"
      class="tw:!mb-[5px] tw:text-lg tw:text-white"
    >
      {{ member.preferences?.runningRevenueShare || '0.00' }} {{ currency }}
    </h1>
    <p class="tw:text-gray-400 tw:text-xs">
      Running Revenue Share
    </p>
  </div>
</template>

<script setup>
defineProps({
  member: {
    type: Object,
    default: null
  },
  currency: {
    type: String,
    default: 'EUR'
  }
})
</script>
