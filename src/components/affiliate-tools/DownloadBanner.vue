<template>
  <v-btn
    :href="href"
    download="banner.html"
    variant="outlined"
    color="teal"
    size="small"
    class="download-btn tw:text-white tw:!m-0"
  >            
    <v-icon
      theme="dark"
      size="small"
      class="tw:!mr-1"
    >
      fas fa-file-download
    </v-icon>
    Download
  </v-btn>
</template>

<script>
import { bannerHtml } from '@/utility/affiliate-utility'

export default {
  name: 'DownloadBanner',
  props: {
    banner: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  computed: {
    href () {
      return 'data:text/html;charset=utf-8,' + encodeURIComponent(bannerHtml(this.banner))
    }
  }
}
</script>
