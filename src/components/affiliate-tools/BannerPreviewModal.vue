<template>
  <v-dialog
    :model-value="modelValue"
    :scrim="false"
    class="banner-preview-modal dialog-masonry tw:bg-dark/25 tw:rounded-md"
    scrollable
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <div class="tw:!p-6 dialog-cls tw:overflow-y-auto">
      <v-row>
        <v-col
          cols="12"
          sm="12"
          md="12"
        >
          <div class="tw:w-full tw:justify-center tw:flex">
            <img
              class="image-preview"
              :src="previewUrl"
            >
          </div>
        </v-col>
        <v-col
          cols="12"
          sm="12"
          md="12"
          class="tw:!my-4"
        >
          <div class="tw:text-center">
            <code>{{ html }}</code>
          </div>
        </v-col>
      </v-row>
      <v-row>
        <v-col
          cols="12"
          sm="12"
          md="12"
        >
          <div class="tw:flex tw:justify-end tw:gap-2 tw:!space-x-2">
            <slot />
            <v-btn
              variant="outlined"
              size="small"
              @click="$emit('update:modelValue', false)"
            >
              Close
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </div>
  </v-dialog>
</template>

<script>
import { bannerHtml, bannerPreviewURL } from '@/utility/affiliate-utility'

export default {
  name: 'BannerPreviewModal',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    banner: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue'],
  computed: {
    previewUrl () {
      if (this.banner) return bannerPreviewURL(this.banner)
      return ''
    },
    html () {
      if (this.banner) return bannerHtml(this.banner)
      return ''
    }
  }
}
</script>

<style scoped>
  .v-dialog__content.v-dialog__content--active {
    background: #0000007a;
  }
  .dialog-cls {
    background: #fff;
    width: 60%;
    margin: auto;
  }
  .image-preview {
    max-width: 100%;
    max-height: 100%;
  }
  @media screen and (max-width: 691px) {
    .dialog-cls {
      width: 100%;
      margin: auto;
    }
  }
</style>
