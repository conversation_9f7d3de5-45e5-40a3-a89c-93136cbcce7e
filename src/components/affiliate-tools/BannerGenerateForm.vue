<template>
  <v-form
    ref="form"
    v-model="valid"
    class="tw:!pb-10"
  >
    <v-row class="tw:!px-5 tw:!pt-10">
      <v-col
        cols="12"
        lg="6"
        sm="12"
        md="6"
        class="tw:!mb-6"
      >
        <div class="gen-banner-wrapper">
          <div class="banner-header tw:bg-info tw:text-white tw:!py-4 tw:!px-3">
            <h1 class="tw:text-lg tw:font-semibold">
              Generate Banner
            </h1>
            <p class="tw:text-sm tw:!mt-2">
              Simply fill out the form with the details of your campaign and we'll generate a unique referral link and a downloadable banner for you.
            </p>
          </div>
          <div class="banner-body tw:!px-5 tw:!py-8">
            <v-row>
              <v-col
                cols="12"
                lg="12"
                class="py-0"
              >
                <p class="label tw:!mb-1">
                  Campaign Name
                </p>
                <v-text-field
                  v-model="form.campaign_name"
                  :rules="rules.campaign_name"
                  placeholder="Enter Campaign Name"
                  variant="outlined"
                  clearable
                  density="compact"
                />
              </v-col>

              <v-card
                class="tw:!px-4 tw:!pt-4 tw:!mx-3 tw:!mb-6 tw:w-full tw:!bg-light"
                border
              >
                <div class="campaign-url-container tw:flex">
                  <div class="tw:flex tw:items-center tw:!mr-3">
                    <label class="radio-label">
                      <input
                        v-model="tab_choices"
                        type="radio"
                        value="1"
                        checked="checked"
                        @click="showWebsite"
                        @click:clear="clearApiErrors()"
                      >
                      Website
                    </label>
                  </div>
                  <div class="tw:flex tw:items-center">
                    <label class="radio-label">
                      <input
                        v-model="tab_choices"
                        type="radio"
                        value="2"
                        @click="showSocialMedia"
                      >
                      Social Media Domain
                    </label>
                  </div>
                </div>

                <v-divider
                  class="tw:!my-4"
                />

                <div id="websiteinput">
                  <p class="label tw:!mb-1">
                    Enter Website
                  </p>
                  <v-text-field
                    v-model="form.website"
                    :rules="rules.website"
                    placeholder="Enter Website"
                    variant="outlined"
                    clearable
                    density="compact"
                    @blur="checkingWebsiteUrl()"
                    @keyup="clearApiErrors()"
                    @click:clear="clearApiErrors(), invalid_use_of_url = false"
                  />
                  <div
                    v-if="invalid_use_of_url && website !== ''"
                    class="tw:text-left website-validation-wrap"
                  >
                    <p class="tw:!text-danger website-validation">
                      Social Media URL cannot be use in Website.
                    </p>
                  </div>
                  <div
                    v-if="showWebsiteError()"
                    class="tw:text-left website-validation-wrap"
                  >
                    <p
                      v-for="(error, index) in apiErrors.website"
                      :key="index"
                      class="tw:!text-danger website-validation"
                    >
                      Website is invalid or has been already taken.
                    </p>
                  </div>
                </div>

                <div
                  id="socialmediadomain"
                  class="hide"
                >
                  <p class="label">
                    Select Social Media Domain
                  </p>
                  <div class="social-media-url-container tw:flex tw:items-center">
                    <v-select
                      v-model="social_media_domain"
                      :items="social_media_domains"
                      item-title="url"
                      item-value="url"
                      placeholder="Select Social Media Domain"
                      variant="outlined"
                      density="compact"
                      class="social_media_domains tw:md:!w-[40%] tw:!w-full"
                      color="primary"
                      @update:model-value="composingSocialMediaUrl()"
                    >
                      <template #item="{ item, props }">
                        <v-list-item v-bind="props">
                          <template #prepend>
                            <i :class="'fab fa-' + item.raw.icon" />
                          </template>
                        </v-list-item>
                      </template>
                    </v-select>
                    <div class="tw:flex tw:md:w-[50%] tw:w-full">
                      <p class="slash tw:text-xl tw:text-gray-400">
                        /
                      </p>
                      <v-text-field
                        v-model="social_media_username"
                        placeholder="Username"
                        variant="outlined"
                        density="compact"
                        clearable
                        @keyup="composingSocialMediaUrl()"
                        @click:clear="clearApiErrors(), invalid_use_of_username = true"
                      />
                    </div>
                  </div>
                  <div class="tw:text-left website-validation-wrap">
                    <p
                      v-for="(error, index) in apiErrors.website"
                      :key="index"
                      class="tw:text-danger website-validation"
                    >
                      Social media account username has been already taken.
                    </p>
                    <p
                      v-if="social_media_username === '' || invalid_use_of_username"
                      class="tw:!text-danger website-validation"
                    >
                      Username is required
                    </p>
                  </div>
                </div>
              </v-card>

              <v-col
                cols="12"
                lg="6"
                class="tw:!py-0"
              >
                <p class="label tw:!mb-1">
                  Type
                </p>
                <v-select
                  v-model="form.type"
                  class="type-cls"
                  :rules="rules.type"
                  :items="types"
                  label="Select Type"
                  variant="outlined"
                  single-line
                  density="compact"
                  color="primary"
                />
              </v-col>
              <v-col
                cols="12"
                lg="6"
                class="tw:!py-0"
              >
                <p class="label tw:!mb-1">
                  Language
                </p>
                <v-select
                  v-model="form.language"
                  :rules="rules.language"
                  :items="languages"
                  clearable
                  placeholder="Select Language"
                  variant="outlined"
                  density="compact"
                  color="primary"
                />
              </v-col>
              <v-col
                cols="12"
                lg="6"
                class="py-0"
              >
                <p class="label tw:!mb-1">
                  Promotion
                </p>
                <v-select
                  v-model="form.banner"
                  :rules="rules.banner"
                  :items="banners"
                  item-title="name"
                  item-value="id"
                  placeholder="Select Promotion"
                  variant="outlined"
                  density="compact"
                  color="primary"
                />
              </v-col>
              <v-col
                cols="12"
                lg="6"
                class="tw:!py-0"
              >
                <p class="label tw:!mb-1">
                  Size
                </p>
                <v-select
                  v-model="form.size"
                  :rules="rules.size"
                  :items="sizes"
                  item-title="name"
                  item-value="id"
                  placeholder="Select Size"
                  variant="outlined"
                  density="compact"
                  color="primary"
                />
              </v-col>
              <v-col
                cols="12"
                lg="12"
                class="get-code-wrapper tw:!py-0"
              >
                <p class="label tw:!mb-1">
                  Referral Code
                </p>
                <v-text-field
                  v-model="form.referral_code"
                  :rules="rules.referral_code"
                  placeholder="Enter Referral Code"
                  variant="outlined"
                  clearable
                  density="compact"
                  @blur="checkingWebsiteUrl()"
                />
                <banner-code-generator @generated="form.referral_code=$event" />
                <div class="tw:text-left website-validation-wrap">
                  <p
                    v-for="(error, index) in apiErrors.referral_code"
                    :key="index"
                    class="tw:text-red website-validation"
                  >
                    Referral Code is invalid or has been already taken.
                  </p>
                </div>
              </v-col>
              <v-col
                cols="12"
                lg="12"
              >
                <div class="tw:text-right">
                  <v-btn
                    class="tw:!mr-4"
                    @click="resetForm(form)"
                  >
                    Reset Form
                  </v-btn>
                  <btn-success
                    :disabled="!valid || invalid_use_of_username"
                    @click="saveBanner(form)"
                  >
                    Generate
                  </btn-success>
                </div>
              </v-col>
            </v-row>
          </div>
        </div>
      </v-col>
      <v-col
        cols="12"
        lg="6"
        sm="12"
        md="6"
      >
        <div class="banner-header tw:bg-info tw:text-white tw:!py-4 tw:!px-3">
          <h1 class="tw:text-lg tw:font-semibold">
            Banner preview
          </h1>
          <p class="tw:text-xs">
            Banner Preview
          </p>
        </div>
        <div class="tw:text-center tw:!p-6 banner-preview tw:bg-light">
          <banner-image-preview
            v-if="previewedBanner !== null"
            :banner="previewedBanner"
          />
        </div>
      </v-col>
    </v-row>
  </v-form>
</template>

<script>
import BannerCodeGenerator from '@/components/affiliate-tools/BannerCodeGenerator.vue'
import BannerImagePreview from '@/components/affiliate-tools/BannerImagePreview.vue'

import { mapActions, mapGetters } from 'vuex'
import { createHelpers } from 'vuex-map-fields'
import customValidator from '../../utility/custom-validator'

const { mapFields } = createHelpers({
  getterType: 'affiliateTools/getField',
  mutationType: 'affiliateTools/updateField',
})

const required = (fieldName) => {
  return v => !!v || `${fieldName} is required`
}

export default {
  name: 'BannerGenerateForm',
  components: { BannerCodeGenerator, BannerImagePreview },
  data: () => ({
    valid: false,
    rules: {
      campaign_name: [ required('Name') ],
      website: [ required('Website') ],
      type: [ required('Type') ],
      banner: [ required('Promotion') ],
      language: [ required('Language') ],
      size: [ required('Size') ],
      referral_code: [ required('Referral Code') ],
    },
    types: ['promotion', 'advertisement'],
    languages: ['EN', 'FR', 'DE'],
    social_media_domains: [
      { url: 'https://www.facebook.com/', icon: 'facebook-square' },
      { url: 'https://www.instagram.com/', icon: 'instagram' },
      { url: 'https://twitter.com/', icon: 'twitter' },
      { url: 'https://t.me/', icon: 'telegram' },
      { url: 'https://wa.me/', icon: 'whatsapp' },
      { url: 'https://www.reddit.com/user/', icon: 'reddit' },
    ],
    social_media_domain: 'https://www.facebook.com/',
    social_media_username: '',
    tab_choices: 1,
    invalid_use_of_url: false,
    invalid_use_of_username: false
  }),
  computed: {
    ...mapGetters('affiliateTools', [ 'banners', 'sizes', 'previewedBanner', 'apiErrors' ]),
    ...mapFields([ 'form.campaign_name', 'form.website', 'form.referral_code', 'form.type', 'form.language', 'form.banner', 'form.size' ]),
    ...mapFields([ 'form' ]),
  },
  watch: {
    'form.banner': function (newVal, oldVal) {
      if (newVal !== oldVal && !newVal) this.form.size = null
    },
    form: {
      handler: function (newVal) {
        if (newVal.type && newVal.language) this.getBannersForTypeAndLanguage(newVal)
        if (newVal.banner && newVal.size) this.preview(newVal)
      },
      deep: true
    }
  },
  methods: {
    ...mapActions('affiliateTools', [
      'preview',
      'getBannersForTypeAndLanguage',
      'saveBanner',
      'clearApiErrors'
    ]),
    resetForm() {
      this.$store.dispatch('affiliateTools/resetForm')
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.reset()
          this.form.website = ''
        }
      })
    },
    submit() {
      this.saveBanner(this.form)
    },
    showWebsite () {
      this.invalid_use_of_url = false;
      document.getElementById('websiteinput').style.display ='block';
      document.getElementById('socialmediadomain').style.display ='none';

      this.form.website = '';
      this.social_media_domain = 'https://www.facebook.com/';
      this.social_media_username = '';
      this.apiErrors.website = '';
    },
    showWebsiteError () {
      return this.invalid_use_of_url === false && this.form.website !== '';
    },
    showSocialMedia () {
      this.invalid_use_of_url = false;
      document.getElementById('socialmediadomain').style.display ='block';
      document.getElementById('websiteinput').style.display ='none';
    },
    composingSocialMediaUrl () {
      this.invalid_use_of_username = false;
      this.form.website = this.social_media_domain + this.social_media_username;
    },
    checkingWebsiteUrl () {
      if (this.tab_choices == 1 && !customValidator.empty(this.form.website)) {
        if (customValidator.isSocialMedia(this.form.website)) {
          this.invalid_use_of_url = true;
          this.valid = false;
        } else {
          this.invalid_use_of_url = false;
          this.valid = true;
        }
      } else {
        this.invalid_use_of_url = false;
      }
    }
  },
}
</script>
<style scoped>
  .banner-body{
    border: 1px solid #eaeaea;
    box-shadow: 1px 2px 8px #eaeaea;
  }
  .banner-header{
    border-radius: 15px 15px 0 0;
  }
  .get-code-cls{
    position: absolute;
    right: 12px;
    top: 31px;
    height: 56px;
  }
  .get-code-wrapper {
      position: relative;
  }
  .get-code-cls{
    position: absolute;
    right: 12px;
    top: 20px;
    height: 40px !important;
    margin: 0 !important;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .type-cls{
    text-transform: capitalize;
  }
  .v-select-list {
      text-transform: capitalize;
  }
  .website-wrap {
    position: relative;
  }
  .website-validation-wrap {
    position: relative;
  }
  .referral-code-wrap{
    position: relative;
  }
  .hide {
    display: none;
  }
  .radio-label {
    font-size: 16px;
    color: rgba(0,0,0,.6);
    cursor: pointer;
  }
  .v-select-list {
    text-transform: initial;
  }
  .v-list-item i {
    font-size: 20px;
    margin-right: 10px;
    color: rgba(0,0,0,.6);
  }
  .campaign-url-container, .social-media-url-container {
    flex-direction: column;
  }
  .campaign-url-container .tw:flex {
    margin-bottom: 5px;
  }
  .campaign-url-container .tw:flex:last-child {
    margin-bottom: 0;
  }
  .social-media-url-container .slash {
    margin: 5px 12px 5px 0;
  }

  /* Responsive */
  @media only screen and (min-width: 768px) {
    .campaign-url-container, .social-media-url-container {
      flex-direction: row;
    }
    .social-media-url-container .slash {
      margin: 5px 12px;
    }
    .social_media_domains {
      width: 50%;
    }
  }
</style>
