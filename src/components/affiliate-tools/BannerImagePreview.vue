<template>
  <div
    v-if="banner"
    class="tw:text-center banner-preview bg-grey-lighten-4"
  >
    <img
      v-if="banner"
      :src="previewUrl"
    >
  </div>
</template>

<script>
import { bannerPreviewURL } from '@/utility/affiliate-utility'

export default {
  name: 'BannerImagePreview',
  props: {
    banner: {
      type: Object,
      default: null
    }
  },
  computed: {
      previewUrl () {
        if (this.banner) return bannerPreviewURL(this.banner)
          return ''
        },
  }
}
</script>
<style scoped>
  .banner-preview {
    height: fit-content;  
  }
  .banner-preview img {
    max-width: 100%;
    max-height: 100%;   
  }
</style>