<template>
  <v-btn
    variant="outlined"
    size="small"
    color="indigo"
    class="download-btn tw:text-white tw:!m-0"
    @click="copy()"
  >            
    <v-icon
      theme="dark"
      size="small"
      class="tw:!mr-1"
    >
      fas fa-copy
    </v-icon>
    Copy URL
  </v-btn>
</template>

<script>
export default {
  name: 'CopyBanner',
  props: {
    banner: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['copied'],
  methods: {
    copy () {
      let url = `https://www.piwi247.com/signup?referral_code=${this.banner.referral_code}`
      navigator.clipboard.writeText(url).then(() => {
        this.$emit('copied')
      })
    }
  }
}
</script>
<style scoped>
</style>