<template>
  <v-row class="table-row">
    <v-col
      class="tw:!pb-16"
      cols="12"
      sm="12"
      md="12"
    >
      <v-data-table
        v-model:page="currentPage"
        :headers="tableHeaders"
        :items="items"
        :items-per-page="pagination.per_page"
        :total-items="items.length"
        class="elevation-1 tw:!text-base"
        hide-default-footer
      >
        <template #item.website="{ item }">
          {{ item.website }}
          <v-icon
            v-if="item.is_social_media === true"
            color="orange"
            size="small"
            theme="light"
          >
            fas fa-users
          </v-icon>
        </template>

        <template #item.action="{ item }">
          <div class="tw:flex">
            <download-banner
              v-if="item !== null"
              :banner="item"
              class="tw:!m-1"
            />
            <copy-banner
              v-if="item !== null"
              :banner="item"
              class="tw:!m-1"
              @copied="showMessage = true; message='Copied URL!'"
            />
            <v-btn
              size="small"
              class="preview-btn tw:text-white tw:!m-1"
              variant="outlined"
              color="teal"
              @click="updateShowPreviewModal(); preview(item)"
            >
              <v-icon
                size="small"
                theme="dark"
                class="tw:!mr-1"
              >
                fas fa-eye
              </v-icon>
              Preview
            </v-btn>
            <v-btn
              size="small"
              class="preview-btn tw:text-white tw:!m-1"
              variant="outlined"
              color="red"
              @click="deleteBanner(item.referral_code)"
            >
              <v-icon
                size="small"
                theme="dark"
                class="tw:!mr-1"
              >
                fas fa-trash
              </v-icon>
              Delete
            </v-btn>
          </div>
        </template>

        <template #bottom>
          <v-pagination
            ref="pagination"
            v-model="currentPage"
            active-color="primary"
            density="compact"
            :length="pagination.page_count"
          />
        </template>
      </v-data-table>

      <banner-preview-modal
        v-if="previewedBanner !== null"
        v-model="showPreviewModal"
        :banner="previewedBanner"
      >
        <copy-banner
          :banner="previewedBanner"
          @copied="showMessage = true; message='Copied URL!'"
        />
        <download-banner :banner="previewedBanner" />
      </banner-preview-modal>
      <banner-delete-modal
        ref="bannerDeleteModalComponent"
        v-model="deleteModal"
        :open="deleteModal"
        @status="getDeleteStatus"
      />
      <v-snackbar
        v-model="showMessage"
        :timeout="2000"
        class="tw:text-sm"
      >
        {{ message }}
      </v-snackbar>
    </v-col>
  </v-row>
</template>

<script>
import BannerPreviewModal from '@/components/affiliate-tools/BannerPreviewModal.vue'
import CopyBanner from '@/components/affiliate-tools/CopyBanner.vue'
import DownloadBanner from '@/components/affiliate-tools/DownloadBanner.vue'
import BannerDeleteModal from '@/components/affiliate-tools/BannerDeleteModal.vue'

import { mapActions, mapGetters } from 'vuex'
import { createHelpers } from 'vuex-map-fields'
const { mapFields } = createHelpers({
  getterType: 'affiliateTools/getField',
  mutationType: 'affiliateTools/updateField',
})

export default {
  name: 'BannerArchive',
  components: { BannerPreviewModal, CopyBanner, DownloadBanner, BannerDeleteModal },
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    message: '',
    showMessage: false,
    deleteModal: false,
    currentPage: 1,
    tableHeaders: [
      {
        title: 'Campaign Name',
        align: 'start',
        value: 'campaign_name',
        sortable: true
      },
      {
        title: 'Type',
        align: 'start',
        value: 'type',
        sortable: true
      },
      {
        title: 'Language',
        align: 'start',
        value: 'language',
        sortable: true
      },
      {
        title: 'Website',
        align: 'start',
        value: 'website',
        sortable: true
      },
      {
        title: 'Referral Code',
        align: 'start',
        value: 'referral_code',
        sortable: true
      },
      {
        title: 'Action',
        align: 'start',
        value: 'action',
        sortable: false
      },
    ]
  }),
  computed: {
    ...mapGetters('affiliateTools', [ 'archivedBanners', 'previewedBanner']),
    ...mapFields(['showPreviewModal']),
    items () {
      return this.archivedBanners.data
    },
    pagination () {
      return this.archivedBanners.pagination
    },
  },
  watch: {
    items () {
      return this.archivedBanners.data
    },
    pagination () {
      return this.archivedBanners.pagination
    },
  },
  mounted () {
    setTimeout(() => {
      this.$refs.pagination?.onResize()
    }, 100)
    this.fetchArchivedBanners()
    this.currentPage = this.pagination.page || 1
  },
  methods: {
    ...mapActions('affiliateTools', [
        'preview',
        'fetchArchivedBanners',
        'updateShowPreviewModal',
      ]
    ),
    deleteBanner(referralCode) {
      this.$refs.bannerDeleteModalComponent.confirmation(true, referralCode)
    },
    getDeleteStatus(status) {
      this.fetchArchivedBanners();
      if (status === 'success') {
        this.$store.dispatch('affiliateTools/fetchArchivedBanners').then(() => {
          this.showMessage = true
          this.message = 'Banner successfully deleted.'
        })
      } else {
        this.showMessage = true
        this.message = 'Something went wrong. Please try agaian.'
      }
    }
  },
}
</script>
