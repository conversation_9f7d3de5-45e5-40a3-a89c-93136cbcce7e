<template>
  <div class="gallery-cls tw:text-center">
    <v-dialog
      v-model="deleteModal"
      max-width="290"
    >
      <v-card>
        <v-card-title class="text-h5">
          Delete Confirmation
        </v-card-title>
        <v-card-text>
          Are you sure you want to delete this Banner? <strong>{{ referralCode }}</strong>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="red-darken-1"
            variant="outlined"
            @click="deleteModal = false"
          >
            No
          </v-btn>
          <v-btn
            color="green-darken-1"
            variant="outlined"
            @click="deleteBanner()"
          >
            Yes
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>

export default {
  name: 'BannerDeleteModal',
  props: {
    open: Boolean
  },
  emits: ['status'],
  data() {
    return {
      deleteModal: false,
      referralCode: null
    }
  },
  methods: {
    confirmation (state, referralCode) {
      this.deleteModal = state
      this.referralCode = referralCode
    },
    deleteBanner() {
      this.$store.dispatch('affiliateTools/deleteBanner', this.referralCode).then(() => {
        this.deleteModal = false
        setTimeout(() => { 
          this.$emit('status', "success")
        }, 500)
      }).catch(() => {
        this.$emit('status', "error")
      })
    }
  }
}
</script>

<style scoped>
  .v-dialog__content.v-dialog__content--active {
    background: #0000007a;
  }
  .dialog-cls {
    background: #fff;
    width: 60%;
    margin: auto;         
  }
  .image-preview {
    max-width: 100%;
    max-height: 100%;
  }
  @media screen and (max-width: 691px) {
    .dialog-cls {
      width: 100%;
      margin: auto;         
    }
  }  
</style>