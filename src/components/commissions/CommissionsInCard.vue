<template>
  <div>
    <v-card
      v-for="(item, index) in commissions"
      :key="index"
      class="tw:!mb-1 tw:shadow-sm tw:rounded-lg"
      :class="index % 2 === 0 ? 'tw:!bg-gray-100' : 'tw:!bg-white'"
    >
      <v-card-text class="tw:!p-3 tw:bg-gray-50">
        <div class="tw:flex tw:justify-between tw:items-start tw:!mb-2">
          <div class="tw:flex tw:items-center tw:gap-2">
            <div class="tw:w-9 tw:h-9 tw:bg-white tw:rounded tw:flex tw:items-center tw:justify-center">
              <img
                :src="item.productIcon"
                :alt="item.product"
                class="tw:w-5 tw:h-5"
              >
            </div>
            <span class="tw:font-medium tw:text-gray-700">{{ item.product }}</span>
          </div>
          <div class="tw:text-right">
            <div class="tw:text-xs tw:font-bold tw:text-success-900">
              MEMBER ID
            </div>
            <div class="tw:text-xs">
              {{ item.memberId }}
            </div>
          </div>
        </div>
        <div class="tw:flex tw:justify-between">
          <div>
            <div class="tw:text-xs tw:font-bold tw:text-success-900">
              TURNOVER
            </div>
            <div class="tw:text-xs">
              {{ item.turnover }}
            </div>
          </div>
          <div class="tw:text-right">
            <div class="tw:text-xs tw:font-bold tw:text-success-900">
              COMMISSION
            </div>
            <div class="tw:text-xs">
              {{ item.commission }}
            </div>
          </div>
        </div>
      </v-card-text>
    </v-card>

    <div class="tw:!mt-4 tw:!pb-2 tw:flex tw:justify-end tw:w-full">
      <Pagination
        class="tw:w-full"
        :data="paginationData"
        @page-change="onPageChange"
      />
    </div>
  </div>
</template>

<script setup>
import Pagination from '@/components/shared/LaravelPagination.vue'

defineProps({
  commissions: {
    type: Array,
    required: true,
  },
  paginationData: {
    type: Object,
    required: true,
  },
  itemsPerPage: {
    type: Number,
    default: 10
  },
  type: {
    type: String,
    default: 'commission'
  },
  onPageChange: {
    type: Function,
    default: () => {}
  }
})
</script>