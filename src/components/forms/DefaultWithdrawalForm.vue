<template>
  <div>
    <v-form
      ref="form"
      v-model="valid"
      class="tw:rounded-lg tw:!pb-2"
    >
      <form-header>
        <img
          :src="ui.logo"
          class="tw:!mt-5 tw:!mb-5"
          :class="paymentOption.toLowerCase() + '-logo'"
        >
        <template #processing_time>
          {{ bundleFieldValue('processing_time') }}
        </template>
        <template #fee>
          {{ bundleFieldValue('withdrawal_fee') }}
        </template>
        <template #min_withdrawal>
          {{ bundleFieldValue('min_withdrawal') }} EUR
        </template>
        <template #max_withdrawal>
          {{ bundleFieldValue('max_withdrawal') }} EUR
        </template>
      </form-header>
      <v-expansion-panel-text
        v-if="form"
        class="tw:!px-4"
      >
        <div class="withdrawal-summary-container">
          <v-row
            class="tw:!p-4 custom-card tw:text-white gateway-inner-wrapper tw:text-sm"
            :class="ui.classes"
          >
            <v-col cols="8">
              <p>Amount to be withdrawn:</p>
            </v-col>
            <v-col cols="4">
              <p class="tw:font-bold">
                {{ totalAmountToBeWithdrawn }} {{ currency }}
              </p>
            </v-col>
            <v-col cols="8">
              <p>Fee:</p>
            </v-col>
            <v-col cols="4">
              <p class="tw:font-bold">
                {{ customerFee }} {{ currency }}
              </p>
            </v-col>
            <div class="divider tw:bg-white tw:!mt-3 tw:!mb-[5px] tw:opacity-50" />
            <v-col cols="8">
              <p>Total Amount:</p>
            </v-col>
            <v-col cols="4">
              <p class="tw:font-bold">
                {{ total }} {{ currency }}
              </p>
            </v-col>
          </v-row>
        </div>

        <div class="withdrawal-form-container tw:!mt-6">
          <v-row class="gateway-inner-wrapper tw:!mt-[15px]">
            <v-text-field
              ref="email"
              v-model="form.email"
              :rules="formRules.email"
              variant="outlined"
              class="tw:md:!mr-2 tw:!mr-0 tw:w-full tw:md:w-auto"
              label="Email"
              required
              :readonly="hasPaymentOptionEmailRegistered"
              density="compact"
            />
            <v-text-field
              ref="amount"
              v-model="form.amount"
              :rules="formRules.amount"
              variant="outlined"
              label="Enter Amount"
              suffix="EUR"
              class="tw:w-full tw:md:w-auto"
              :class="'gateway-' + paymentOption.toLowerCase()"
              type="number"
              required
              density="compact"
            />
            <div class="verification-wrapper tw:w-full">
              <div class="tw:flex tw:!w-full tw:gap-2">
                <!-- OTP -->
                <v-text-field
                  ref="verificationCode"
                  v-model="form.verification_code"
                  :rules="formRules.verification_code"
                  variant="outlined"
                  :label="totpStatus === 'enabled' && !isOTPEmail ? 'Enter Google Authenticator Code' : 'Verification Code'"
                  :class="isOTPEmail ? 'verification-field' : 'tw:!mr-0'"
                  type="number"
                  required
                  density="compact"
                />
                <v-btn
                  v-if="isOTPEmail"
                  color="primary"
                  class="m-black--text gradient-primary tw:!capitalize tw:!font-bold"
                  :loading="loadingGetCode"
                  @click="requestCode()"
                >
                  Get Code
                </v-btn>
                <!-- End OTP -->
              </div>
            </div>
            <!-- Switch -->
            <div
              v-if="totpStatus === 'enabled'"
              class="tw:!-mt-[5px] tw:mb-3 tw:cursor-pointer tw:flex tw:justify-start"
              @click="changeOTPMethod()"
            >
              <p class="tw:text-sm tw:font-bold tw:text-info tw:underline">
                <span v-if="isOTPEmail">Use Google Authenticator code instead</span>
                <span v-else>Use Email OTP instead</span>
              </p>
            </div>
            <!-- End Switch -->
          </v-row>
          <v-row class="request-btn-wrapper gateway-inner-wrapper tw:justify-end tw:!pt-2">
            <btn-success
              class="tw:!capitalize"
              :block="$vuetify.display.xs"
              :loading="loading"
              :disabled="!valid"
              @click="submit"
            >
              Send Request
            </btn-success>
          </v-row>
        </div>
      </v-expansion-panel-text>
    </v-form>
    <get-code-success-dialog
      ref="childGetCodeSuccess"
      title="Please check your inbox"
      message="We’ve sent the OTP code to"
      :recipient="memberEmail"
      :open="showGetCodeDialog"
    />
    <error-dialog
      ref="childError"
      :title="errorTitle"
      :message="message"
      :open="showErrorDialog"
      to-page=""
    />
    <success-dialog
      ref="childSuccess"
      title="Success"
      :message="message"
      :open="showSuccessDialog"
      to-page="/transaction-history"
    />
  </div>
</template>

<script>
import {withdrawalFormMixin} from '../../mixins/withdrawal-form-mixin';
import ErrorDialog from '../elements/dialog/ErrorDialog.vue';
import GetCodeSuccessDialog from '../elements/dialog/GetCodeSuccessDialog.vue';
import SuccessDialog from '../elements/dialog/SuccessDialog.vue';
import FormHeader from '@/components/forms/FormHeader.vue';

export default {
    name: 'DefaultWithdrawalForm',
    components: {
      GetCodeSuccessDialog,
      ErrorDialog,
      SuccessDialog,
      FormHeader
    },
    mixins: [withdrawalFormMixin],
    data() {
      return {
        showGetCodeDialog: false,
        showErrorDialog: false,
        showSuccessDialog: false
      }
    }
}
</script>

<style scoped>

</style>
