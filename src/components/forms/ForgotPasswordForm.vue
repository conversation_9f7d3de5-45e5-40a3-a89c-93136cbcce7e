<template>
  <div id="forgotPasswordForm">
    <v-form
      ref="form"
      class="piwiblue"
    >
      <v-row class="login-form-wrapper">
        <v-col
          cols="12"
          class="tw:!mt-6"
        >
          <v-text-field
            v-model="email"
            :error-messages="emailError ? capitalize(emailError) : []"
            :disabled="loading"
            :label="usernameLabel"
            variant="solo"
            density="compact"
            flat
            class="username-input"
            @update:model-value="enableGetCode"
          />
        </v-col>
        <v-col
          cols="12"
          class="tw:!-mt-2"
        >
          <btn-primary
            size="small"
            class="tw:float-right"
            :loading="loadingGetCode"
            :disabled="disableGetCode"
            @click="requestCode"
          >
            Get code
          </btn-primary>
        </v-col>
        <v-col
          cols="12"
          class="tw:!mt-6"
        >
          <v-text-field
            v-model="verification_code"
            :error-messages="codeError ? codeError : []"
            :disabled="loading"
            :label="codeLabel"
            type="text"
            density="compact"
            required
            variant="solo"
            flat
          />
        </v-col>
        <v-col cols="12">
          <v-text-field
            v-model="password"
            :error-messages="passwordError ? capitalize(passwordError) : []"
            :disabled="loading"
            :label="passwordLabel"
            type="password"
            density="compact"
            required
            variant="solo"
            flat
          />
        </v-col>
        <v-col cols="12">
          <v-text-field
            v-model="repeat_password"
            :error-messages="repeatPasswordError ? capitalize(repeatPasswordError) : []"
            :disabled="loading"
            :label="confirmPasswordLabel"
            type="password"
            density="compact"
            required
            variant="solo"
            flat
          />
        </v-col>
        <v-col cols="12">
          <btn-primary
            block
            :loading="loading"
            @click="onSubmit"
          >
            SAVE
          </btn-primary>
        </v-col>
      </v-row>
    </v-form>
    <get-code-success-dialog
      ref="childGetCodeSuccess"
      title="Please check your inbox"
      message="We've sent the verification code to"
      :recipient="email"
    />
    <error-dialog
      ref="childError"
      title="Error"
      :message="message"
      to-page=""
    />
    <success-dialog
      ref="childSuccess"
      title="Success"
      :is-persistent="true"
      :message="message"
      to-page="/auth/login"
    />
  </div>
</template>

<script setup>
import { ref, watch, capitalize } from 'vue';
import { useForm, useField } from 'vee-validate';
import * as yup from 'yup';
import customValidator from '../../utility/custom-validator';
import GetCodeSuccessDialog from "@/components/elements/dialog/GetCodeSuccessDialog.vue";
import ErrorDialog from "@/components/elements/dialog/ErrorDialog.vue";
import SuccessDialog from "@/components/elements/dialog/SuccessDialog.vue";
import { useStore } from 'vuex';

defineProps({
  usernameLabel: { type: String, default: 'Username' },
  passwordLabel: { type: String, default: 'Password' },
  confirmPasswordLabel: { type: String, default: 'Confirm Password' },
  codeLabel: { type: String, default: 'Verification Code' },
  modelValue: {
    type: Object,
    default: () => ({
      email: '',
      verification_code: '',
      password: '',
      repeat_password: ''
    })
  }
});

const emit = defineEmits(['update:modelValue']);

const store = useStore();

const schema = yup.object({
  email: yup.string().required().email(),
  verification_code: yup.string().required().length(6, ).matches(/^\d+$/, 'Verification code may only contain numeric characters').label('Verification Code'),
  password: yup.string().required().min(8).max(16),
  repeat_password: yup.string().oneOf([yup.ref('password')], 'The password confirmation does not match.').label('Password Confirmation').required()
});

const { handleSubmit } = useForm({
  validationSchema: schema
});

const { value: email, errorMessage: emailError } = useField('email');
const { value: verification_code, errorMessage: codeError } = useField('verification_code');
const { value: password, errorMessage: passwordError } = useField('password');
const { value: repeat_password, errorMessage: repeatPasswordError } = useField('repeat_password');

const loading = ref(false);
const loadingGetCode = ref(false);
const disableGetCode = ref(true);
const childGetCodeSuccess = ref(null);
const childError = ref(null);
const childSuccess = ref(null);
const message = ref('');

const enableGetCode = () => {
  if (!customValidator.empty(email.value) && customValidator.email(email.value)) {
    disableGetCode.value = false;
  } else {
    disableGetCode.value = true;
  }
};

const requestCode = () => {
  if (!customValidator.empty(email.value) && customValidator.email(email.value)) {
    loadingGetCode.value = true;
    store.dispatch('account/getCode', { email: email.value, purpose: 'reset-password' }).then(() => {
      childGetCodeSuccess.value.setOpen(true);
      loadingGetCode.value = false;
    }).catch(err => {
      message.value = err.data.errors[0].message || 'An error has occurred. Please contact customer support for assistance.';
      childError.value.setOpen(true);
      loadingGetCode.value = false;
    });
  } else {
    loadingGetCode.value = false;
  }
};

const onSubmit = handleSubmit(async () => {
  const formValues = {
    email: email.value,
    verification_code: verification_code.value,
    password: password.value,
    repeat_password: repeat_password.value
  };
  loading.value = true;
  disableGetCode.value = true;
  try {
    await store.dispatch('account/forgotPassword', formValues);
    message.value = 'Your password has been updated';
    childSuccess.value.setOpen(true);
    loading.value = false;
  } catch (err) {
    message.value = err.data.errors[0].message || 'An error has occurred. Please contact customer support for assistance.';
    childError.value.setOpen(true);
    loading.value = false;
  }
});

watch([email, verification_code, password, repeat_password], () => {
  emit('update:modelValue', {
    email: email.value,
    verification_code: verification_code.value,
    password: password.value,
    repeat_password: repeat_password.value
  });
});
</script>

<style scoped>
</style>
