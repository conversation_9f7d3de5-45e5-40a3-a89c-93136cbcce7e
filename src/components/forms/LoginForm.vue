<template>
  <v-form
    ref="form"
    class="piwiblue"
  >
    <v-row class="login-form-wrapper">
      <v-col
        cols="12"
      >
        <v-text-field
          v-model="username"
          :error-messages="usernameError ? [capitalize(usernameError)] : []"
          :disabled="loading"
          :label="usernameLabel"
          prepend-inner-icon="fas fa-user tw:!text-base"
          class="username-input"
          density="compact"
          required
          variant="solo"
          flat
        />
      </v-col>
      <v-col
        cols="12"
      >
        <v-text-field
          v-model="password"
          :error-messages="passwordError ? [capitalize(passwordError)] : []"
          :disabled="loading"
          :label="passwordLabel"
          type="password"
          prepend-inner-icon="fas fa-lock tw:!text-base"
          density="compact"
          required
          variant="solo"
          flat
        />
      </v-col>
      <v-col
        cols="12"
        class="tw:text-xs"
      >
        <v-switch
          v-model="rememberMe"
          density="compact"
          :label="rememberMeLabel"
          :disabled="loading"
          theme="dark"
          color="success"
          class="tw:text-xs tw:px-1 tw:text-white"
          hide-details="auto"
          @update:model-value="remember"
        />
      </v-col>
      <v-col
        cols="12"
        class="tw:!mt-3"
      >
        <btn-primary
          block
          :loading="loading"
          @click="submit"
        >
          Login
        </btn-primary>
      </v-col>
    </v-row>
  </v-form>
</template>

<script setup>
import { ref, watch, onMounted, capitalize } from 'vue'
import { useForm, useField } from 'vee-validate'
import * as yup from 'yup'
import storage from '../../utility/storage'
import { useStore } from 'vuex'

defineProps({
  rememberMeLabel: { type: String, default: 'Remember password' },
  usernameLabel: { type: String, default: 'Username' },
  passwordLabel: { type: String, default: 'Password' },
  modelValue: {
    type: Object,
    default: () => ({ username: '', password: '', userType: '' }),
  }
})

const emit = defineEmits(['update:modelValue', 'loggedin', 'failed'])

const store = useStore()
const loading = ref(false)
const rememberMe = ref(false)

const { handleSubmit, values, setValues } = useForm({
  validationSchema: yup.object({
    username: yup.string().required().email(),
    password: yup.string().required().max(16),
  }),
  initialValues: {
    username: '',
    password: '',
    userType: 4,
  }
})

const { value: username, errorMessage: usernameError } = useField('username')
const { value: password, errorMessage: passwordError } = useField('password')

watch(values, (newValues) => {
  emit('update:modelValue', newValues)
}, { deep: true })

const remember = () => {
  if (rememberMe.value) {
    storage.setObject('remember_me', { username: username.value, password: password.value })
  } else {
    storage.removeItem('remember_me')
  }
}

const submit = handleSubmit(async (formValues) => {
  remember()
  loading.value = true
  try {
    await store.dispatch('auth/login', formValues)
    emit('loggedin')
  } catch (err) {
    emit('failed', err)
  } finally {
    loading.value = false
  }
})

onMounted(() => {
  const rememberData = storage.getObject('remember_me')
  if (rememberData) {
    rememberMe.value = true
    setValues({
      username: rememberData.username,
      password: rememberData.password,
      userType: 4,
    })
  }
})
</script>
