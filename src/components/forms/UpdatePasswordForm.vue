<template>
  <div id="updatePasswordForm">
    <v-form ref="form">
      <div class="tw:relative">
        <v-text-field
          v-model="verification_code"
          :error-messages="verificationCodeError ? [verificationCodeError] : []"
          :disabled="loading"
          type="text"
          required
          variant="outlined"
          label="Verification Code"
          class="verification-field"
          density="compact"
        />
        <btn-primary
          color="primary"
          class="tw:!mr-[5px] tw:!absolute tw:top-0 tw:right-[-4px] tw:!capitalize"
          :loading="loadingGetCode"
          @click="requestCode"
        >
          Get Code
        </btn-primary>
      </div>
      <v-text-field
        v-model="current_password"
        :error-messages="currentPasswordError ? [currentPasswordError] : []"
        :disabled="loading"
        type="password"
        required
        variant="outlined"
        label="Enter Current Password"
        density="compact"
      />
      <v-text-field
        v-model="password"
        :error-messages="passwordError ? [passwordError] : []"
        :disabled="loading"
        type="password"
        required
        variant="outlined"
        label="Enter New Password"
        density="compact"
      />
      <v-text-field
        v-model="repeat_password"
        :error-messages="repeatPasswordError ? [repeatPasswordError] : []"
        :disabled="loading"
        type="password"
        required
        variant="outlined"
        label="Confirm New Password"
        density="compact"
      />
      <div class="tw:float-right">
        <btn-success
          class="tw:!capitalize"
          block
          :loading="loading"
          @click="onSubmit"
        >
          Update
        </btn-success>
      </div>
    </v-form>
    <get-code-success-dialog
      ref="childGetCodeSuccess"
      title="Please check your inbox"
      message="We’ve sent the verification code to"
      :recipient="email"
      :open="dialog"
    />
    <error-dialog
      ref="childError"
      title="Error"
      :message="message"
      :open="showErrorDialog"
      to-page=""
    />
    <success-dialog
      ref="childSuccess"
      title="Success"
      :is-persistent="true"
      :message="message"
      :open="showSuccessDialog"
      to-page="/dashboard"
    />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useForm, useField } from 'vee-validate'
import * as yup from 'yup'
import { useStore } from 'vuex'
import storage from '../../utility/storage'
import customValidator from '../../utility/custom-validator'
import GetCodeSuccessDialog from "@/components/elements/dialog/GetCodeSuccessDialog.vue"
import ErrorDialog from "@/components/elements/dialog/ErrorDialog.vue"
import SuccessDialog from "@/components/elements/dialog/SuccessDialog.vue"

defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      verification_code: '',
      current_password: '',
      password: '',
      repeat_password: ''
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const store = useStore()
const member = storage.getObject('member')
const email = member.user.email

const loading = ref(false)
const loadingGetCode = ref(false)
const dialog = ref(false)
const showErrorDialog = ref(false)
const showSuccessDialog = ref(false)
const message = ref('')

const { handleSubmit, values } = useForm({
  validationSchema: yup.object({
    verification_code: yup.string().required().matches(/^\d{6}$/, 'The verification code may only contain numeric characters').label('Verification Code'),
    current_password: yup.string().required().min(8).max(16).label('Current Password'),
    password: yup.string().required().min(8).max(16).label('New Password'),
    repeat_password: yup.string().oneOf([yup.ref('password')], 'The password confirmation does not match.').label('Password Confirmation').required()
  }),
  initialValues: {
    verification_code: '',
    current_password: '',
    password: '',
    repeat_password: ''
  }
})

const { value: verification_code, errorMessage: verificationCodeError } = useField('verification_code')
const { value: current_password, errorMessage: currentPasswordError } = useField('current_password')
const { value: password, errorMessage: passwordError } = useField('password')
const { value: repeat_password, errorMessage: repeatPasswordError } = useField('repeat_password')

watch(values, (newValues) => {
  emit('update:modelValue', newValues)
}, { deep: true })

const requestCode = async () => {
  if (!customValidator.empty(email) && customValidator.email(email)) {
    loadingGetCode.value = true
    try {
      await store.dispatch('account/getCode', { email, purpose: 'change-password' })
      dialog.value = true
      childGetCodeSuccess.value?.setOpen(true)
    } catch (err) {
      message.value = err?.data?.errors?.[0]?.message || 'An error has occurred. Please contact support.'
      showErrorDialog.value = true
      childError.value?.setOpen(true)
    } finally {
      loadingGetCode.value = false
    }
  }
}

const onSubmit = handleSubmit(async (values) => {
  loading.value = true
  try {
    await store.dispatch('account/updatePassword', values)
    message.value = 'Your password has been updated'
    showSuccessDialog.value = true
    childSuccess.value?.setOpen(true)
  } catch (err) {
    message.value = err?.data?.errors?.[0]?.message || 'An error has occurred. Please contact support.'
    showErrorDialog.value = true
    childError.value?.setOpen(true)
  } finally {
    loading.value = false
  }
})

const childGetCodeSuccess = ref(null)
const childError = ref(null)
const childSuccess = ref(null)

defineExpose({ requestCode, onSubmit })
</script>

<style scoped>
  #updatePasswordForm {
    width: 100%;
  }
  @media only screen and (min-width: 768px) {
    .get-code-btn {
      top: 46px;
    }
    .request-btn {
      max-width: 200px !important;
      min-width: 200px !important;
      width: 200px;
      display: inline-block;
    }
  }
</style>
