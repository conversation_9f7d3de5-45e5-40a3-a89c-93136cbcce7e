<template>
  <div id="usdcWithdrawalForm">
    <v-form
      ref="form"
      v-model="valid"
      class="tw:rounded-lg"
    >
      <form-header>
        <img
          :src="ui.logo"
          class="usdc-logo tw:!mt-5 tw:!mb-5"
        />
        <template #processing_time>
          {{ bundleFieldValue('processing_time') }}
        </template>
        <template #fee>
          {{ bundleFieldValue('withdrawal_fee') }}
        </template>
        <template #min_withdrawal>
          {{ minimumAmountWithdrawal }} USDC
        </template>
        <template #max_withdrawal>
          {{ maximumAmountWithdrawal }} USDC
        </template>
      </form-header>
      <v-expansion-panel-text class="tw:!px-4 tw:!py-1.5">
        <div>
          <v-row
            class="tw:!p-2 custom-card tw:text-white gateway-inner-wrapper tw:text-sm tw:!ml-0 tw:w-full"
            :class="ui.classes"
          >
            <v-col cols="12 tw:!mb-[5px]">
              <p>Amount to be withdrawn:</p>
            </v-col>
            <v-col cols="6">
              <p class="tw:font-bold tw:text-sm">
                {{ form.cryptoAmount }} USDC
              </p>
            </v-col>
            <v-col cols="6">
              <p class="tw:font-bold tw:text-sm">
                {{ form.eurAmount }} EUR
              </p>
            </v-col>
          </v-row>

          <v-row class="tw:!pt-[15px] tw:text-sm tw:!ml-0">
            Exchange Rate: <span
              v-if="usdcExchangeRate"
              class="tw:!ml-[5px] tw:font-bold tw:text-usdc"
            >1 USDC = {{ adjustedUsdcValue }} EUR</span>
          </v-row>
        </div>

        <div class="withdrawal-form-container tw:!mt-6">
          <v-row class="gateway-inner-wrapper tw:!mt-[15px]">
            <v-text-field
              ref="cryptoAmount"
              v-model="form.cryptoAmount"
              :rules="formRules.cryptoAmount"
              variant="outlined"
              label="Enter USDC Amount"
              suffix="USDC"
              class="gateway-usdc tw:sm:!mr-1 tw:!mr-0 tw:sm:!w-[55%] tw:w-full"
              type="number"
              density="compact"
              color="primary"
              required
              @update:model-value="onAmountChange('cryptoAmount')"
            />
            <v-text-field
              ref="eurAmount"
              v-model="form.eurAmount"
              variant="outlined"
              label="Enter EUR Amount"
              suffix="EUR"
              class="gateway-usdttrc20 tw:sm:!w-[40%] tw:w-full"
              type="number"
              density="compact"
              color="primary"
              required
              @update:model-value="onAmountChange('eurAmount')"
            />
            <v-text-field
              ref="receiveAddress"
              v-model="form.receive_address"
              :rules="formRules.receive_address"
              variant="outlined"
              label="Receiver Address"
              class="tw:!mr-1 tw:!w-full"
              required
              density="compact"
              color="primary"
            />
            <div class="verification-wrapper tw:w-full">
              <div class="tw:w-full tw:flex tw:!space-x-2">
                <!-- OTP -->
                <v-text-field
                  ref="verificationCode"
                  v-model="form.verification_code"
                  :rules="formRules.verification_code"
                  variant="outlined"
                  :label="totpStatus === 'enabled' && !isOTPEmail ? 'Enter Google Authenticator Code' : 'Verification Code'"
                  :class="isOTPEmail ? 'verification-field' : 'tw:!mr-0'"
                  type="number"
                  density="compact"
                  class="tw:!w-full"
                  color="primary"
                  required
                />
                <v-btn
                  v-if="isOTPEmail"
                  color="primary"
                  class="m-black--text gradient-primary tw:!capitalize tw:!font-bold"
                  :loading="loadingGetCode"
                  @click="requestCode()"
                >
                  Get Code
                </v-btn>
                <!-- End OTP -->
              </div>

              <!-- Switch -->
              <div
                v-if="totpStatus === 'enabled'"
                class="tw:-mt-[5px] tw:!mb-3 tw:cursor-pointer"
                @click="changeOTPMethod()"
              >
                <p class="tw:text-sm tw:font-bold tw:text-info tw:underline">
                  <span v-if="isOTPEmail">Use Google Authenticator code instead</span>
                  <span v-else>Use Email OTP instead</span>
                </p>
              </div>
              <!-- End Switch -->
            </div>
          </v-row>
          <v-row class="request-btn-wrapper gateway-inner-wrapper tw:justify-end">
            <btn-success
              class="tw:!capitalize"
              :block="$vuetify.display.xs"
              :loading="loading"
              :disabled="!valid"
              @click="submit"
            >
              Send Request
            </btn-success>
          </v-row>
        </div>
      </v-expansion-panel-text>
    </v-form>
    <get-code-success-dialog
      ref="childGetCodeSuccess"
      title="Please check your inbox"
      message="We’ve sent the OTP code to"
      :recipient="memberEmail"
      :open="dialog"
    />
    <error-dialog
      ref="childError"
      title="Error"
      :message="message"
      :open="errorDialog"
      to-page=""
    />
    <success-dialog
      ref="childSuccess"
      title="Success"
      :message="message"
      :open="successDialog"
      to-page="/transaction-history"
    />
  </div>
</template>

<script>
import {withdrawalFormMixin} from '../../mixins/withdrawal-form-mixin'
import {mapActions, mapGetters} from 'vuex'
import {div, inBetween, mul, toFixed} from '../../utility/number.util'

export default {
	name: "UsdcWithdrawalForm",
	mixins: [withdrawalFormMixin],
	data() {
		return {
			interval: null,
			form: {receiveAddress: '', cryptoAmount: '', eurAmount: '', verificationCode: ''},
      lastChanged: null,
    }
	},
  computed: {
    ...mapGetters('withdrawal', [
      'usdcExchangeRate'
    ]),
    minimumAmountWithdrawal() {
      return this.usdcExchangeRate ? this.usdcExchangeRate.limits.minimumAmountWithdrawal : ''
    },
    maximumAmountWithdrawal() {
      return this.usdcExchangeRate ? this.usdcExchangeRate.limits.maximumAmountWithdrawal : ''
    },
    adjustedUsdcValue() {
      let adjustedVal = toFixed(this.getAdjustedRate('cryptoAmount', 1), 8);
      if(this.lastChanged) {
        this.onAmountChange(this.lastChanged);
      }
      return adjustedVal;
    }
  },
  beforeUnmount() {
    clearInterval(this.interval)
  },
	mounted() {
		this.interval = setInterval(() => {
			this.fetchAdjustedUsdcExchangeRates()
		}, 1000)
	},
	methods: {
		...mapActions('withdrawal', [
			'fetchAdjustedUsdcExchangeRates'
		]),
		getPayload() {
			return {
				customer_fee: this.customerFee,
				company_fee: this.companyFee,
				verification_method: this.activeOTPMethod,
				verification_code: this.form.verification_code,
				payment_option_type: this.paymentOption,
				products: [{
					username: this.customerProduct.username,
					product_code: this.customerProduct.product.code,
					amount: this.form.eurAmount,
					meta: {
						payment_details: {
							requested_crypto: this.form.cryptoAmount
						}
					}
				}],
				meta: {
					field: {
						account_id: this.form.receive_address
					},
					payment_details: {
						blockchain_rate: this.usdcExchangeRate.base_rate,
						rate: this.getAdjustedRate('eurAmount', this.form.eurAmount)
					}
				}
			}
		},
		onAmountChange(from) {
			const isFromEurAmount = from === 'eurAmount'
			const value = this.form[from] || '0.00'
			const convertedAmountField = isFromEurAmount ? 'cryptoAmount' : 'eurAmount'

			if (!isNaN(value)) {
				const adjustedRate = this.getAdjustedRate(from, value);
				this.form[convertedAmountField] = isFromEurAmount ? div(value, adjustedRate).toFixed(2) : mul(value, adjustedRate).toFixed(2)
			}
      this.lastChanged = from;
    },
		getRangeSetting(from, amount) {
			const ranges = this.usdcExchangeRate.rate.withdrawal;
			return from === 'eurAmount' ?
				ranges.filter(range => inBetween(amount, range.amountFrom, range.amountTo)).pop() :
				ranges.filter(range => inBetween(amount, range.from, range.to)).pop();
		},
		getAdjustedRate(from, amount) {
			const range = this.getRangeSetting(from, amount);
      return (range && range.adjustedRate) || this.usdcExchangeRate.base_rate;
		}
	},
}
</script>

<style scoped>
.textflash {
	-webkit-animation: flash linear 1s infinite;
	animation: flash linear 1s infinite;
}

@-webkit-keyframes flash {
	0% {
		opacity: 1;
	}
	50% {
		opacity: .1;
	}
	100% {
		opacity: 1;
	}
}

@keyframes flash {
	0% {
		opacity: 1;
	}
	50% {
		opacity: .1;
	}
	100% {
		opacity: 1;
	}
}
</style>
