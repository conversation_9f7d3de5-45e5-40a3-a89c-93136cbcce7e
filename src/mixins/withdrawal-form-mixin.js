import customValidator from '../utility/custom-validator'
import GetCodeSuccessDialog from '@/components/elements/dialog/GetCodeSuccessDialog.vue'
import ErrorDialog from '@/components/elements/dialog/ErrorDialog.vue'
import SuccessDialog from '@/components/elements/dialog/SuccessDialog.vue'
import storage from '../utility/storage'
import FormHeader from '@/components/forms/FormHeader.vue';
import {add, decimal, inBetween} from "../utility/number.util";
import {mapGetters} from "vuex";

export const withdrawalFormMixin = {
	components: {FormHeader, GetCodeSuccessDialog, ErrorDialog, SuccessDialog},
	props: {
		paymentOption: {
			type: String
		},
		customerPaymentOption: {
			type: Object,
		}
	},
	data() {
		return {
			member: storage.getObject('member'),
			settings: storage.getObject('settings'),
			memberEmail: storage.getObject('member').user.email,
			customerProduct: storage.getObject('products'),
			errorTitle: 'Error',
			message: '',
			dialog: false,
			successDialog: false,
			errorDialog: false,
			lazy: false,
			loadingGetCode: false,
			loading: false,
			currency: 'EUR',
			valid: false,
			form: null,
      activeOTPMethod: 'email',
		}
	},
	mounted() {
		this.form = this.isCrypto ?
			{
				receive_address: '',
				btcAmount: '0.00',
				cryptoAmount: '0.00',
				eurAmount: '0.00',
				verification_code: ''
			} :
			{email: '', amount: '', verification_code: ''}

		if (this.isCrypto) {
			this.form.receive_address = this.customerPaymentOption.fields[0].account_id.value
		} else {
			this.form.email = this.customerPaymentOption.fields[0].email.value
		}

		this.$store.dispatch('gAuth/status').then(() => {})
	},

	computed: {
		...mapGetters('gAuth', ['getStatus']),
		totpStatus() {
			return this.getStatus
		},
		isOTPEmail() {
			return this.activeOTPMethod === 'email';
		},
		total() {
			return this.form.amount
		},
		totalAmountToBeWithdrawn () {
			return add(this.form.amount, this.customerFee)
		},
		customerFee () {
			return this.computeFee('member')
		},
		companyFee () {
			return this.computeFee('company')
		},
		hasPaymentOptionEmailRegistered () {
			return this.customerPaymentOption.fields[0].email.value !== ''
		},
		ui () {
			let meta = { }
			switch (this.customerPaymentOption.code) {
				case 'USDTTRC20':
					meta.logo = new URL('@/assets/gateway-usdttrc20-logo.svg', import.meta.url).href
					meta.classes = { 'gradient-usdttrc20': true }
					break
				case 'USDTERC20':
					meta.logo = new URL('@/assets/gateway-usdterc20-logo.svg', import.meta.url).href
					meta.classes = { 'gradient-usdterc20': true }
					break
				case 'USDC':
					meta.logo =  new URL('@/assets/gateway-usdc-logo.svg', import.meta.url).href
					meta.classes = { 'gradient-usdc': true }
					break
				case 'BITCOIN':
					meta.logo = new URL('@/assets/gateway-bitcoin-logo.svg', import.meta.url).href
					meta.classes = { 'gradient-bitcoin': true }
					break
				case 'SKRILL':
					meta.logo = new URL('@/assets/gateway-skrill-logo.svg', import.meta.url).href
					meta.classes = { 'gradient-skrill': true }
					break
				case 'NETELLER':
					meta.logo = new URL('@/assets/gateway-neteller-logo.svg', import.meta.url).href
					meta.classes = { 'gradient-neteller': true }
					break
			}
			return meta
		},
		formRules () {
			return {
				email: [
					value => !!value || 'Email is required',
					value => /.+@.+\..+/.test(value) || 'E-mail must be valid',
				],
				receive_address: [
					value => !!value || 'Receive Address is required',
				],
				amount: [
					value => !!value || 'Amount is required',
					value => decimal(value).greaterThanOrEqualTo(this.minAmount) || 'Amount should be greater than equals '+ this.minAmount +' but less than ' + this.maxAmount,
					value => decimal(value).lessThanOrEqualTo(this.maxAmount) || 'Amount should be greater than equals '+ this.minAmount +' but less than ' + this.maxAmount,
					() => {
						let errorField = null
						if (this.apiErrors[this.customerPaymentOption.code]) {
							let currIndex = null;
							errorField = this.apiErrors[this.customerPaymentOption.code].find((error, idx) => {
								if (error.field === 'products[0].amount') {
									currIndex = idx;
									return true;
								}
								return false;
							})

							if (errorField) {
								this.apiErrors[this.customerPaymentOption.code].splice(currIndex, 1);
								return errorField.message;
							}
						}

						return true
					}
				],
				btcAmount: [
					value => !!value || 'Amount is required',
					value => !decimal(value).greaterThan(this.maximumAmountWithdrawal) || 'Above maximum amount',
					value => !decimal(value).lessThan(this.minimumAmountWithdrawal) || 'Below minimum amount'
				],
				cryptoAmount: [
					value => !!value || 'Amount is required',
					value => !decimal(value).greaterThan(this.maximumAmountWithdrawal) || 'Above maximum amount',
					value => !decimal(value).lessThan(this.minimumAmountWithdrawal) || 'Below minimum amount'
				],
				verification_code: [
					value => !!value || 'Verification is required',
					() => {
						let verificationError = null
						if (this.apiErrors[this.customerPaymentOption.code]) {
							let currIndex = null;
							verificationError = this.apiErrors[this.customerPaymentOption.code].find((error, idx) => {
								if (error.field === 'verification_code') {
									currIndex = idx;
									return true;
								}
								return false;
							})
							if (verificationError) {
								this.apiErrors[this.customerPaymentOption.code].splice(currIndex, 1);
								return verificationError.message;
							}
						}

						return true
					}
				]
			}
		},
		isCrypto () {
			return ['BITCOIN', 'USDTTRC20', 'USDTERC20', 'USDC'].includes(this.customerPaymentOption.code)
		},
		minAmount () {
			return this.bundleFieldValue('min_withdrawal') != '' ? this.bundleFieldValue('min_withdrawal') : 10
		},
		maxAmount () {
			return this.bundleFieldValue('max_withdrawal') != '' ? this.bundleFieldValue('max_withdrawal') : 999999
		},
		...mapGetters('withdrawal', [
			'apiErrors'
		])
	},
	methods: {
		changeOTPMethod() {
			this.activeOTPMethod = this.activeOTPMethod === 'email' ? 'gauth' : 'email';
			this.form.verification_code = '';
		},
		requestCode() {
			let isValid = false
			let ref = null
			if (this.isCrypto) {
				isValid = !customValidator.empty(this.form.receiveAddress)
				ref = this.$refs.receiveAddress
			} else {
				isValid = !customValidator.empty(this.form.email) && customValidator.email(this.form.email)
				ref = this.$refs.email
			}

			if (isValid) {
				this.loadingGetCode = true
				this.$store.dispatch('account/getCode', {'email': this.memberEmail, 'purpose': 'transaction'}).then(() => {
					this.dialog = true
					this.$refs.childGetCodeSuccess.setOpen(true)
					this.loadingGetCode = false
				}).catch((err) => {
					this.$ref.form.validate()
					this.message = err.response.data.errors[0].message
					this.errorDialog = true
					this.$refs.childError.setOpen(true)
					this.loadingGetCode = false
				})
			} else {
				ref.focus()
				this.loadingGetCode = false
			}
		},
		submit() {
			if (customValidator.zero(this.member.available_balance)) {
				this.message = 'Unable to withdraw with 0.00 Balance.'
				this.errorDialog = true
				this.$refs.childError.setOpen(true)
			} else {
				this.$refs.form.validate().then((result) => {
					if (result) {
						this.loading = true

						this.$store.dispatch('withdrawal/withdraw', this.getPayload()).then((response) => {
							this.message = response.message
							this.successDialog = true
							this.$refs.childSuccess.setOpen(true)
							this.loading = false
						}).catch((err) => {
							if (err.data.title === 'Payment Option Not Available') {
								this.errorTitle = err.data.title
								this.message = err.data.message
								this.errorDialog = true
								this.$refs.childError.setOpen(true)
							} else {
								this.$refs.form.validate()
							}

							let insufficientBalObj = err.data.errors.find(obj => obj.message === 'Insufficient Balance');
							if (insufficientBalObj) {
								this.errorTitle = insufficientBalObj.message
								this.message = "Sorry, you don't have enough balance to make this transaction."
								this.errorDialog = true
								this.$refs.childError.setOpen(true)
							}

							console.log(insufficientBalObj);

							this.loading = false
						})
					}
				})
			}
		},
		getPayload() {
			return {
				verification_method: this.activeOTPMethod,
				verification_code: this.form.verification_code,
				payment_option_type: this.paymentOption,
				customer_fee: this.customerFee,
				company_fee: this.companyFee,
				products: [{
					username: this.customerProduct.username,
					product_code: this.customerProduct.product.code,
					amount: this.form.amount,
				}],
				meta: {
					field: {
						email: this.form.email
					}
				}
			}
		},
		getFeeSetting(type) {
			const feeSettings = this.customerPaymentOption.fees.withdrawal[type]
			if (!this.customerPaymentOption.fees.enabled) return null
			if (this.customerPaymentOption.fees.withdrawal[type].length !== 0) return null

			return feeSettings.filter((setting) => {
				if (setting.from !== null) {
					return inBetween(this.form.amount, setting.from, setting.to)
				}
			});
		},
		computeFee(type) {
			let actualFee = '0.00'
			let feeSetting = this.getFeeSetting(type)

			if (feeSetting) {
				const customFee = this.form.amount * ((feeSetting[0].percentage).valueOf() / 100);
				actualFee = customFee.toString();
			} else {
				actualFee = this.bundleFieldValue(`withdrawal_fee`).toLowerCase() === 'free' ? '0.00' : this.bundleFieldValue(`withdrawal_fee`);
				if (actualFee.includes('%')) {
					const percentage = parseFloat(actualFee.slice(0, -1));
					const bundleFee = decimal(this.form.amount)
						.mul(decimal(percentage.valueOf())
						.div(decimal(100)))

					actualFee = bundleFee.toString()
				}
			}
			return actualFee
		},
		bundleFieldValue(fieldName) {
			const field = this.customerPaymentOption.bundle.data.filter(f => f.name === fieldName).pop();
			return field ? field.value : '';
		},
	},
}
