import customValidator from '../utility/custom-validator'

const userType = {
  member: 1,
  affiliate: 4
}

export const authFormMixin = {
  $_veeValidate: {
    validator: 'new',
  },
  data() {
    return {
      userType: userType,
      valid: true,
      loading: false,
      lazy: false,
      email: '',
      verification_code: '',
      usernameRules: [
        v => customValidator.empty(v) || this.usernameLabel + ' is required',
        v => customValidator.email(v) || this.usernameLabel + ' must be valid',
      ],
    }
  },
  methods: {
    updateData (key, value) {
      this.$emit('input', { ...this.value, [key]: value, userType: userType.affiliate })
    }
  },
  props: {
    usernameLabel: { type: String, default: 'Username' },
    passwordLabel: { type: String, default: 'Password' },
    codeLabel: { type: String, default: 'Verification Code' },
  }
}
