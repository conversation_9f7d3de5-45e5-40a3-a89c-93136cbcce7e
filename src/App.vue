<template>
  <v-app>
    <router-view v-if="loading === false" />
  </v-app>
</template>

<script>
import storage from './utility/storage'
import apiClient from './plugins/api-client'

  export default {
    name: 'App',
    data() {
      return {
        loading: true
      }
    },
    created() {
      apiClient.interceptors.response.use(undefined, function (err) {
        return new Promise(function () {
          if (err && err.status === 401 && err.config && !err.config.__isRetryRequest) {
            this.$store.dispatch('logout').then(() => {
              this.$router.push({ name: 'auth-login' })
            })
          }
          throw err
        })
      })
    },
    mounted() {
      storage.setString('is_undermaintenance', false)
      apiClient.get('/api/v1/bo/setting/all?filterByCode=system.maintenance').then((response) => {
        if (response) {
          const app = response.data.data && response.data.data['system.maintenance'] !== null ? response.data.data['system.maintenance'].app : []
          if (app.length) {
            for (let i = 0; i < app.length; i++) {
              if (app[i].key === 'affiliate' && app[i].value == true) {
                storage.setString('is_undermaintenance', true)
                this.$router.push({ name: 'maintenance' })
              }
            }
          }
        }
      })

      this.loading = false
    }
  }
</script>

<style>

</style>
