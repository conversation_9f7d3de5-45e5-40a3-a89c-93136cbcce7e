import { createRouter, createWebHistory } from 'vue-router'
import DefaultLayout from '@/views/layouts/Default.vue'
import InternalLayout from '@/views/layouts/Internal.vue'
import store from './stores'

let router = createRouter({
    history: createWebHistory(),
    routes: [
        {
            path: '/auth',
            name: 'auth',
            component: DefaultLayout,
            redirect: '/auth/login',
            children: [
                {
                    path: 'login',
                    name: 'auth-login',
                    meta: { requiredAuth: false },
                    component: () => import('./views/Login.vue')
                },
                {
                    path: 'forgot-password',
                    name: 'forgot-password',
                    meta: { requiredAuth: false },
                    component: () => import('./views/ForgotPassword.vue')
                }
            ]
        },
        {
            path: '/',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'index',
                    component: () => import('./views/Dashboard.vue')
                }
            ]
        },
        {
            path: '/dashboard',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'dashboard',
                    component: () => import('./views/Dashboard.vue')
                }
            ]
        },
        {
            path: '/withdrawal',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'withdrawal',
                    component: () => import('./views/Withdrawal.vue')
                }
            ]
        },
        {
            path: '/earnings',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'earnings',
                    component: () => import('./views/EarningsByPeriod.vue')
                }
            ]
        },
        {
            path: '/commission',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'commission',
                    component: () => import('./views/CommissionAndTurnover.vue')
                }
            ]
        },
        {
            path: '/revenue-share',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'revenue-share',
                    component: () => import('./views/RevenueShare.vue')
                }
            ]
        },
        {
            path: '/referrals',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'referrals',
                    component: () => import('./views/Referrals.vue')
                }
            ]
        },
        {
            path: '/transaction-history',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'transaction-history',
                    component: () => import('./views/TransactionHistory.vue')
                }
            ]
        },
        {
            path: '/statement-of-account',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'statement-of-account',
                    component: () => import('./views/StatementOfAccount.vue')
                }
            ]
        },
        {
            path: '/affilliate-tools',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'affiliate-tools',
                    component: () => import('./views/AffiliateTools.vue')
                }
            ]
        },
        {
            path: '/notification',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'notification',
                    component: () => import('./views/Notification.vue')
                }
            ]
        },
        {
            path: '/settings',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'settings',
                    component: () => import('./views/Settings.vue')
                }
            ]
        },
        {
            path: '/contact',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'contact',
                    component: () => import('./views/ContactUs.vue')
                }
            ]
        },
        {
            path: '/change-password',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'change_password',
                    component: () => import('./views/ChangePassword.vue')
                }
            ]
        },
        {
            path: '/google-authenticator',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'google_authenticator',
                    component: () => import('./views/GoogleAuthenticator.vue')
                }
            ]
        },
        {
            path: '/kyc',
            meta: { requiredAuth: true },
            component: InternalLayout,
            children: [
                {
                    path: '',
                    name: 'kyc',
                    component: () => import('./views/Kyc.vue')
                }
            ]
        },
        {
            path: '/maintenance',
            component: DefaultLayout,
            children: [
                {
                    path: '',
                    name: 'maintenance',
                    component: () => import('./views/Maintenance.vue')
                }
            ]
        },
    ]
})

function hasQueryParams(route) {
    return !!Object.keys(route.query).length
}

router.beforeEach((to, from, next) => {
    store.dispatch('auth/authenticated').then((valid) => {
        if (to.matched.some(record => record.meta.requiredAuth) && !valid && to.name !== 'maintenance') {
            console.log("auth/authenticated", valid)
            return next('/auth/login');
        } else if (!to.matched.some(record => record.meta.requiredAuth) && valid && to.name !== 'maintenance') {
            return next('/');
        }

        if (!hasQueryParams(to) && hasQueryParams(from) &&
            (from.path === '/auth/forgot-password' || from.path === '/auth/login') &&
            (to.path === '/auth/forgot-password' || to.path === '/auth/login')) {
            return next({ name: to.name, query: from.query });
        }

        next();
    });
});

export default router
