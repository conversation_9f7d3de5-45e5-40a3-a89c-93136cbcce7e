import apiClient from "../plugins/api-client"

export default {
  async forgotPasswordAction(data) {
    const response = await apiClient.post('/api/v1/bo/auth/forgot-password', data)
    return response.data
  },
  async updatePasswordAction(data) {
    const response = await apiClient.post('/api/v1/bo/me/change-password', data)
    return response.data
  },
  async getCodeAction(data) {
    const response = await apiClient.post('/api/v1/bo/2fa/requestcode', data)
    return response.data
  }
}