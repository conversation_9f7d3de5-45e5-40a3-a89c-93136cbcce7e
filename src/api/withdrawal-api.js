import apiClient from "../plugins/api-client"

export default {
  async withdrawAction(data) {
    const response = await apiClient.post('/api/v1/bo/me/transactions/withdraw', data)
    return response.data
  },
  async bitcoinRateAction() {
    const response = await apiClient.get('/api/v1/bo/paymentoptions/bitcoin-adjustment/withdraw')
    return response.data
  },
  async fetchCustomerPaymentOptions(params) {
    const response = await apiClient.get(`/api/v1/customer-payment-option/${ params.customerId }/fields`, { params })
    return response.data
  },
  async fetchAdjustedBitcoinExchangeRates() {
    const response = await apiClient.get(`api/v1/payment-option/rates/BITCOIN/EUR`)
    return response.data
  },
  async fetchAdjustedUsdtTrc20ExchangeRates() {
    const response = await apiClient.get(`api/v1/payment-option/rates/USDTTRC20/EUR`)
    return response.data
  },
  async fetchAdjustedUsdtErc20ExchangeRates() {
    const response = await apiClient.get(`api/v1/payment-option/rates/USDTERC20/EUR`)
    return response.data
  },
  async fetchAdjustedUsdcExchangeRates() {
    const response = await apiClient.get(`api/v1/payment-option/rates/USDC/EUR`)
    return response.data
  }
}
