import env from '../config'
import apiClient from "../plugins/api-client"

export default {
    async getKYCDetails (customerId) {
        const response = await apiClient.get(`/api/v1/kyc/status/${ customerId }`)
        return response.data
    },

    async initiate (customerId) {
        const response = await apiClient.post(`/api/v1/kyc/initiate`, {
            userReference: customerId,
            successUrl: env.jumio.successUrl,
            errorUrl: env.jumio.errorUrl
        })
        return response.data
    }
}