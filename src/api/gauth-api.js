import apiClient from "../plugins/api-client"

export default {
  async statusAction() {
    const response = await apiClient.get('/api/v1/bo/totp/status')
    return response.data
  },
  async settingAction() {
    const response = await apiClient.get('/api/v1/bo/totp/setting')
    return response.data
  },
  async setupAction() {
    const response = await apiClient.post('/api/v1/bo/totp/setup')
    return response.data
  },
  async confirmAction(data) {
    const response = await apiClient.post('/api/v1/bo/totp/confirm', data)
    return response.data
  },
  async resetAction(data) {
    const response = await apiClient.post('/api/v1/bo/totp/reset', data)
    return response.data
  },
  async enableAction(data) {
    const response = await apiClient.post('/api/v1/bo/totp/enable', data)
    return response.data
  },
  async disableAction(data) {
    const response = await apiClient.post('/api/v1/bo/totp/disable', data)
    return response.data
  },
}
