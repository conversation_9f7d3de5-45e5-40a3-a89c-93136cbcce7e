import apiClient from "../plugins/api-client"

export default {
  async login (data) {
    const response = await apiClient.post('/api/v1/auth/login', data)
    return response.data
  },
  async loadUser() {
    const response = await apiClient.get('/api/v1/auth/user')
    return response.data
  },
  async refreshToken(refreshToken) {
    const response = await apiClient.post('/api/v1/auth/refresh-token', {
      refreshToken: refreshToken
    })
    return response.data
  }
}
