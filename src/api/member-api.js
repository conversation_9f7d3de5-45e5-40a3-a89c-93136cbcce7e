import apiClient from "../plugins/api-client"

export default {
  async getMemberAction() {
    const response = await apiClient.get('/api/v1/bo/me?include=preferences')
    return response
  },
  async getSettingsAction() {
    const response = await apiClient.get('/api/v1/bo/setting/all')
    return response.data
  },
  async getProcessedPaymentOptionsAction(data) {
    const response = await apiClient.get('/api/v1/bo/me/processed-paymentoptions', data)
    return response.data
  },
  async getActivePaymentOptionsAction(data) {
    const response = await apiClient.get('/api/v1/bo/me/payment-options/active', data)
    return response.data
  },
  async getProductsAction() {
    const response = await apiClient.get('/api/v1/bo/me/products')
    return response.data
  },
  async getNotificationsAction(data) {
    const response = await apiClient.get('/api/v1/bo/member/notifications', data)
    return response.data
  },
  async readNotificationAction() {
    const response = await apiClient.put('/api/v1/bo/member/read-notifications')
    return response.data
  }
}