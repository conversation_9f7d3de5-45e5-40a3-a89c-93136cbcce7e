import apiClient from "../plugins/api-client"

export default {
  saveBanner(affiliateUserId, payload) {
    return apiClient.post(`/api/v1/affiliate/${affiliateUserId}/banner`, payload)
  },
  async fetchBanners(affiliateUserId, page = 1) {
    const response = await apiClient.get(`/api/v1/affiliate/${ affiliateUserId }/banner/?page=${ page }`)
    return response.data
  },
  async fetchBannerHTML(affiliateUserId, bannerId, size) {
    const response = await apiClient.get(`/api/v1/affiliate/${ affiliateUserId }/banner/${ bannerId }/html?size=${ size }`)
    return response.data
  },
  async deleteBanner (affiliateUserId, referralCode) {
    const response = await apiClient.delete(`/api/v1/affiliate/${ affiliateUserId }/banner/${ referralCode }`)
    return response.data
  },
  async sendContactUsEmail (payload) {
    const response = await apiClient.post(`/api/v1/affiliate/contact-us`, payload)
    return response.data
  }
}