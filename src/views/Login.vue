<template>
  <v-container
    v-if="!loading"
    class="login-bg tw:h-full tw:bg-dark"
    fluid
  >
    <v-row
      align="center"
      justify="center"
      class="tw:h-full"
    >
      <v-col
        cols="12"
        lg="4"
        sm="8"
        class="tw:text-center"
      >
        <div class="tw:flex tw:flex-col tw:items-center tw:!mb-3">
          <img
            src="/src/assets/piwi-logo.svg"
            class="tw:w-[32%] tw:sm:w-[22%]"
          >
        </div>
        <div class="tw:!my-8 tw:text-white tw:text-center">
          <h1 class="tw:!mb-3 tw:md:text-3xl tw:text-2xl tw:font-bold">
            Welcome Affiliate!
          </h1>
          <p class="tw:md:text-lg tw:text-base">
            Log in your account.
          </p>
        </div>
        <div class="login-tab-wrapper">
          <login-form
            username-label="Email"
            :logo="logo"
            @loggedin="redirectToDashboard"
            @failed="loginFailed"
          />
          <div class="tw:!mt-5 tw:text-center">
            <router-link
              to="forgot-password"
              class="no-line tw:text-warning tw:text-xs"
            >
              Forgot Password
            </router-link>
          </div>
        </div>
      </v-col>
    </v-row>
    <error-dialog
      ref="childError"
      title="Error"
      :message="errorMsg"
      :open="showErrorDialog"
      to-page=""
    />
  </v-container>
</template>

<script setup>
import { ref, onMounted, defineComponent } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import LoginForm from '@/components/forms/LoginForm.vue'
import ErrorDialog from '@/components/elements/dialog/ErrorDialog.vue'
import storage from '../utility/storage'
import logo from '@/assets/piwi-logo.svg'

defineComponent({
  name: 'LoginPage',
})

const store = useStore()
const router = useRouter()

const loading = ref(true)
const showErrorDialog = ref(false)
const errorMsg = ref('')
const childError = ref(null)

onMounted(() => {
  if (storage.getString('is_undermaintenance') === 'true') {
    router.push({ name: 'maintenance' })
  } else {
    loading.value = false
  }
})

const redirectToDashboard = async () => {
  await store.dispatch('member/getMember').then(res => {
    if (res.status === 200 && res.data.user) {
      router.push({ name: 'dashboard' })
    }
  }).catch(() => {
    openErrDialog('Account cannot be logged in. Please contact support.')
  })
}

const loginFailed = (err) => {
  if (err.status === 422) {
    openErrDialog('Account does not exist or invalid credentials.')
  }
}

const openErrDialog = (msg) => {
  childError.value.setOpen(true)
  errorMsg.value = msg
}
</script>

<style scoped>
</style>
