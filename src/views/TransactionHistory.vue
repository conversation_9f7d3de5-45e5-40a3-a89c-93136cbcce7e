<template>
  <internal-container :title="title">
    <template #main>
      <div class="tw:!mx-0 tw:!mb-2 tw:bg-[url('@/assets/login-bg.svg')] tw:bg-cover tw:bg-dark tw:md:bg-transparent tw:md:bg-none tw:!py-2 tw:w-screen tw:md:w-auto tw:!px-4 tw:!-mt-4 tw:sm:!-mt-6 tw:!-ml-2 tw:sm:!-ml-4">
        <div class="tw:w-full tw:!pb-[5px]">
          <span class="tw:text-sm tw:text-gray-500">Please note that all time stamps are using GMT -4.</span>
        </div>
        <div class="tw:lg:w-[400px] flex-container tw:w-full tw:!mb-3">
          <v-text-field
            v-model="formPayload.search"
            variant="solo"
            flat
            density="compact"
            label="Search transaction"
            single-line
            prepend-inner-icon="fas fa-search tw:!text-xs"
            clearable
            hide-details
            @blur="filter"
          />
          <btn-primary
            size="x-small"
            class="tw:!ml-4 tw:!mt-4 tw:lg:!hidden"
            @click="sheet = !sheet"
          >
            <span class="tw:!mr-[5px] tw:font-xxs tw:font-bold">Filter</span><i class="fas fa-chevron-right" />
          </btn-primary>
        </div>
      </div>
      <div class="tw:!pr-2 tw:sm:!pr-4 tw:md:!pr-0">
        <div
          class="tw:flex"
          :class="{'tw:justify-center tw:items-center': !items.length && $vuetify.display.mdAndDown}"
        >
          <div class="transaction-content">
            <div v-if="items.length">
              <div
                v-for="(item, key) in items"
                :key="key"
                class="custom-card-shadow tw:bg-white tw:!px-4 tw:!py-3 tw:!mb-3"
              >
                <v-row class="tw:!px-[5px] tw:!pt-4 tw:!pb-4 gateway-wrapper">
                  <v-col cols="6">
                    <h4 class="tw:text-lg tw:font-semibold">
                      {{ item.type }}
                    </h4>
                  </v-col>
                  <v-col
                    cols="6"
                    class="tw:text-gray-500 tw:text-right"
                  >
                    <p class="tw:text-xs">
                      {{ item.date }}
                    </p>
                  </v-col>
                  <v-col
                    cols="12"
                    class="tw:!mt-3"
                  >
                    <p class="tw:text-sm">
                      Transaction No. <span class="tw:font-bold">{{ item.number }}</span>
                    </p>
                    <p class="tw:text-sm tw:!mt-1">
                      Amount: <span class="tw:font-bold">{{ item.amount }} {{ item.currency }}</span>
                    </p>
                    <p
                      v-if="item.hash"
                      class="tw:text-sm tw:!mt-1"
                    >
                      Transaction Hash: <span class="tw:font-bold tw:break-all">{{ item.hash }}</span>
                    </p>
                    <p
                      v-if="item.notes"
                      class="tw:text-sm tw:!mt-1"
                    >
                      Notes: <span class="tw:font-bold">{{ item.notes }}</span>
                    </p>
                  </v-col>
                  <v-col
                    cols="12"
                    class="tw:!mt-3"
                  >
                    <div
                      v-if="!item.isVoided"
                      class="flex-inline-container tw:!mr-[5px]"
                    >
                      <chip-info
                        v-if="item.status === 'Requested'"
                        class="tw:uppercase"
                      >
                        {{ item.status }}
                      </chip-info>
                      <chip-info
                        v-if="item.status === 'Acknowledged'"
                        class="tw:uppercase"
                      >
                        {{ item.status }}
                      </chip-info>
                      <chip-success
                        v-if="item.status === 'Processed'"
                        class="tw:uppercase"
                      >
                        {{ item.status }}
                      </chip-success>
                      <chip-error
                        v-if="item.status === 'Declined'"
                        class="tw:uppercase"
                      >
                        {{ item.status }}
                      </chip-error>
                    </div>
                    <div
                      v-else
                      class="flex-inline-container tw:!mr-[5px]"
                    >
                      <chip-error class="tw:uppercase">
                        Voided
                      </chip-error>
                    </div>
                    <v-chip
                      v-if="item.paymentOptionType"
                      variant="elevated"
                      density="compact"
                      :color="item.paymentOptionType.toLowerCase()"
                      class="tw:!mr-[5px] tw:!text-white"
                    >
                      {{ item.paymentOptionType }}
                    </v-chip>
                  </v-col>
                </v-row>
              </div>
            </div>
            <div
              v-else
              class="tw:text-center no-record tw:!pt-8"
            >
              <div
                v-if="!loading"
                class="tw:flex tw:flex-col tw:items-center tw:justify-center"
              >
                <img
                  :src="noResult"
                  class="tw:lg:w-[25%] tw:md:w-[50%] tw:w-[50%]"
                >
                <p class="tw:text-sm tw:font-bold tw:text-black tw:!mt-[15px]">
                  No record available
                </p>
                <p class="tw:text-gray-500 tw:!mt-1">
                  We can't find any transaction at the moment.
                </p>
              </div>
            </div>
          </div>
          <div class="desktop-view transaction-filter">
            <v-sheet class="custom-card-shadow tw:bg-white tw:!overflow-visible">
              <div class="tw:!px-5 tw:!py-3 gradient-blue tw:!mb-5 tw:text-white tw:rounded-t-[15px]">
                <h1 class="tw:text-xl tw:font-semibold">
                  Transaction Filter
                </h1>
              </div>
              <div class="px-5 transaction-filter-date">
                <div class="tw:!mb-1">
                  <span class="tw:text-gray-500 tw:text-sm">Select Date Covered</span>
                </div>
                <date-picker
                  v-model="range"
                  class="tw:!mb-2"
                  use-range
                />
              </div>
              <div class="tw:!pr-5 tw:!pb-5 tw:!pl-6 tw:text-xs bottom-sheet-container">
                <div class="tw:text-gray-500 tw:text-sm tw:!mb-[5px]">
                  Select Type
                </div>
                <v-chip-group
                  v-model="selectedType"
                  column
                  mandatory
                  selected-class="chip-info"
                >
                  <v-chip
                    v-for="(type, index) in types"
                    :key="index"
                    :value="index"
                    variant="outlined"
                    class="tw:!text-xs tw:!px-3 tw:!py-0 tw:!border-gray-300 tw:!text-gray-700"
                    @click="formPayload.filter.type = type.value"
                  >
                    {{ type.label }}
                  </v-chip>
                </v-chip-group>

                <div class="tw:text-gray-500 tw:text-sm tw:!mb-[5px]">
                  Select Status
                </div>
                <v-chip-group
                  v-model="selectedStatus"
                  column
                  mandatory
                  selected-class="chip-info"
                  class="tw:!mb-[5px]"
                >
                  <v-chip
                    variant="outlined"
                    value="all"
                    class="tw:!text-xs tw:!px-3 tw:!py-0 tw:!border-gray-300 tw:!text-gray-700"
                    @click="formPayload.filter.status = []"
                  >
                    All Status
                  </v-chip>
                  <v-chip
                    v-for="(item, key) in status"
                    :key="key"
                    :value="key"
                    variant="outlined"
                    class="tw:!text-xs tw:!px-3 tw:!py-0 tw:!border-gray-300 tw:!text-gray-700"
                    @click="formPayload.filter.status = [key]"
                  >
                    {{ item }}
                  </v-chip>
                </v-chip-group>

                <div class="tw:text-gray-500 tw:text-sm tw:!mb-[5px]">
                  Select Payment
                </div>
                <v-chip-group
                  v-model="selectedPayment"
                  column
                  mandatory
                  selected-class="chip-info"
                  class="tw:!mb-[5px]"
                >
                  <v-chip
                    variant="outlined"
                    value="all"
                    class="tw:!text-xs tw:!px-3 tw:!py-0 tw:!border-gray-300 tw:!text-gray-700"
                    @click="formPayload.filter.paymentOption = ''"
                  >
                    All Payment
                  </v-chip>
                  <v-chip
                    v-for="(payment, index) in payments"
                    :key="payment"
                    :value="index"
                    variant="outlined"
                    class="tw:!text-xs tw:!px-3 tw:!py-0 tw:!border-gray-300 tw:!text-gray-700"
                    @click="formPayload.filter.paymentOption = payment"
                  >
                    {{ payment }}
                  </v-chip>
                </v-chip-group>
                <div class="transaction-filter-cta tw:!mt-[15px]">
                  <btn-success
                    class="tw:!mr-4"
                    :loading="loading"
                    size="small"
                    @click="filter"
                  >
                    Apply
                  </btn-success>
                  <export-json
                    :data="dataRow"
                    :file-name="dataFile"
                    :sheet-name="dataSheet"
                    class="tw:uppercase"
                  />
                </div>
              </div>
            </v-sheet>
          </div>
        </div>
      </div>

      <v-dialog
        v-model="loading"
        :scrim="false"
        persistent
        width="300"
      >
        <v-card
          color="primary"
          dark
        >
          <v-card-text class="tw:text-center tw:!pt-4">
            Loading
            <v-progress-linear
              indeterminate
              color="white"
              class="tw:!mb-0"
            />
          </v-card-text>
        </v-card>
      </v-dialog>
    </template>
    <template #footer>
      <v-bottom-sheet
        v-model="sheet"
        inset
        attach
      >
        <v-sheet class="tw:text-sm tw:bg-white tw:!overflow-visible">
          <div class="tw:!p-5 gradient-blue tw:!mb-5 tw:text-white">
            <v-row>
              <v-col cols="8">
                <h1 class="tw:text-lg tw:font-bold">
                  Transaction Filter
                </h1>
              </v-col>
              <v-col
                cols="4"
                class="tw:text-right tw:text-xs tw:cursor-pointer"
              >
                <span
                  class="tw:text-sm tw:text-white tw:underline"
                  @click="sheet = !sheet"
                >Dismiss</span>
              </v-col>
            </v-row>
          </div>
          <div class="tw:!px-5 transaction-filter-date">
            <v-row>
              <v-col cols="12">
                <date-picker
                  v-model="range"
                  class="tw:!mb-6 tw:!mt-2"
                  use-range
                />
              </v-col>
            </v-row>
          </div>
          <div class="tw:!pr-5 tw:!pb-5 tw:!pl-6 tw:text-xs bottom-sheet-container">
            <div class="tw:text-gray-500">
              Select Type:
            </div>
            <v-chip-group
              v-model="selectedType"
              column
              mandatory
              selected-class="chip-info"
            >
              <v-chip
                v-for="(type, index) in types"
                :key="index"
                :value="index"
                variant="outlined"
                class="tw:!text-xs tw:!px-3 tw:!py-0 tw:!border-gray-300 tw:!text-gray-700"
                @click="formPayload.filter.type = type.value"
              >
                {{ type.label }}
              </v-chip>
            </v-chip-group>

            <div class="tw:!mt-3 tw:text-gray-500">
              Select Status:
            </div>
            <v-chip-group
              v-model="selectedStatus"
              column
              mandatory
              selected-class="chip-info"
            >
              <v-chip
                variant="outlined"
                value="all"
                class="tw:!text-xs tw:!px-3 tw:!py-0 tw:!border-gray-300 tw:!text-gray-700"
                @click="formPayload.filter.status = []"
              >
                All Status
              </v-chip>
              <v-chip
                v-for="(item, key) in status"
                :key="key"
                :value="key"
                variant="outlined"
                class="tw:!text-xs tw:!px-3 tw:!py-0 tw:!border-gray-300 tw:!text-gray-700"
                @click="formPayload.filter.status = [key]"
              >
                {{ item }}
              </v-chip>
            </v-chip-group>

            <div class="tw:!mt-3 tw:text-gray-500">
              Select Payment:
            </div>
            <v-chip-group
              v-model="selectedPayment"
              column
              mandatory
              selected-class="chip-info"
            >
              <v-chip
                variant="outlined"
                value="all"
                class="tw:!text-xs tw:!px-3 tw:!py-0 tw:!border-gray-300 tw:!text-gray-700"
                @click="formPayload.filter.paymentOption = ''"
              >
                All Payment
              </v-chip>
              <v-chip
                v-for="payment in payments"
                :key="payment"
                variant="outlined"
                class="tw:!text-xs tw:!px-3 tw:!py-0 tw:!border-gray-300 tw:!text-gray-700"
                @click="formPayload.filter.paymentOption = payment"
              >
                {{ payment }}
              </v-chip>
            </v-chip-group>

            <div class="transaction-filter-cta tw:!mt-[15px]">
              <btn-success
                class="tw:!mr-4"
                :loading="loading"
                size="small"
                @click="filter"
              >
                Apply
              </btn-success>
              <export-json
                :data="dataRow"
                :file-name="dataFile"
                :sheet-name="dataSheet"
                class="tw:uppercase"
              />
            </div>
          </div>
        </v-sheet>
      </v-bottom-sheet>
    </template>
  </internal-container>
</template>

<script>
import InternalContainer from '@/components/shared/InternalContainer.vue'
import customFilters from '../utility/custom-filters'
import noResultSvg from '@/assets/no-result.svg'
import ExportJson from '@/components/shared/ExportJson.vue';
import DatePicker from '@/components/shared/DatePicker.vue';

export default {
  name: 'TransactionHistoryPage',
  components: { InternalContainer, DatePicker, ExportJson },
  filters: {
    moment(date) {
      return customFilters.moment(date, 'MMMM D, YYYY hh:mm:ss A')
    },
    capitalize(value) {
      return customFilters.capitalize(value)
    },
    decimal(value) {
      return customFilters.decimal(value, 2)
    }
  },
  data () {
    return {
      title: 'Transaction History',
      noResult: noResultSvg,
      member: JSON.parse(localStorage.getItem('member')),
      items: '',
      loading: false,
      drawer: false,
      sheet: false,
      range: {},
      types: [
        { label: 'All Type', value: [2, 10, 11, 12] },
        { label: 'Payout', value: [10] },
        { label: 'Withdrawal', value: [2] },
        { label: 'Credit', value: [11] },
        { label: 'Debit', value: [12] }
      ],
      status: [
        'Voided',
        'Requested',
        'Processed',
        'Declined',
        'Acknowledged',
      ],
      payments: [
        'Bitcoin',
        'Skrill',
        'Neteller',
        'USDTTRC20',
        'USDTERC20',
        'USDC',
      ],
      formPayload: {
        search: '',
        page: 1,
        filter: {
          fromDate: '',
          toDate: '',
          type: [2,10,11,12],
          status: [],
          paymentOption: '',
        }
      },
      dataFile: 'TransactionHistory_' + customFilters.moment(new Date(), 'DD-MM-YYYY'),
      dataRow: [],
      dataSheet: 'Transactions',
      isExported: false,
      selectedType: 0,
      selectedStatus: 'all',
      selectedPayment: 'all',
    }
  },
  computed:{
    theme(){
      return (this.$vuetify.theme.dark) ? 'dark' : 'light'
    },
  },
  mounted() {
    this.list()
  },
  methods: {
    list() {
      if (typeof this.range.start !== 'undefined' && this.range.start !== '') {
        this.formPayload.filter.fromDate = this.range.start
        this.formPayload.filter.toDate = this.range.end
      }

      if (this.formPayload.filter.status[0] === 0) {
        this.formPayload.filter.status = []
        this.formPayload.filter['isVoided'] = 1
      } else {
        this.formPayload.filter['isVoided'] = []
      }

      this.items = ''
      this.loading = true
      this.$store.dispatch('transactionHistory/list', this.formPayload).then((response) => {
        setTimeout(() => {
          const transactions = response.items
          const newJson = []
          let newJsonRow = []

          transactions.forEach((item) => {
            newJson.push({
              id: item.id,
              number: item.number,
              type: customFilters.capitalize(item.type),
              status: customFilters.capitalize(item.status),
              paymentOptionType: item.paymentOptionType,
              isVoided: item.isVoided,
              amount: item.amount,
              date: customFilters.moment(item.date, 'MMMM D, YYYY hh:mm:ss A'),
              currency: item.currency,
              hash: item.transaction_hash,
              notes: item.notes
            })

            newJsonRow.push({
              'Transaction No': item.number,
              'Payment Option': item.paymentOptionType,
              'Transaction Type': customFilters.capitalize(item.type),
              'Status': item.isVoided ? 'Voided' : customFilters.capitalize(item.status),
              'Amount': item.amount,
              'Currency': item.currency,
              'Date': customFilters.moment(item.date, 'MMMM D, YYYY hh:mm:ss A')
            })
          });

          this.items = newJson
          console.log('items', this.items)
          this.dataRow = newJsonRow
          this.loading = false
        }, 500)
      })
    },
    filter() {
       return this.list()
    },
    logout() {
      this.$store.dispatch('auth/logout').then(() => {
        this.$router.push({ name: 'auth-login' })
      })
    },
    onOpenDrawer(value) {
      this.drawer = value
    },
    onCloseDrawer(value) {
      this.drawer = value
    },
    exported() {
      this.isExported = true
      setTimeout(() => {
        this.isExported = false
      }, 3 * 1000)
    },
    getTypeFilter(index) {
      switch(index) {
        case 0: // All Type
          return [2, 10, 11, 12]
        case 1: // Payout
          return [2]
        case 2: // Withdrawal
          return [10]
        case 3: // Credit
          return [11]
        case 4: // Debit
          return [12]
        default:
          return [2, 10, 11, 12]
      }
    }
  }
}
</script>

<style>
  .v-toolbar.v-toolbar--absolute {
    z-index: 100;
    box-shadow: none;
  }
  .v-navigation-drawer--absolute {
    z-index: 102 !important;
  }
  .v-overlay--absolute {
    z-index: 101 !important;
  }
  .v-toolbar__content {
    height: 160px !important;
  }
  .v-app-bar--is-scrolled {
    transform: translateY(0px) !important;
  }
  .v-app-bar--hide-shadow.v-app-bar--is-scrolled {
    transform: translateY(-160px) !important;
  }
  .v-application .overflow-hidden {
    border-radius: 0;
  }
  .theme--light.v-sheet {
    background-color: #EFEFF4;
  }
  .transaction-filter-cta {
    display: flex;
    justify-content: flex-end;
  }
  .transaction-filter {
    display: none;
  }
  .v-bottom-sheet .dp__outer_menu_wrap.dp--menu-wrapper {
    transform: translate(-50%, -70%) !important;
  }
  @media only screen and (min-width: 768px) {
    .v-bottom-sheet .dp__outer_menu_wrap.dp--menu-wrapper {
      transform: unset !important;
    }
    .bottom-sheet-container {
      font-size: 14px;
    }
    .v-chip.v-size--default {
      font-size: 12px !important;
    }
    .v-text-field.v-text-field--solo:not(.v-text-field--solo-flat) > .v-input__control > .v-input__slot {
      -webkit-box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.10);
      -moz-box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.10);
      box-shadow: 0px 0px 15px 0px rgba(23,37,48,0.10);
    }
  }
  @media only screen and (min-width: 1024px) {
    .transaction-body-wrapper {
      display: flex;
    }
    .transaction-content {
      width: 65%;
      margin-right: 20px;
    }
    .transaction-filter {
      display: block;
      width: 35%;
      height: fit-content;
    }
  }
  @media only screen and (min-width: 1030px) {
    .transaction-content {
      width: 70%;
    }
    .transaction-filter {
      width: 30%;
    }
  }
</style>
