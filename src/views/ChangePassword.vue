<template>
  <internal-container :title="title">
    <template #main>  
      <v-card class="tw:w-full tw:!mx-auto custom-card-shadow tw:bg-white tw:!p-5">
        <v-row class="gateway-inner-wrapper tw:!px-4 tw:!py-3">
          <div class="tw:text-sm tw:md:text-sm mb-5">
            Password must contain at least 8 characters up to 16 characters.
          </div>
          <update-password-form v-model="updatePassword" />
        </v-row>
      </v-card>
    </template>
  </internal-container>
</template>
<script>
import UpdatePasswordForm from "@/components/forms/UpdatePasswordForm.vue";
import verificationIcon from "@/assets/success_inbox.svg";
import successIcon from "@/assets/success-general.svg";
import InternalContainer from '@/components/shared/InternalContainer.vue';

export default {
  components: { UpdatePasswordForm, InternalContainer },
  data () {
    return {
      updatePassword: {
        verification_code: '',
        current_password: '',
        password: '',
        repeat_password: ''
      },
      title: 'Change Password',
      dialog: false,
      successDialog: false,
      verificationIcon,
      successIcon
    }
  },
}
</script>

<style scoped>
  .container {
    padding: 0 15px !important;
  }
</style>