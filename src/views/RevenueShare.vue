<template>
  <internal-container title="Revenue Share">
    <template #main>
      <div class="tw:sm:!p-4 tw:!p-2 tw:bg-white tw:rounded-lg">
        <earnings-table-options
          v-model:selected-year="selectedYear"
          v-model:selected-period="selectedPeriod"
          v-model:items-per-page="itemsPerPage"
          v-model:search="search"
          v-model:should-hide-zero-turnover="shouldHideZeroTurnover"
          :years="years"
          :periods="periods"
          @filter="applyFilter"
          @search="searchCommissions"
        />
        <stat-summary
          class="tw:w-full tw:!mb-4"
          :summary-data="summaryData"
          :export-name="`RevenueShare_${selectedYear}-${selectedPeriod}`"
          type="revenue"
        />
        <revenue-share-in-card
          v-if="$vuetify.display.xs"
          :commissions="mappedCommissions"
          :pagination-data="paginationData"
          :items-per-page="itemsPerPage"
          @page-change="onPageChange"
        />
        <earnings-table
          v-else
          :headers="headers"
          :commissions="mappedCommissions"
          :pagination-data="paginationData"
          :items-per-page="itemsPerPage"
          type="revenue"
          @page-change="onPageChange"
        />
      </div>
    </template>
  </internal-container>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import InternalContainer from '@/components/shared/InternalContainer.vue'
import EarningsTable from '@/components/shared/EarningsTable.vue'
import EarningsTableOptions from '@/components/shared/EarningsTableOptions.vue'
import RevenueShareInCard from '@/components/revenue-share/RevenueShareInCard.vue';
import CasinoIcon from '@/assets/piwi-casino.svg'
import SlotsIcon from '@/assets/piwi-slots.svg'
import SportsIcon from '@/assets/piwi-sports.svg'
import PiwixIcon from '@/assets/piwix-icon.svg'
import StatSummary from '@/components/shared/StatSummary.vue';

const itemsPerPage = ref(10)
const currentYear = new Date().getFullYear()
const selectedYear = ref(currentYear.toString())
const years = Array.from({length: 6}, (_, i) => String(currentYear - i))
const periods = computed(() => {
  const now = new Date();
  const currentYear = now.getFullYear();
  const monthCount = selectedYear.value === currentYear.toString() ? now.getMonth() + 1 : 12;
  return [...Array.from({ length: 12 }).map((_, i) => {
    if (i >= monthCount) return null;
    const date = new Date(selectedYear.value, i, 1);
    const start = new Date(date.getFullYear(), date.getMonth(), 1);
    const end = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    const formatDate = (d) => `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
    return `${formatDate(start)} - ${formatDate(end)}`;
  }).filter(Boolean).reverse()];
});

const selectedPeriod = ref(periods.value[0]);

watch(periods, (newPeriods) => {
  selectedPeriod.value = newPeriods[0];
}, { immediate: true });

const search = ref('')
const shouldHideZeroTurnover = ref(false)
const originalCommissions = ref([])

watch(shouldHideZeroTurnover, (newValue) => {
  if (newValue) {
    originalCommissions.value = [...commissions.value]
    commissions.value = commissions.value.filter(item => item.turnover !== 'EUR 0.00')
  } else {
    commissions.value = [...originalCommissions.value]
  }
})

const applyFilter = (payload) => {
  console.log('Apply filter', payload)
}

const searchCommissions = (payload) => {
  console.log('Search commissions', payload)
}

const headers = [
  { title: 'Product', value: 'product' },
  { title: 'Member ID', value: 'memberId' },
  { title: 'Turnover', value: 'turnover' },
  { title: 'Customer W/L', value: 'w/l' },
  { title: 'Bonus', value: 'bonus' },
  { title: 'Revenue Share', value: 'revenueShare' },
]

const commissions = ref([
  { product: 'PIWIXchange', memberId: '123456', turnover: 'EUR 0.00', 'w/l': 'EUR 0.00', bonus: 'EUR 0.00', revenueShare: 'EUR 0.00' },
  { product: 'Slot', memberId: '123456', turnover: 'EUR 0.00', 'w/l': 'EUR -50.00', bonus: 'EUR 10.00', revenueShare: 'EUR -5.00' },
  { product: 'Sports', memberId: '123456', turnover: 'EUR 100.00', 'w/l': 'EUR 25.00', bonus: 'EUR 5.00', revenueShare: 'EUR 2.50' },
  { product: 'Casino', memberId: '123456', turnover: 'EUR 100.00', 'w/l': 'EUR -75.00', bonus: 'EUR 15.00', revenueShare: 'EUR -7.50' },
  { product: 'Slot', memberId: '123456', turnover: 'EUR 100.00', 'w/l': 'EUR 30.00', bonus: 'EUR 5.00', revenueShare: 'EUR 3.00' },
  { product: 'Sports', memberId: '123456', turnover: 'EUR 100.00', 'w/l': 'EUR -40.00', bonus: 'EUR 8.00', revenueShare: 'EUR -4.00' },
  { product: 'Casino', memberId: '123456', turnover: 'EUR 100.00', 'w/l': 'EUR 60.00', bonus: 'EUR 12.00', revenueShare: 'EUR 6.00' },
  { product: 'Slot', memberId: '123456', turnover: 'EUR 100.00', 'w/l': 'EUR -25.00', bonus: 'EUR 5.00', revenueShare: 'EUR -2.50' },
  { product: 'Sports', memberId: '123456', turnover: 'EUR 100.00', 'w/l': 'EUR 45.00', bonus: 'EUR 9.00', revenueShare: 'EUR 4.50' },
])

const paginationData = {
  current_page: 1,
  last_page: 11,
  from: 1,
  to: 10,
  total: 108,
  prev_page_url: null,
  next_page_url: 'https://api.example.com/commissions?page=2',
  links: Array.from({ length: 11 }, (_, i) => ({
    label: String(i + 1),
    url: `https://api.example.com/commissions?page=${i + 1}`,
  })),
}

function onPageChange(newUrl) {
  console.log('Should fetch from:', newUrl)
  // implement API fetching logic here using newUrl
}

const productIconMap = {
  Casino: CasinoIcon,
  Slot: SlotsIcon,
  Sports: SportsIcon,
  PIWIXchange: PiwixIcon
}

const mappedCommissions = computed(() => {
  return commissions.value.map(item => ({
    ...item,
    productIcon: productIconMap[item.product] || ''
  }))
})

const summaryData = [
  { title: 'Period', value: '2025-03-01 to 2025-03-31', useCurrency: false, icon: 'fas fa-calendar-week' },
  { title: 'Total W/L', value: '100.00', useCurrency: true },
  { title: 'Total Bonus', value: '100.00', useCurrency: true },
  { title: 'Total Revenue Share', value: '100.00', useCurrency: true },
]
</script>
