<template>
  <v-app :style="{ background: $vuetify.theme.themes[theme].background }">
    <v-main class="tw:!p-0">
      <router-view v-if="loading === false" />
      <span v-else>
        <v-dialog
          v-model="loading"
          :scrim="false"
          persistent
          width="300"
        >
          <v-card
            color="primary"
            dark
          >
            <v-card-text class="tw:text-center tw:!pt-4">
              Please stand by
              <v-progress-linear
                indeterminate
                color="white"
                class="tw:!mb-0"
              />
            </v-card-text>
          </v-card>
        </v-dialog>
      </span>
    </v-main>
  </v-app>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import storage from '../../utility/storage'

export default {
  name: 'InternalLayout',
  beforeRouteUpdate (to, from, next) {
    next()
  },
  data () {
    return {
      loading: true
    }
  },
  computed:{
    theme(){
      return (this.$vuetify.theme.dark) ? 'dark' : 'light'
    },
    ...mapGetters('auth', ['user']),
  },
  watch: {
    user (value) {
      if (value) {
        this.loading = false
      }
    }
  },
  mounted () {
    if (storage.getString('is_undermaintenance') == 'true') {
      this.$router.push({name: 'maintenance'})
    }
    this.loadUser()
    this.loadMember()
    this.loadProducts()
    this.loadSettings()
    this.loadNotifications()
  },
  methods: {
    ...mapActions('auth', { 'loadUser': 'loadCurrentUser' }),
    ...mapActions('member', {'loadMember': 'getMember', 'loadProducts': 'getProducts', 'loadSettings': 'getSettings', 'loadNotifications': 'getNotifications'}),
    logout () {
      this.$store.dispatch('auth/logout').then(() => {
        this.$router.push({ name: 'auth-login' })
      })
    }
  }
}
</script>
