<template>
  <v-card
    class="tw:overflow-hidden tw:tw:bg-gray-200 br-0 tw:h-full"
  >
    <header>
      <slot name="header">
        <top-bar
          :title="title"
          @open-drawer="onOpenDrawer"
        />
      </slot>
    </header>
    <nav>
      <slot name="nav">
        <nav-drawer
          :open="drawer"
          @close-drawer="onCloseDrawer"
        />
      </slot>
    </nav>
    <main>
      <slot name="main">
        <v-container class="br-wrapper tw:bg-gray-200">
          <div class="body-wrapper tw:!pb-[70px]">
            <div class="authen-body-wrap">
              <div class="authen-body-wrapper tw:!mb-6">
                <v-row>
                  <v-col
                    cols="12"
                    sm="12"
                    md="12"
                  >
                    <slot />
                  </v-col>
                </v-row>
              </div>
            </div>
          </div>
        </v-container>          
      </slot>
    </main>
  </v-card>
</template>

<script>
import NavDrawer from '@/components/shared/NavDrawer.vue'
import TopBar from '@/components/shared/TopBar.vue'


export default {
  name: 'AuthenticatedLayout',
  components: { TopBar, NavDrawer },
  data: () => ({
    title: '',
    drawer: false
  }),  
  mounted () {
    this.title = this.$route.meta.title
  },
  methods: {
    logout () {
      this.$store.dispatch('auth/logout').then(() => {
        this.$router.push({ name: 'auth-login' })
      })
    },
    onOpenDrawer(value) {
      this.drawer = value
    },
    onCloseDrawer(value) {
      this.drawer = value
    },
  },
}
</script>

<style scoped>
  @media only screen and (min-width: 767px) {
    .authen-body-wrapper {
      margin-top: 140px;
    }
  }
  .br-wrapper {
    margin-top: -7px;
  }
</style>