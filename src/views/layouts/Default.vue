<template>
  <v-app>
    <v-main>
      <router-view v-if="!loading" />
    </v-main>
  </v-app>
</template>

<script>
import storage from '../../utility/storage';
  export default {
    name: 'DefaultLayout',
    data () {
      return {
        loading: true
      }
    },
    computed:{
      theme(){
        return (this.$vuetify.theme.dark) ? 'dark' : 'light'
      }
    },
    mounted () {
      if (storage.getString('is_undermaintenance') == 'true') {
        this.$router.push({name: 'maintenance'})
      }
      this.loading = false
    }
  };
</script>