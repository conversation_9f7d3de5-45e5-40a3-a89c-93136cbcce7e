<template>
  <internal-container
    :title="title"
    :is-inner="false"
  >
    <template #main>
      <div
        v-if="isVerified"
        class="tw:md:hidden tw:block tw:!mb-3 wb-container tw:sm:!p-6 tw:!p-4 tw:w-screen tw:!-mt-6 tw:!-ml-2 tw:sm:!-ml-4"
      >
        <span class="tw:text-xs tw:text-gray-400">Withdrawable Balance</span>
        <h1 class="tw:text-2xl tw:text-white tw:!font-bold tw:!mt-2">
          <sup><i class="fas fa-euro-sign tw:text-sm tw:font-bold tw:!mr-[5px]" /></sup>{{ (parseFloat(member.available_balance) || 0).toFixed(2) }}
        </h1>
        <btn-primary
          size="x-small"
          class="tw:text-sm gradient-primary tw:!mt-1 tw:!tracking-normal tw:!px-2.5"
          append-icon="fas fa-angle-right tw:!text-sm tw:!text-dark"
          :to="{ name: 'transaction-history' }"
        >
          Transaction History
        </btn-primary>
      </div>
      <div
        v-if="isVerified"
        class="withdrawal-body-wrapper tw:!pr-2 tw:sm:!pr-4 tw:md:!pr-0"
      >
        <div class="withdrawal-gateway">
          <v-expansion-panels
            v-for="cpo in customerPaymentOptions"
            :key="cpo.code"
            color="white"
            bg-color="white"
            rounded="lg"
            hide-actions
          >
            <v-expansion-panel
              v-if="cpo.code === 'NETELLER' || cpo.code === 'SKRILL'"
              class="customize-panel-wrapper"
            >
              <default-withdrawal-form
                :customer-payment-option="cpo"
                :payment-option="cpo.code"
              />
            </v-expansion-panel>
            <v-expansion-panel
              v-if="cpo.code === 'USDTTRC20'"
              class="customize-panel-wrapper"
            >
              <usdttrc20-withdrawal-form
                :customer-payment-option="cpo"
                :payment-option="cpo.code"
              />
            </v-expansion-panel>
            <v-expansion-panel
              v-if="cpo.code === 'USDTERC20'"
              class="customize-panel-wrapper"
            >
              <usdterc20-withdrawal-form
                :customer-payment-option="cpo"
                :payment-option="cpo.code"
              />
            </v-expansion-panel>
            <v-expansion-panel
              v-if="cpo.code === 'USDC'"
              class="customize-panel-wrapper"
            >
              <usdc-withdrawal-form
                :customer-payment-option="cpo"
                :payment-option="cpo.code"
              />
            </v-expansion-panel>
            <v-expansion-panel
              v-if="cpo.code === 'BITCOIN'"
              class="customize-panel-wrapper"
            >
              <btc-withdrawal-form
                :customer-payment-option="cpo"
                :payment-option="cpo.code"
              />
            </v-expansion-panel>
          </v-expansion-panels>
        </div>
        <div class="desktop-view withdrawal-balance-desktop">
          <div class="tw:w-full custom-card-shadow tw:!p-6 wb-container tw:text-white">
            <span class="text-sm">Withdrawable Balance</span>
            <h1 class="tw:text-2xl tw:!mt-5 tw:!mb-6 tw:!font-bold">
              <sup><i class="fas fa-euro-sign tw:text-xs tw:font-bold tw:!mr-[5px]" /></sup>{{ (parseFloat(member.available_balance) || 0).toFixed(2) }}
            </h1>

            <btn-primary
              class="tw:!text-xs gradient-primary tw:!mt-1 tw:!tracking-normal tw:!px-2.5 tw:font-bold"
              append-icon="fas fa-angle-right tw:!text-sm tw:!text-dark"
              :to="{ name: 'transaction-history' }"
            >
              Transaction History
            </btn-primary>
          </div>
        </div>

        <withdrawal-reminder-dialog
          ref="childReminder"
          title="Reminder:"
          message="Your withdrawal request will be processed within 12-24 hours upon receiving approval from our game provider."
        />
      </div>

      <!-- Unable to withdraw screen -->
      <div
        v-else
        class="tw:text-center tw:flex tw:flex-col tw:items-center"
      >
        <img
          src="../assets/unable-to-withdraw.svg"
          class="tw:w-[500px]"
        >
        <h1 class="tw:!mt-4 text-black tw:font-bold">
          Unable to withdraw
        </h1>
        <p class="pending-subtext">
          You need to complete your verification in order to withdraw from your account.
        </p>

        <!-- kyc button -->
        <div class="tw:text-center tw:!mt-5">
          <btn-primary :to="{ name: 'kyc' }">
            Go to KYC Page
          </btn-primary>
        </div>
        <!-- kyc button end -->
      </div>
      <!-- Unable to withdraw screen end -->
    </template>
    <template #footer>
      <v-snackbar
        v-model="snackbar"
        :multi-line="multiLine"
        class="tw:text-sm"
      >
        Transaction ********-010132-997983-1 Withdrawal has been Acknowledged
        <span
          class="tw:text-gray-500 tw:text-sm tw:underline"
          @click="snackbar = false"
        >Dismiss</span>
      </v-snackbar>
    </template>
  </internal-container>
</template>

<script>
import DefaultWithdrawalForm from '@/components/forms/DefaultWithdrawalForm.vue'
import BtcWithdrawalForm from '@/components/forms/BtcWithdrawalForm.vue';
import InternalContainer from '@/components/shared/InternalContainer.vue';
import Usdttrc20WithdrawalForm from '@/components/forms/Usdttrc20WithdrawalForm.vue';
import Usdterc20WithdrawalForm from '@/components/forms/Usdterc20WithdrawalForm.vue';
import storage from '../utility/storage';
import { mapActions, mapGetters } from 'vuex';
import WithdrawalReminderDialog from '@/components/elements/dialog/WithdrawalReminderDialog.vue';
import UsdcWithdrawalForm from "../components/forms/UsdcWithdrawalForm.vue";

export default {
  name: 'WithdrawalPage',
  components: {
    InternalContainer,
		DefaultWithdrawalForm,
    BtcWithdrawalForm,
    Usdttrc20WithdrawalForm,
    Usdterc20WithdrawalForm,
    UsdcWithdrawalForm,
    WithdrawalReminderDialog
  },
  data () {
    return {
      title: 'Withdrawal',
      member: storage.getObject('member'),
      message: null,
      drawer: false,
      dialog: false,
      successDialog: false,
      errorDialog: false,
      withdrawalReminderDialog: false,
      multiLine: true,
      snackbar: false,
      sheet: false,
      isVerified: storage.getObject('member').verified_at !== null,
      date: new Date().toISOString().substr(0, 10),
    }
  },
  computed: {
    theme(){
      return (this.$vuetify.theme.dark) ? 'dark' : 'light'
    },
    ...mapGetters('withdrawal', [ 'customerPaymentOptions']),
  },
  mounted () {
    this.fetchPaymentOptions()
    if (this.isVerified) {
      this.showReminderModal()
    }
  },
  methods: {
    logout () {
      this.$store.dispatch('auth/logout').then(() => {
        this.$router.push({ name: 'auth-login' })
      })
    },
    onOpenDrawer(value) {
      this.drawer = value
    },
    onCloseDrawer(value) {
      this.drawer = value
    },
    showReminderModal() {
      this.withdrawalReminderDialog = true
      this.$refs.childReminder.setOpen(true)
    },
    ...mapActions('withdrawal', [
        'fetchPaymentOptions'
    ])
  },
}
</script>

<style scoped>
  .theme--light.v-sheet {
    background-color: #EFEFF4;
  }
  .v-select__selection--comma {
    font-size: 14px;
  }
  .v-list-item__title {
    font-size: 14px;
  }
  .v-data-table td {
    font-size: 12px;
  }
  .th-btn {
    background: #fff !important;
    color: #647079 !important;
  }
  .withdrawal-balance-desktop {
    display: none;
  }

  .pending-subtext, .text-black{
  color: #20303C;
  }

  .text-black{
  font-size: 20px;
  }

  @media screen and (min-width: 600px) {
    .pending-subtext{
    font-size: 13px;
    }
  }

  @media only screen and (min-width: 1024px) {
    .withdrawal-body-wrapper {
      display: flex;
    }
    .withdrawal-gateway {
      width: 65%;
      margin-right: 20px;
    }
    .withdrawal-balance-desktop {
      display: block;
      width: 35%;
      height: fit-content;
    }
  }
  @media only screen and (min-width: 1030px) {
    .withdrawal-gateway {
      width: 75%;
    }
  }
</style>
