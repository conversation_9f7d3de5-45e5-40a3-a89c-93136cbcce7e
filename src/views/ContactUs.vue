<template>
  <internal-container title="Contact Us">
    <template #main>
      <div class="tw:flex tw:justify-center">
        <div class="tw:grid tw:grid-cols-1 tw:lg:grid-cols-3 tw:gap-6 tw:w-full tw:lg:w-[80%] tw:bg-white tw:rounded-lg tw:!p-5 ">
          <!-- Contact Form Section -->
          <div class="tw:lg:col-span-2 tw:flex tw:flex-col">
            <h2 class="tw:text-2xl tw:font-bold tw:text-gray-800 tw:!mb-1">
              Get in touch with us.
            </h2>
            <p class="tw:text-gray-500 tw:!mb-6">
              Fill out the form with your inquiry.
            </p>
            
            <form  
              class="tw:flex tw:flex-col tw:flex-1 tw:h-auto"
              @submit.prevent="submitForm"
            >
              <div class="tw:!mb-4 tw:flex tw:flex-1 tw:flex-col">
                <div class="tw:flex tw:lg:gap-2 tw:lg:flex-row tw:flex-col">
                  <v-text-field
                    v-model="form.name"
                    label="Name"
                    variant="outlined"
                    density="compact"
                    class="tw:!mb-3"
                    required
                    hide-details
                  />
                  
                  <v-text-field
                    v-model="form.email"
                    label="Email Address"
                    variant="outlined"
                    density="compact"
                    class="tw:!mb-3"
                    required
                    type="email"
                    hide-details
                  />
                </div>
                
                <v-text-field
                  v-model="form.subject"
                  label="Concern / Inquiry"
                  variant="outlined"
                  density="compact"
                  class="tw:!mb-3 tw:max-h-[36px]"
                  required
                  hide-details
                />
                
                <v-textarea
                  v-model="form.message"
                  label="Message"
                  variant="outlined"
                  rows="4"
                  class="tw:!mb-5"
                  required
                  hide-details
                />
              </div>
              
              <div class="tw:flex tw:gap-3 tw:justify-end">
                <btn-success
                  flat
                  :loading="loading"
                  @click="submitForm"
                >
                  SUBMIT
                </btn-success>
                
                <btn-light
                  flat
                  @click="resetForm"
                >
                  CLEAR FORM
                </btn-light>
              </div>
            </form>
          </div>
          
          <!-- Image Section -->
          <div class="tw:hidden tw:lg:block">
            <img 
              alt="Customer Support" 
              class="tw:w-full tw:h-full tw:object-cover tw:rounded-lg"
              src="@/assets/piwi-affiliate-contact.png"
            >
          </div>
        </div>
      </div>
    </template>
  </internal-container>
</template>

<script setup>
import { ref } from 'vue'
import InternalContainer from '@/components/shared/InternalContainer.vue'

const loading = ref(false)
const form = ref({
  name: '',
  email: '',
  subject: '',
  message: ''
})

const submitForm = () => {
  loading.value = true
  
  
  
  loading.value = false
}

const resetForm = () => {
  form.value = {
    name: '',
    email: '',
    subject: '',
    message: ''
  }
}
</script>
