<template>
  <internal-container title="Contact Us">
    <template #main>
      <div class="tw:flex tw:justify-center">
        <div class="tw:grid tw:grid-cols-1 tw:lg:grid-cols-3 tw:gap-6 tw:w-full tw:lg:w-[80%] tw:bg-white tw:rounded-lg tw:!p-5 ">
          <!-- Contact Form Section -->
          <div class="tw:lg:col-span-2 tw:flex tw:flex-col">
            <h2 class="tw:text-2xl tw:font-bold tw:text-gray-800 tw:!mb-1">
              Get in touch with us.
            </h2>
            <p class="tw:text-gray-500 tw:!mb-6">
              Fill out the form with your inquiry.
            </p>
            
            <form  
              class="tw:flex tw:flex-col tw:flex-1 tw:h-auto"
              @submit.prevent="submitForm"
            >
              <div class="tw:!mb-4 tw:flex tw:flex-1 tw:flex-col">
                <div class="tw:flex tw:lg:gap-2 tw:lg:flex-row tw:flex-col">
                  <v-text-field
                    v-model="form.name"
                    label="Name"
                    variant="outlined"
                    density="compact"
                    class="tw:!mb-3"
                    required
                    hide-details
                  />
                  
                  <v-text-field
                    v-model="form.email"
                    label="Email Address"
                    variant="outlined"
                    density="compact"
                    class="tw:!mb-3"
                    required
                    type="email"
                    hide-details
                  />
                </div>
                
                <v-text-field
                  v-model="form.subject"
                  label="Concern / Inquiry"
                  variant="outlined"
                  density="compact"
                  class="tw:!mb-3 tw:max-h-[36px]"
                  required
                  hide-details
                />
                
                <v-textarea
                  v-model="form.message"
                  label="Message"
                  variant="outlined"
                  rows="4"
                  class="tw:!mb-5"
                  required
                  hide-details
                />
              </div>
              
              <div class="tw:flex tw:gap-3 tw:justify-end">
                <btn-success
                  flat
                  :loading="loading"
                  @click="submitForm"
                >
                  SUBMIT
                </btn-success>
                
                <btn-light
                  flat
                  @click="resetForm"
                >
                  CLEAR FORM
                </btn-light>
              </div>
            </form>
          </div>
          
          <!-- Image Section -->
          <div class="tw:hidden tw:lg:block">
            <img 
              alt="Customer Support" 
              class="tw:w-full tw:h-full tw:object-cover tw:rounded-lg"
              src="@/assets/piwi-affiliate-contact.png"
            >
          </div>
        </div>
      </div>
    </template>
  </internal-container>

  <!-- Success Dialog -->
  <success-dialog
    ref="childSuccess"
    title="Success"
    :message="successMessage"
    :open="showSuccessDialog"
    to-page=""
  />

  <!-- Error Dialog -->
  <error-dialog
    ref="childError"
    title="Error"
    :message="errorMessage"
    :open="showErrorDialog"
    to-page=""
  />
</template>

<script setup>
import { ref } from 'vue'
import axios from 'axios'
import InternalContainer from '@/components/shared/InternalContainer.vue'
import SuccessDialog from '@/components/elements/dialog/SuccessDialog.vue'
import ErrorDialog from '@/components/elements/dialog/ErrorDialog.vue'
import config from '@/config.js'

const loading = ref(false)
const showSuccessDialog = ref(false)
const showErrorDialog = ref(false)
const successMessage = ref('')
const errorMessage = ref('')
const childSuccess = ref(null)
const childError = ref(null)

const form = ref({
  name: '',
  email: '',
  subject: '',
  message: ''
})

const submitForm = async () => {
  // Basic form validation
  if (!form.value.name.trim()) {
    errorMessage.value = 'Please enter your name.'
    showErrorDialog.value = true
    childError.value?.setOpen(true)
    return
  }

  if (!form.value.email.trim()) {
    errorMessage.value = 'Please enter your email address.'
    showErrorDialog.value = true
    childError.value?.setOpen(true)
    return
  }

  // Basic email validation
  const emailRegex = /.+@.+\..+/
  if (!emailRegex.test(form.value.email)) {
    errorMessage.value = 'Please enter a valid email address.'
    showErrorDialog.value = true
    childError.value?.setOpen(true)
    return
  }

  if (!form.value.subject.trim()) {
    errorMessage.value = 'Please enter a subject for your inquiry.'
    showErrorDialog.value = true
    childError.value?.setOpen(true)
    return
  }

  if (!form.value.message.trim()) {
    errorMessage.value = 'Please enter your message.'
    showErrorDialog.value = true
    childError.value?.setOpen(true)
    return
  }

  loading.value = true

  try {
    // Construct the endpoint URL
    const endpoint = `${config.affiliateService}/api/internal/v2/affiliate/contact-us`

    // Prepare the request payload
    const payload = {
      name: form.value.name.trim(),
      email: form.value.email.trim(),
      subject: form.value.subject.trim(),
      message: form.value.message.trim()
    }

    // Send POST request to the affiliate service
    const response = await axios.post(endpoint, payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // Handle successful response
    if (response.status === 200 || response.status === 201) {
      successMessage.value = 'Your message has been sent successfully! We will get back to you soon.'
      showSuccessDialog.value = true
      childSuccess.value?.setOpen(true)

      // Reset form after successful submission
      resetForm()
    }

  } catch (error) {
    // Handle error response
    let errorMsg = 'An error occurred while sending your message. Please try again later.'

    if (error.response) {
      // Server responded with error status
      if (error.response.status === 400) {
        errorMsg = 'Please check your form data and try again.'
      } else if (error.response.status === 422) {
        errorMsg = error.response.data?.message || 'Please check your form data and try again.'
      } else if (error.response.status >= 500) {
        errorMsg = 'Server error. Please try again later or contact support.'
      }
    } else if (error.request) {
      // Network error
      errorMsg = 'Network error. Please check your connection and try again.'
    }

    errorMessage.value = errorMsg
    showErrorDialog.value = true
    childError.value?.setOpen(true)

    console.error('Contact form submission error:', error)
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  form.value = {
    name: '',
    email: '',
    subject: '',
    message: ''
  }
}
</script>
