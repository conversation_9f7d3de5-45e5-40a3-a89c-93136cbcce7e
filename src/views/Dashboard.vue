<template>
  <internal-container :title="title">
    <template #main>
      <v-dialog
        v-model="loading"
        :scrim="false"
        persistent
        width="300"
      >
        <v-card
          color="primary"
          dark
        >
          <v-card-text class="tw:text-center tw:!pt-4">
            Loading
            <v-progress-linear
              indeterminate
              color="white"
              class="tw:!mb-0"
            />
          </v-card-text>
        </v-card>
      </v-dialog>

      <!-- Summary Cards Section -->
      <div class="tw:bg-white tw:rounded-lg tw:!p-4 tw:!mb-5">
        <div class="tw:grid tw:grid-cols-1 tw:lg:grid-cols-4 tw:gap-4">
          <!-- Withdrawal Balance Card -->
          <div class="tw:lg:col-span-2 tw:bg-white tw:rounded-lg tw:border tw:border-gray-100 tw:!p-4 tw:flex tw:flex-col tw:justify-end tw:items-start tw:bg-[url(/src/assets/dashboard-banner.png)] tw:bg-bottom tw:bg-cover">
            <div>
              <div class="tw:font-semibold tw:text-gray-500">
                Total Withdrawable Balance
              </div>
              <div class="tw:font-bold tw:text-2xl tw:!mb-5 tw:flex tw:items-start tw:!mt-1">
                <v-icon icon="fas fa-euro-sign tw:!text-black tw:!text-sm tw:!mr-1 tw:!mt-1" />
                <span>{{ (parseFloat(member.available_balance) || 0).toFixed(2) }}</span>
              </div>
            </div>
            <v-btn
              color="primary"
              size="small"
              variant="flat"
              append-icon="fas fa-angle-right tw:!text-sm tw:!text-dark"
              class="tw:!tracking-normal tw:!px-2.5"
              :to="{ name: 'withdrawal' }"
            >
              WITHDRAW NOW
            </v-btn>
          </div>

          <div class="tw:lg:col-span-2">
            <div class="tw:grid tw:grid-cols-2 tw:gap-4">
              <!-- Registration Card -->
              <stat-card
                title="Total Registration"
                :value="summaryData.totalRegistration.toString()"
                type="registration"
                icon="fas fa-user-friends"
              />

              <!-- Conversion Card -->
              <stat-card
                title="Total Conversion"
                :value="summaryData.totalConversion.toString()"
                type="conversion"
                icon="fas fa-chart-line"
              />

              <!-- Commission Card -->
              <stat-card
                title="Total Commission"
                :value="summaryData.totalRegistration.toString()"
                use-currency
                type="commission"
              />

              <!-- Revenue Share Card -->
              <stat-card
                title="Total Revenue Share"
                :value="formatCurrency(summaryData.totalRevenueShare)"
                use-currency
                type="revenue"
                icon="fas fa-chart-pie"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Statistics Section with Charts -->
      <div class="tw:flex tw:flex-col tw:gap-5 tw:max-w-[calc(100vw-15px)] tw:md:max-w-[calc(100vw-245px-80px)]">
        <div class="tw:bg-white tw:rounded-lg tw:shadow-sm tw:md:!px-8 tw:!px-4 tw:!py-4">
          <div class="tw:flex tw:justify-between tw:items-center tw:flex-wrap tw:!gap-y-2">
            <h2 class="tw:text-xl tw:font-bold tw:text-gray-800">
              Statistics
            </h2>
            <div class="tw:overflow-hidden">
              <v-tabs
                v-model="activeTab"
                hide-slider
                bg-color="transparent"
                :density="$vuetify.display.smAndDown ? 'compact' : 'default'"
              >
                <v-tab
                  v-for="(tab, index) in tabConfigs"
                  :key="index"
                  :value="tab.id"
                  class="tw:!max-w-[140px] tw:!px-2 tw:md:!px-6 tw:!text-xs tw:first:!rounded-l-lg tw:last:!rounded-r-lg tw:!font-semibold tw:!tracking-normal"
                  :style="{
                    backgroundColor: activeTab === tab.id ? tab.activeColor : tab.bgColor,
                    color: activeTab === tab.id ? 'white' : tab.textColor
                  }"
                >
                  {{ tab.label }}
                </v-tab>
              </v-tabs>
            </div>
          </div>

          <v-window
            v-model="activeTab"
            class="tw:!pt-4 tw:min-h-[200px] tw:sm:min-h-[400px]"
          >
            <v-window-item
              v-for="(chart, index) in chartConfigs"
              :key="index"
              :value="chart.id"
              class="tw:min-h-[200px] tw:sm:min-h-[400px] tw:!mt-6"
              :class="{ 'tw:max-w-full': $vuetify.display.xs }"
            >
              <LineChart
                :data="chart.data"
                :options="chartOptions"
                class="tw:h-full tw:!w-full"
              />
            </v-window-item>
          </v-window>
        </div>
      </div>
    </template>
  </internal-container>
</template>

<script>
import moment from 'moment'
import customFilters from '../utility/custom-filters'
import noResultSvg from '@/assets/no-result.svg'
import InternalContainer from '@/components/shared/InternalContainer.vue'
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js'
import { Line as LineChart } from 'vue-chartjs'
import StatCard from '@/components/shared/StatCard.vue'
import { useDisplay } from 'vuetify'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

export default {
  name: 'DashboardPage',
  components: { InternalContainer, LineChart, StatCard },
  filters: {
    formatDate(date) {
      return customFilters.moment(date, 'MMM. DD, YYYY')
    }
  },
  data () {
    return {
      title: 'Affiliate Dashboard',
      noResult: noResultSvg,
      member: '',
      drawer: false,
      dialog: false,
      sheet: false,
      date: new Date().toISOString().substr(0, 10),
      modal: false,
      range: {},
      items: '',
      totals: '',
      currency: '',
      dataRow: [],
      dataFooter: [],
      dataFile: 'AffiliateDashboardSummary_' + customFilters.moment(new Date(), 'DD-MM-YYYY'),
      isExported: false,
      loading: false,
      formPayload: {
        limit: 10,
        page: 1,
        precision: 2,
        orderBy: 'memberId',
        dwlDateFrom: moment().startOf('month').format('YYYY-MM-DD'),
        dwlDateTo: moment().endOf('month').format('YYYY-MM-DD'),
        sort: 'asc',
        hideZeroTurnover: 0
      },
      // TODO: update labels and chartData based on api response
      labels: ['2025-03-01', '2025-03-02', '2025-03-03', '2025-03-04'],
      activeTab: 'registration',
      chartData: {
        registration: {
          id: 'registration',
          label: 'Registration',
          color: '#4CAF50',
          data: [40, 75, 25, 50]
        },
        conversion: {
          id: 'conversion',
          label: 'Total PNL',
          color: '#2196F3',
          data: [20, 18, 30, 22]
        },
        commission: {
          id: 'commission',
          label: 'Total Bonus',
          color: '#009688',
          data: [15, 12, 25, 5]
        },
        revenue: {
          id: 'revenue',
          label: 'Total Revenue Share',
          color: '#FFC107',
          data: [10, 30, 18, 20]
        }
      },
      chartOptions: {
        responsive: true,
        maintainAspectRatio: true,
        scales: {
          x: {
            ticks: {
              minRotation: useDisplay().xs.value ? 45 : 0
            }
          }
        },
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            enabled: true,
            callbacks: {
              title: function(tooltipItems) {
                return moment(tooltipItems[0].label).format('MMM DD, YYYY');
              }
            }
          }
        }
      },
      summaryData: {
        withdrawalBalance: 1988.88,
        totalRegistration: 18,
        totalConversion: 8,
        totalRevenueShare: 1380.00,
        totalCommission: 588.11
      }
    }
  },
  computed: {
    theme() {
      return (this.$vuetify.theme.dark) ? 'dark' : 'light'
    },
    chartConfigs() {
      return Object.keys(this.chartData).map(key => {
        const chart = this.chartData[key];
        return {
          id: chart.id,
          data: {
            labels: this.labels,
            datasets: [
              {
                label: chart.label,
                data: chart.data,
                borderColor: chart.color,
                backgroundColor: chart.color,
                fill: false,
                tension: 0.4,
                pointRadius: 4,
                pointBackgroundColor: chart.color
              }
            ]
          }
        };
      });
    },
    tabConfigs() {
      return [
        { id: 'registration', label: 'REGISTRATION', bgColor: '#E8F5E9', textColor: '#4CAF50', activeColor: '#4CAF50' },
        { id: 'conversion', label: 'CONVERSION', bgColor: '#E3F2FD', textColor: '#0084d1', activeColor: '#0084d1' },
        { id: 'commission', label: 'COMMISSION', bgColor: '#e8f4f3', textColor: '#009688', activeColor: '#009688' },
        { id: 'revenue', label: 'REVENUE SHARE', bgColor: '#fff5e5', textColor: '#FFC107', activeColor: '#FFC107' }
      ];
    }
  },
  mounted() {
    setTimeout(() => {
      this.member = JSON.parse(localStorage.getItem('member'))
      this.summary()
    }, 1000)
  },
  methods: {
    formatCurrency(value) {
      return value.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    summary() {
      this.loading = true
      let payload = this.formPayload;
      payload['affiliate_user_id'] = this.member.user.id;
      this.$store.dispatch('dashboard/list', payload).then((response) => {
        this.items = response.allRecords !== null ? response.allRecords : []
        this.currency = Object.keys(response.totals)[0] || 'EUR'
        this.totals = response.totals[this.currency] || false
        this.formatDataForExport(this.items)

        // Update summary data from API response
        if (this.totals) {
          this.summaryData = {
            withdrawalBalance: this.member?.available_balance || 0,
            totalRegistration: this.totals.totalRegistration || 0,
            totalConversion: this.totals.totalConversion || 0,
            totalRevenueShare: this.totals.totalRevenueShare || 0,
            totalCommission: this.totals.totalCommission || 0
          };
        }

        // Update chart data
        if (!(this.items?.length)) {
          return;
        }
        this.labels = this.items.map(item => {
          return item.date || '';
        });

        this.chartData.conversion.data = this.items.map(item => item.totalPnl || 0);
        this.chartData.commission.data = this.items.map(item => item.totalBonus || 0);
        this.chartData.revenue.data = this.items.map(item => item.totalRevenueShare || 0);
        this.chartData.registration.data = this.items.map(item => item.registrations || 0);
      }).finally(() => {
        this.loading = false
      })
    },
    formatDataForExport(data) {
      if (data.length !== 0) {
        let newJson = []
        let newJsonFooter = []

        for (let i = 0; i < data.length; i++) {
          newJson.push({'Member': data[i].memberId, 'Product': data[i].productName, 'W/L': data[i].totalPnl, 'Bonus': data[i].totalBonus, 'RevShare': data[i].totalRevenueShare })
        }

        newJsonFooter.push('')
        newJsonFooter.push('Total W/L: ' + (this.totals.totalPnl || 0.00) + ' ' + this.currency)
        newJsonFooter.push('Total Bonus: ' + (this.totals.totalBonus || 0.00) + ' ' + this.currency)
        newJsonFooter.push('Total Revenue Share: ' + (this.totals.totalRevenueShare || 0.00) + ' ' + this.currency)

        this.dataRow = newJson
        this.dataRow.push({})
        newJsonFooter.forEach((footerLine) => {
          this.dataRow.push({ Member: footerLine });
        });
      }
    },
    filter() {
      if (typeof this.range.start !== 'undefined' && this.range.start !== '') {
        this.formPayload.dwlDateFrom = customFilters.moment(this.range.start, 'YYYY-MM-DD')
        this.formPayload.dwlDateTo = customFilters.moment(this.range.end, 'YYYY-MM-DD')
      }

      return this.summary()
    },
    logout () {
      this.$store.dispatch('auth/logout').then(() => {
        this.$router.push({ name: 'auth-login' })
      })
    },
    onOpenDrawer(value) {
      this.drawer = value
    },
    onCloseDrawer(value) {
      this.drawer = value
    },
    exported() {
      this.isExported = true
      setTimeout(() => {
        this.isExported = false
      }, 3 * 1000)
    }
  }
}
</script>

<style scoped>
  .theme--light.v-sheet {
    background-color: #EFEFF4;
  }
  .date-covered-container .col-6 {
    padding-top: 0;
  }
  .dashboard-filter {
    display: none;
  }
  @media only screen and (min-width: 768px) {
    .v-menu__content {
      margin-top: 10px;
      border-radius: 5px;
    }
    .v-list-item {
      min-height: 30px;
    }
    .v-list-item__title {
      font-size: 14px !important;
    }
    .loading-dashboard {
      background-color: #EFEFF4;
    }
  }
</style>
