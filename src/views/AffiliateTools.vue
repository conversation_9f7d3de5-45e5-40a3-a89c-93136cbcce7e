<template>
  <internal-container title="Affiliate Tools">
    <template #main>
      <div class="tw:h-max tw:bg-white tw:rounded-lg tw:max-w-[95vw] tw:md:max-w-[calc(100vw-370px)]">
        <v-tabs
          v-model="activeTab"
          grow
          color="primary"
        >
          <v-tab
            value="generateTab"
            @click="clearPreview(); updateComponentKey()"
          >
            Generate
          </v-tab>
          <v-tab
            value="archiveTab"
            @click="clearPreview(); updateComponentKey()"
          >
            Archive
          </v-tab>
        </v-tabs>
        <v-window v-model="activeTab">
          <v-window-item
            value="generateTab"
          >
            <banner-generate-form :key="componentKey" />
          </v-window-item>

          <v-window-item
            value="archiveTab"
          >
            <banner-archive class="tw:!mt-5" />
          </v-window-item>
        </v-window>
      </div>
    </template>
  </internal-container>
</template>

<script>
import BannerArchive from '@/components/affiliate-tools/BannerArchive.vue'
import BannerGenerateForm from '@/components/affiliate-tools/BannerGenerateForm.vue'
import { mapActions, mapGetters, mapMutations } from 'vuex'
import { createHelpers } from 'vuex-map-fields'
import InternalContainer from '@/components/shared/InternalContainer.vue'

const { mapFields } = createHelpers({
  getterType: 'affiliateTools/getField',
  mutationType: 'affiliateTools/updateField',
})

export default {
  name: 'AffiliateTools',
  components: { InternalContainer, BannerGenerateForm, BannerArchive },
  computed: {
    ...mapGetters('affiliateTools', ['componentKey']),
    ...mapFields(['activeTab']),
  },
  methods: {
    ...mapActions('affiliateTools', ['clearPreview']),
    ...mapMutations('affiliateTools', ['updateComponentKey'])
  },
}
</script>

<style scoped>
  .v-application--is-ltr .v-tabs-bar.v-tabs-bar--is-mobile:not(.v-tabs-bar--show-arrows) > .v-slide-group__wrapper > .v-tabs-bar__content > .v-tabs-slider-wrapper + .v-tab {
    margin-left: 0;
  }
</style>
