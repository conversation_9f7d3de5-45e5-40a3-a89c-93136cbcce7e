<template>
  <v-container
    fluid
    class="login-bg tw:bg-dark fill-height"
  >
    <v-row
      align="center"
      justify="center"
      class="fill-height"
    >
      <v-col
        cols="12"
        sm="8"
        md="4"
        class="tw:text-sm tw:text-center"
      >
        <div class="tw:flex tw:flex-col tw:items-center tw:!mb-3">
          <v-img
            :src="logo"
            class="tw:md:w-[22%] tw:w-[32%]"
          />
        </div>
        <div class="tw:!mt-6 tw:text-white tw:text-center">
          <h1 class="tw:!mb-3 tw:md:text-3xl tw:text-2xl tw:font-bold">
            Under Maintenance
          </h1>
          <p>Sorry for the inconvenience but we're performing some maintenance at the moment. We'll be back shortly.</p>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import storage from '../utility/storage'
import logo from '@/assets/piwi-logo.svg'

export default {
  name: 'MaintenancePage',
  data () {
    return {
      logo,
      is_undermaintenance: storage.getString('is_undermaintenance')
    }
  },
  mounted () {
    if (this.is_undermaintenance == 'false') {
      this.$router.push({name: 'index'})
    }
  }
}
</script>

<style scoped>
</style>
