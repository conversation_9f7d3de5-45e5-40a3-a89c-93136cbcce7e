<template>
  <internal-container title="Earnings By Period">
    <template #main>
      <div class="tw:sm:!p-4 tw:!p-2 tw:bg-white tw:rounded-lg">
        <div class="tw:flex tw:sm:justify-between tw:justify-center tw:sm:flex-row tw:flex-col tw:!mb-4">
          <div class="tw:sm:w-4/6 tw:w-full tw:max-w-[600px] tw:flex tw:flex-1 tw:rounded-md tw:overflow-hidden tw:!mb-1">
            <button
              :class="[
                'tw:flex-1 tw:text-sm tw:!font-semibold tw:!py-4 tw:transition-colors',
                activeTab === 'commission' ? 'tw:!bg-success-900 tw:!text-white' : 'tw:!bg-success-50 tw:!text-success-900'
              ]"
              @click="activeTab = 'commission'"
            >
              COMMISSION
            </button>
            <button
              :class="[
                'tw:flex-1 tw:text-sm tw:!font-semibold tw:!py-4 tw:transition-colors',
                activeTab === 'revenue' ? 'tw:!bg-warning-900 tw:!text-white' : 'tw:!bg-warning-50 tw:!text-warning-900'
              ]"
              @click="activeTab = 'revenue'"
            >
              REVENUE SHARE
            </button>
          </div>
          <div class="tw:sm:w-2/6 tw:w-full tw:flex tw:items-center tw:justify-end">
            <div class="tw:text-sm tw:text-gray-500 tw:flex tw:sm:!mt-0 tw:!mt-2 tw:justify-end tw:!space-x-2">
              <number-of-items-select
                v-model="itemsPerPage"
                :options="itemsPerPageOptions"
              />
            </div>
          </div>
        </div>
        <!-- Mobile View -->
        <earnings-in-card 
          v-if="$vuetify.display.xs"
          :commissions="commissions"
          :pagination-data="paginationData"
          :items-per-page="itemsPerPage"
          :type="activeTab"
          @page-change="onPageChange"
        />
        <!-- Desktop View -->
        <earnings-in-table
          v-else
          :pagination-data="paginationData"
          :commissions="commissions"
          :items-per-page="itemsPerPage"
          :type="activeTab"
          :headers="headers"
          @page-change="onPageChange"
        />
      </div>
    </template>
  </internal-container>
</template>

<script setup>
import { computed, ref } from 'vue'
import InternalContainer from '@/components/shared/InternalContainer.vue'
import EarningsInTable from '@/components/earnings/EarningsInTable.vue'
import EarningsInCard from '@/components/earnings/EarningsInCard.vue'
import NumberOfItemsSelect from '@/components/shared/NumberOfItemsSelect.vue'

const itemsPerPage = ref(10)
const itemsPerPageOptions = [10, 20, 50, 100]

const activeTab = ref('commission')

const commissions = [
  { dateFrom: '2025-03-01', dateTo: '2025-03-31', status: 'Condition Not Met', commission: 'EUR 0.00' },
  { dateFrom: '2025-02-01', dateTo: '2025-02-28', status: 'Condition Not Met', commission: 'EUR 0.00' },
  { dateFrom: '2025-01-01', dateTo: '2025-01-31', status: 'Condition Not Met', commission: 'EUR 0.00' },
  { dateFrom: '2024-12-01', dateTo: '2024-12-31', status: 'Condition Met', commission: 'EUR 0.30' },
  { dateFrom: '2024-11-01', dateTo: '2024-11-30', status: 'Condition Not Met', commission: 'EUR 0.00' },
  { dateFrom: '2024-10-01', dateTo: '2024-10-31', status: 'Condition Not Met', commission: 'EUR 0.00' },
  { dateFrom: '2024-09-01', dateTo: '2024-09-30', status: 'Condition Met', commission: 'EUR 1.88' },
  { dateFrom: '2024-08-01', dateTo: '2024-08-31', status: 'Condition Not Met', commission: 'EUR 0.00' },
  { dateFrom: '2024-07-01', dateTo: '2024-07-31', status: 'Condition Not Met', commission: 'EUR 0.00' },
  { dateFrom: '2024-06-01', dateTo: '2024-06-30', status: 'Condition Not Met', commission: 'EUR 0.00' },
]

const paginationData = {
  current_page: 1,
  last_page: 11,
  from: 1,
  to: 10,
  total: 108,
  prev_page_url: null,
  next_page_url: 'https://api.example.com/commissions?page=2',
  links: Array.from({ length: 11 }, (_, i) => ({
    label: String(i + 1),
    url: `https://api.example.com/commissions?page=${i + 1}`,
  })),
}


const headers = computed(() => {
  const headers = [
    { title: 'Date From', value: 'dateFrom' },
    { title: 'Date To', value: 'dateTo' },
    { title: 'Status', value: 'status' },
  ]

  if (activeTab.value === 'commission') {
    headers.push({ title: 'Commission', value: 'commission' })
  } else {
    headers.push({ title: 'Revenue Share', value: 'commission' })
  }

  return headers;
})


const onPageChange = (newUrl) => {
  console.log('Should fetch from:', newUrl)
  // implement API fetching logic here using newUrl
}
</script>