<template>
  <internal-container
    :title="title"
  >
    <template #main>
      <div>
        <v-card
          class="tw:!mx-auto custom-card-shadow tw:bg-white tw:!p-5 tw:!mb-3"
          :to="{ name: verifiedAt ? null : 'kyc' }"
        >
          <div class="tw:flex tw:items-center tw:justify-between">
            <div class="tw:flex tw:items-center tw:!space-x-2">
              <v-icon
                icon="fas fa-user-circle"
                :size="$vuetify.display.mdAndUp ? 33 : 28"
                color="secondary"
                class="tw:self-start tw:!mt-2 tw:sm:!mt-1 tw:sm:self-center"
              />
              <div class="tw:flex tw:flex-col tw:!space-y-1">
                <span class="tw:text-lg tw:font-semibold tw:text-dark tw:break-all">{{ member?.full_name }}</span>
                <span class="tw:!-mt-2 tw:text-xs tw:text-gray-500 tw:break-all">{{ member?.user.email }}</span>
                <div v-if="$vuetify.display.smAndDown">
                  <div 
                    v-if="verifiedAt !== null"
                    class="tw:bg-success-50 tw:text-success tw:!px-2 tw:!py-1 tw:rounded-sm tw:flex tw:items-center tw:!space-x-1"
                  >
                    <v-icon
                      icon="fas fa-shield-check"
                      size="x-small"
                      color="success"
                    />
                    <div class="tw:text-xs">
                      Verified affiliate since <span class="tw:font-bold">{{ new Date(verifiedAt).toLocaleDateString('en-US', {month: 'long', day: 'numeric', year: 'numeric'}) }}</span>
                    </div>
                  </div>
                  <div
                    v-else
                    class="tw:bg-info/20 tw:text-info tw:!px-2 tw:!py-1 tw:rounded-sm tw:text-xs tw:flex tw:items-center tw:!space-x-1"
                  >
                    <v-icon
                      icon="fas fa-shield"
                      size="x-small"
                      color="warning"
                    />
                    <div>Verify your account</div>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="$vuetify.display.mdAndUp">
              <div 
                v-if="verifiedAt !== null"
                class="tw:!ml-5 tw:bg-success-50 tw:text-success tw:!px-2 tw:!py-1 tw:rounded-sm tw:flex tw:items-center tw:!space-x-1"
              >
                <v-icon
                  icon="fas fa-shield-check"
                  size="x-small"
                  color="success"
                />
                <div>Verified affiliate since <span class="tw:font-bold">{{ new Date(verifiedAt).toLocaleDateString('en-US', {month: 'long', day: 'numeric', year: 'numeric'}) }}</span></div>
              </div>
              <div 
                v-else
                class="tw:!ml-5 tw:bg-info/20 tw:text-info tw:!px-2 tw:!py-1 tw:rounded-sm tw:flex tw:items-center tw:!space-x-1"
              >
                <v-icon
                  icon="fas fa-shield"
                  size="x-small"
                  color="warning"
                />
                <div>Verify your account</div>
              </div>
            </div>
          </div>
        </v-card>

        <v-card
          class="tw:!mx-auto custom-card-shadow tw:bg-white tw:!p-5 tw:!mb-3"
          :to="{ name: 'change_password' }"
        >
          <p class="tw:text-lg tw:font-bold">
            Change Password
          </p>
          <p class="tw:text-sm tw:text-gray-500">
            It's a good idea to use a strong password
          </p>
        </v-card>
      
        <v-card
          class="tw:!mx-auto custom-card-shadow tw:bg-white tw:!p-5"
          :to="{ name: 'google_authenticator' }"
        >
          <div class="tw:flex tw:items-center">
            <div class="tw:w-[80%]">
              <p class="tw:text-lg tw:font-bold">
                Google Authenticator
              </p>
              <p class="tw:text-sm tw:text-gray-500">
                Strengthen your security with Google Authenticator
              </p>
            </div>
            <!-- add a spinner if gAuthLoading is true -->
            <div
              v-if="gAuthLoading"
              class="tw:w-[20%] tw:text-right"
            >
              <v-progress-circular
                indeterminate
                color="primary"
              />
            </div>
            <div
              v-else
              class="tw:sm:w-[20%] tw:w-[25%] tw:text-nowrap tw:text-right tw:text-sm tw:font-bold tw:uppercase"
            >
              <span
                v-if="setting.status === 'off'"
                class="tw:text-info"
              >Setup</span>
              <span
                v-if="setting.status ==='enabled'"
                class="tw:text-success"
              >Enabled</span>
              <span
                v-if="setting.status ==='disabled'"
                class="tw:text-danger"
              >Disabled</span>
            </div>
          </div>
        </v-card>

        <v-expansion-panels>
          <v-expansion-panel class="tw:!shadow-none custom-card-shadow customize-panel-wrapper customize-panel-bigger">
            <v-expansion-panel-title>
              <div class="tw:flex tw:justify-between tw:items-center tw:w-full">
                <div>
                  <h1 class="tw:text-lg tw:font-bold tw:!mb-[5px]">
                    Language
                  </h1>
                  <p class="tw:text-sm tw:text-gray-500">
                    Choose preferred language.
                  </p>
                </div>
                <div class="tw:flex tw:items-center tw:!mr-3">
                  <img
                    :src="en"
                    width="30px"
                    alt="Selected language"
                  >
                </div>
              </div>
            </v-expansion-panel-title>
            <v-expansion-panel-text class="tw:text-sm">
              <div class="flex-container">
                <div class="tw:flex tw:flex-col tw:items-center tw:!space-y-1 language-flag active-language tw:!mr-[5px]">
                  <img
                    :src="en"
                    width="30px"
                  >
                  <p>English</p>
                </div>
              <!-- <div class="text-center language-flag mr-[5px]">
                <img :src="de" width="30px" />
                <p>German</p>
              </div>
              <div class="text-center language-flag mr-[5px]">
                <img :src="es" width="30px" />
                <p>Spanish</p>
              </div>
              <div class="text-center language-flag mr-[5px]">
                <img :src="fr" width="30px" />
                <p>French</p>
              </div> -->
              </div>
            </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>
      </div>
    </template>
  </internal-container>
</template>

<script>
import storage from '../utility/storage';
import { mapGetters } from 'vuex'
import enSvg from '@/assets/language/flag-en.svg'
import deSvg from '@/assets/language/flag-de.svg'
import esSvg from '@/assets/language/flag-es.svg'
import frSvg from '@/assets/language/flag-fr.svg'
import InternalContainer from "@/components/shared/InternalContainer.vue";

export default {
  name: 'SettingsPage',
    components: {
      InternalContainer
  },
  data () {
    return {
      title: 'Settings',
      en: enSvg,
      de: deSvg,
      es: esSvg,
      fr: frSvg,
      gAuthLoading: true,
      drawer: false,
      member: null,
    }
  },
  computed: {
    ...mapGetters('gAuth', ['getSetting']),
    setting: function () {
      return this.getSetting
    },
    verifiedAt() {
      return this.member?.verified_at
    }
  },
  mounted () {
    if (storage.getString('is_undermaintenance') == 'true') {
      this.$router.push({name: 'maintenance'})
    }

    const memberData = localStorage.getItem('member')
    this.member = memberData ? JSON.parse(memberData) : null

    this.$store.dispatch('gAuth/setting').then(() => {
      this.gAuthLoading = false
    })
  },
}
</script>

<style scoped>
  .container {
    padding: 0 15px !important;
  }
  @media only screen and (min-width: 768px) {
    .customize-panel-bigger .v-expansion-panel-header {
      padding: 20px;
    }
  }
</style>
