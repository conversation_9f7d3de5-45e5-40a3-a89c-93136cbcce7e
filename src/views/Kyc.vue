<template>
  <internal-container title="KYC">
    <template #main>
      <kyc-form
        v-show="status === 'for-verification' && !loading"
        :disable-verify-button="initiating"
        @initiate="initiate"
      />
      <kyc-status
        v-show="status !== 'for-verification' && !loading"
        :status="status"
        :disable-verify-button="initiating"
        @initiate="initiate"
      />
    </template>
  </internal-container>
</template>

<script>
import KycForm from '@/components/kyc/KycForm.vue'
import KycStatus from '@/components/kyc/KycStatus.vue'
import {mapActions, mapGetters} from 'vuex'
import storage from '../utility/storage';
import InternalContainer from '@/components/shared/InternalContainer.vue';

export default {
  name: 'KycPage',
  components: {
    KycForm,
    KycStatus,
    InternalContainer
  },
  data () {
    return {
      drawer: false
    }
  },
  computed: {
    ...mapGetters('kyc', [ 'status', 'loading', 'initiating'])
  },
  mounted () {
    if (storage.getString('is_undermaintenance') == 'true') {
      this.$router.push({name: 'maintenance'})
    }
    this.getKYCDetails()
  },
  methods: {
    ...mapActions('kyc', [
          'getKYCDetails',
          'initiate'
        ]
    ),
    onOpenDrawer(value) {
      this.drawer = value
    },
    onCloseDrawer(value) {
      this.drawer = value
    },
  },
}
</script>
