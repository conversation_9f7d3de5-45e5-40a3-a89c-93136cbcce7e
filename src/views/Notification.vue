<template>
  <internal-container :title="title">
    <template #main>
      <div>
        <v-card
          v-for="(item, key) in list"
          :key="key"
          class="tw:!mx-auto tw:rounded-lg tw:bg-white tw:!p-5 tw:!mb-3"
        >
          <p class="tw:text-xs tw:text-gray-500">
            {{ item.dateTime }}
          </p>
          <p class="tw:text-sm tw:!mt-2">
            {{ item.message }}
          </p>
        </v-card>
      </div>
    </template>
  </internal-container>
</template>
<script>
import { mapGetters } from 'vuex'
import storage from "../utility/storage";
import InternalContainer from '@/components/shared/InternalContainer.vue';

export default {
  name: 'NotificationPage',
  components: {
    InternalContainer
  },
  data() {
    return {
      list: '',
      title: 'Notifications',
    }
  },
  computed: {
    ...mapGetters('member', ['notifications']),
  },
  watch: {
    notifications (value) {
        this.notificationList(value)
    }
  },
  mounted() {
    this.notificationList('')
  },
  methods: {
    notificationList(value) {
      let list = value
      if (value === '')   {
        list = storage.getObject('notifications')
      }

      this.list = list
    }
  },
}
</script>

<style scoped>
</style>