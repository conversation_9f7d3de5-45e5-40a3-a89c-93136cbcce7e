<template>
  <internal-container
    :title="title"
    :is-inner="false"
  >
    <template #main>
      <div class="tw:flex tw:sm:flex-row tw:flex-col tw:items-start tw:sm:items-center tw:!p-5 tw:md:!mb-0 tw:!mb-2 tw:bg-[url('@/assets/login-bg.svg')] tw:bg-cover tw:bg-dark tw:md:bg-transparent tw:md:bg-none tw:w-screen tw:md:w-auto tw:!-mt-4 tw:sm:!-mt-6 tw:!-ml-2 tw:sm:!-ml-4">
        <div class="tw:!mb-[5px] tw:!mr-2">
          <span class="tw:text-sm tw:text-gray-500 tw:!mb-3">Only 6 months of account statement available.</span>
        </div>
        <v-col
          class="tw:!pl-0"
          cols="6"
        >
          <v-select
            v-if="periods !== []"
            v-model="period"
            class="tw:sm:w-[220px] tw:w-[270px]"
            density="compact"
            :items="periods"
            label="Select month"
            variant="solo"
            hide-details
            @update:model-value="selectPeriod"
          />
        </v-col>
      </div>
      <div class="body-wrapper tw:!pr-2 tw:sm:!pr-4 tw:md:!pr-0">
        <div class="soa-body-wrapper">
          <div class="soa-content">
            <div class="soa-body custom-card-shadow tw:bg-white">
              <div class="tw:!pt-5 tw:!px-5">
                <h1 class="tw:text-xl tw:font-semibold">
                  {{ statementDate }} - Statement of Account
                </h1>
                <p class="tw:text-sm tw:!mt-1 tw:text-gray-500">
                  Date Covered: <span class="tw:font-bold tw:text-black">{{ periodFrom }} - {{ periodTo }}</span>
                </p>
              </div>
              <div v-if="items.length">
                <v-table fixed-header>
                  <thead>
                    <tr>
                      <th
                        class="tw:text-left"
                        width="20%"
                      >
                        Date
                      </th>
                      <th
                        class="tw:text-left"
                        width="60%"
                      >
                        Description
                      </th>
                      <th
                        class="tw:text-left"
                        width="20%"
                      >
                        Amount
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(item, key) in items"
                      :key="key"
                    >
                      <td class="tw:text-sm tw:!py-2">
                        {{ item.date }}
                      </td>
                      <td class="tw:text-sm tw:!py-2">
                        <span class="tw:font-bold">{{ item.type }}</span><br>{{ item.number }}<br>{{ item.status }}
                      </td>
                      <td class="tw:text-sm tw:!py-2">
                        {{ item.amount }} {{ item.currency }}
                      </td>
                    </tr>
                  </tbody>
                </v-table>
                <div class="mobile-view tw:!p-5 total-statement-wrapper">
                  <v-row class="tw:text-sm">
                    <v-col cols="8">
                      Total Earnings:
                    </v-col>
                    <v-col
                      cols="4"
                      class="tw:float-right tw:font-bold"
                    >
                      {{ total.earnings }} EUR
                    </v-col>
                    <v-col cols="8">
                      Total Withdrawn:
                    </v-col>
                    <v-col
                      cols="4"
                      class="tw:float-right tw:font-bold"
                    >
                      {{ total.withdrawn }} EUR
                    </v-col>
                    <v-col cols="8">
                      Remaining Balance:
                    </v-col>
                    <v-col
                      cols="4"
                      class="tw:float-right tw:font-bold"
                    >
                      {{ total.balance }} EUR
                    </v-col>
                  </v-row>
                  <div class="tw:flex tw:justify-end tw:!mt-7">
                    <export-json
                      :data="dataRow"
                      :file-name="dataFile"
                      :disabled="!items.length"
                      class="tw:uppercase"
                    />
                  </div>
                </div>
                <div class="ipad-view tw:!p-6 total-statement-wrapper">
                  <v-row>
                    <v-col cols="8">
                      Total Earnings:
                    </v-col>
                    <v-col
                      cols="4"
                      class="tw:float-right tw:font-bold"
                    >
                      {{ total.earnings }} EUR
                    </v-col>
                    <v-col cols="8">
                      Total Withdrawn:
                    </v-col>
                    <v-col
                      cols="4"
                      class="tw:float-right tw:font-bold"
                    >
                      {{ total.withdrawn }} EUR
                    </v-col>
                    <v-col cols="8">
                      Remaining Balance:
                    </v-col>
                    <v-col
                      cols="4"
                      class="tw:float-right tw:font-bold"
                    >
                      {{ total.balance }} EUR
                    </v-col>
                  </v-row>
                  <div class="tw:text-right tw:!mt-7">
                    <export-json
                      :data="dataRow"
                      :file-name="dataFile"
                      :disabled="!items.length"
                      class="tw:uppercase"
                    />
                  </div>
                </div>
              </div>
              <div
                v-else
                class="tw:text-center no-record tw:!py-14"
              >
                <div
                  v-if="!loading"
                  class="tw:flex tw:items-center tw:flex-col"
                >
                  <img
                    :src="noResult"
                    class="tw:md:w-[25%] tw:w-[35%]"
                  >
                  <p class="tw:text-sm tw:font-bold tw:text-black tw:!mt-[15px]">
                    No record available
                  </p>
                  <p class="tw:text-gray-500 tw:!mt-1">
                    We can't find any record for this period.
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="desktop-view soa-filter">
            <v-sheet class="custom-card-shadow tw:bg-white tw:w-full">
              <div class="tw:!px-5 tw:!py-3 gradient-blue tw:mb-5 tw:text-white">
                <h1 class="tw:text-xl tw:font-semibold">
                  Total Amount
                </h1>
              </div>
              <div class="tw:!px-5 tw:!py-5">
                <div>
                  <span class="tw:text-gray-500 tw:text-sm">Total Earnings</span>
                  <h4 class="tw:text-lg tw:!mt-1 tw:font-semibold">
                    {{ total.earnings }} EUR
                  </h4>
                </div>
                <div class="tw:!mt-[15px]">
                  <span class="tw:text-gray-500 tw:text-sm">Total Withdrawn</span>
                  <h4 class="tw:text-lg tw:!mt-1 tw:font-semibold">
                    {{ total.withdrawn }} EUR
                  </h4>
                </div>
                <div class="tw:!mt-[15px]">
                  <span class="tw:text-gray-500 tw:text-sm">Remaining Balance</span>
                  <h4 class="tw:text-lg tw:!mt-1 tw:font-semibold">
                    {{ total.balance }} EUR
                  </h4>
                </div>
                <div class="tw:text-right">
                  <export-json
                    :data="dataRow"
                    :file-name="dataFile"
                    :disabled="!items.length"
                    class="tw:uppercase"
                  />
                </div>
              </div>
            </v-sheet>
          </div>
        </div>
      </div>
      
      <v-dialog
        v-model="loading"
        :scrim="false"
        persistent
        width="300"
      >
        <v-card
          color="primary"
          dark
        >
          <v-card-text class="tw:text-center tw:!pt-4">
            Loading
            <v-progress-linear
              indeterminate
              color="white"
              class="tw:!mb-0"
            />
          </v-card-text>
        </v-card>
      </v-dialog>
    </template>
  </internal-container>
</template>

<script>
import InternalContainer from '@/components/shared/InternalContainer.vue'
import customFilters from '../utility/custom-filters'
import moment from 'moment-timezone'
import { Decimal } from 'decimal.js'
import noResultSvg from '@/assets/no-result.svg'
import ExportJson from '../components/shared/ExportJson.vue';

export default {
  name: 'StatementOfAccountPage',
  components: { InternalContainer, ExportJson },
  filters: {
    formatDate(date) {
      return customFilters.moment(date, 'MMMM D, YYYY hh:mm:ss A')
    },
    capitalize(value) {
      return customFilters.capitalize(value)
    }
  },
  data () {
    return {
      title: 'Statement of Account',
      noResult: noResultSvg,
      member: JSON.parse(localStorage.getItem('member')),
      drawer: false,
      loading: false,
      items: [],
      dataRow: [],
      dataFooter: [],
      statementDate: moment().startOf('month').format('MMMM DD'),
      periodFrom: moment().startOf('month').format('DD-MM-YYYY'),
      periodTo: moment().endOf('month').format('DD-MM-YYYY'),
      periods: [],
      period: null,
      formPayload: {
        search: '',
        filter: {
          fromDate: '',
          toDate: '',
          type: [2,12],
          status: [2],
          paymentOption: '',
        }
      },
      dataFile: 'StatementOfAccount_' + customFilters.moment(new Date(), 'DD-MM-YYYY'),
      isExported: false,
      total: {
        earnings: 0,
        withdrawn: 0,
        balance: 0
      }
    }
  },
  mounted() {
    this.list()
    this.datePeriod()
  },
  methods: {
    list() {
      this.formPayload.filter.fromDate = this.periodFrom
      this.formPayload.filter.toDate = this.periodTo
      this.items = []
      this.total.withdrawn = 0
      this.total.earnings = 0
      this.loading = true
      this.$store.dispatch('transactionHistory/list', this.formPayload).then((response) => {
      this.computeTotal(response.items)
        setTimeout(() => {
          const transactions = response.items
          const newJson = []
          let newJsonRow = []
          let newJsonFooter = []

          transactions.forEach((item) => {
            newJson.push({
              id: item.id,
              number: item.number,
              type: customFilters.capitalize(item.type),
              status: customFilters.capitalize(item.status),
              paymentOptionType: item.paymentOptionType,
              isVoided: item.isVoided,
              amount: item.amount,
              date: customFilters.moment(item.date, 'MMMM D, YYYY hh:mm:ss A'),
              currency: item.currency
            })

            newJsonRow.push({
              'Transaction No': item.number,
              'Payment Option': item.paymentOptionType,
              'Transaction Type': customFilters.capitalize(item.type),
              'Status': customFilters.capitalize(item.status),
              'Amount': item.amount,
              'Currency': item.currency,
              'Date': customFilters.moment(item.date, 'MMMM D, YYYY hh:mm:ss A')
            })
          });

          newJsonFooter.push('')
          newJsonFooter.push('Total Earnings: ' + this.total.earnings + ' EUR')
          newJsonFooter.push('Total Withdrawn: ' + this.total.withdrawn + ' EUR')
          newJsonFooter.push('Remaining Balance: ' + this.total.balance + ' EUR')

          this.items = newJson
          this.dataRow = newJsonRow
          this.dataRow.push({})
          newJsonFooter.forEach((footerLine) => {
            this.dataRow.push({ 'Transaction No': footerLine });
          });
          this.loading = false
        }, 500)
      })
    },
    computeTotal(data) {
      if (data.length !== 0) {
        for (let i = 0; i < data.length; i++) {
          if (data[i].type === 'withdraw') {
            this.total.withdrawn = new Decimal(this.total.withdrawn).plus(data[i].amount).toFixed(2)
          }
        }
      }

      this.total.balance = this.member.available_balance
    },
    datePeriod() {
      let monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]
      let today = new Date()
      let year = today.getFullYear()
      let month = today.getMonth()

      let i = 0
      let periods = []

      do {
        if (month < 0) {
          month = 11
          year--
        }

        periods.push({ title: monthNames[month] + ' ' + year, value: (month + 1) + '-01-' + year })

        month--
        i++
      } while (i < 6)

      this.periods = periods
    },
    selectPeriod() {
      const selectedDate = moment(this.period)

      this.periodFrom = moment(selectedDate).startOf('month').add(1, 'months').format('DD-MM-YYYY')
      this.periodTo = moment(selectedDate).endOf('month').add(1, 'months').format('DD-MM-YYYY')
      this.statementDate = moment(selectedDate).add(1, 'months').format('MMMM YYYY')

      this.list()
    },
    onOpenDrawer(value) {
      this.drawer = value
    },
    onCloseDrawer(value) {
      this.drawer = value
    },
    exported() {
      this.loading = true
      this.isExported = true
      setTimeout(() => {
        this.loading = false
        this.isExported = false
      }, 3 * 1000)
    }
  }
}
</script>

<style>
  .theme--light.v-sheet {
    background-color: #EFEFF4;
  }
  .v-select__selection--comma {
    font-size: 14px;
  }
  .v-list-item__title {
    font-size: 14px;  
  }
  .v-data-table td {
    font-size: 12px;
  }
  .soa-filter {
    display: none;
  }
  @media only screen and (min-width: 768px) {
    .transaction-filter-date {
      top: 0;
      right: 6%;
    }
    .transaction-filter-date .pl-6{
      padding-top: 10px;
    }
    .soa-body .v-data-table {
      margin: 20px 30px;
    }
    .soa-body .v-data-table th {
      font-size: 16px;
    }
    .soa-body .v-data-table td {
      font-size: 14px;
      line-height: 1.4;
      padding: 10px 15px;
    }
  }
  @media only screen and (min-width: 1024px) {
    .soa-body-wrapper {
      display: flex;
    }
    .soa-content {
      width: 65%;
      margin-right: 20px;
    }
    .soa-filter {
      display: block;
      width: 35%;
      height: fit-content;
    }
  }
  @media only screen and (min-width: 1030px) {
    .soa-content {
      width: 75%;
    }
    .soa-filter {
      width: 25%;
    }
  }
</style>
