<template>
  <v-container
    class="login-bg tw:bg-dark tw:h-full"
    fluid
  >
    <v-row
      align="center"
      justify="center"
      class="fill-height"
    >
      <v-col
        cols="12"
        lg="4"
        sm="8"
        class="tw:text-sm tw:text-center"
      >
        <div class="tw:flex tw:flex-col tw:items-center tw:!mb-3">
          <img
            src="/src/assets/piwi-logo.svg"
            class="tw:w-[32%] tw:sm:w-[22%]"
          >
        </div>
        <div class="tw:!mb-3 tw:!mt-8 tw:text-white tw:text-center">
          <h1 class="tw:md:text-3xl tw:text-2xl tw:font-bold">
            Forgot Password
          </h1>
        </div>
        <div class="login-tab-wrapper">
          <forgot-password-form
            username-label="Email"
            @loggedin="redirectToDashboard"
          />
          <div class="tw:!mt-5 tw:text-center">
            <router-link
              to="login"
              class="no-line tw:text-warning tw:text-xs"
            >
              Back to Login
            </router-link>
          </div>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import ForgotPasswordForm from '@/components/forms/ForgotPasswordForm.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const redirectToDashboard = () => {
  router.push({ name: 'dashboard' })
}
</script>