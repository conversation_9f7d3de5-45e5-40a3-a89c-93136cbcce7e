<template>
  <internal-container title="Referrals">
    <template #main>
      <div class="tw:sm:!p-4 tw:!p-2 tw:bg-white tw:rounded-lg">
        <!-- Filter Section -->
        <!-- Mobile Filter Button -->
        <div
          v-if="$vuetify.display.xs"
          class="tw:!mb-4"
        >
          <btn-info
            flat
            prepend-icon="fas fa-sliders-h tw:!text-white"
            @click="showFilters = !showFilters"
          >
            {{ showFilters ? "Hide" : "Show" }} FILTERS
          </btn-info>
        </div>

        <div
          v-if="showFilters || $vuetify.display.smAndUp"
          class="tw:flex tw:sm:flex-row tw:flex-col tw:flex-wrap tw:!gap-2 tw:!mb-4 tw:max-w-3xl"
        >
          <div class="tw:flex tw:!gap-2">
            <v-select
              v-model="selectedYear"
              :items="years"
              label="Year"
              variant="outlined"
              density="compact"
              class="tw:w-min tw:sm:w-auto tw:sm:min-w-[120px]"
              hide-details
            />
            <v-select
              v-model="selectedMonth"
              :items="months"
              label="Month"
              variant="outlined"
              density="compact"
              class="tw:w-min tw:sm:w-auto tw:sm:min-w-[120px]"
              hide-details
            />
            <v-select
              v-model="selectedCategory"
              :items="categories"
              label="Category"
              variant="outlined"
              density="compact"
              class="tw:sm:min-w-[120px] tw:!flex-none tw:sm:w-auto tw:w-[115px]"
              hide-details
            />
          </div>

          <div class="tw:flex tw:!gap-2">
            <btn-success
              class="tw:flex-1 tw:sm:flex-auto"
              flat
              @click="applyFilter"
            >
              APPLY FILTER
            </btn-success>
            <btn-light
              class="tw:flex-1 tw:sm:flex-auto"
              flat
              @click="resetFilter"
            >
              RESET FILTER
            </btn-light>
          </div>
        </div>

        <div class="tw:flex tw:justify-between tw:items-end tw:sm:items-center tw:flex-wrap tw:!gap-y-2 tw:!mb-3">
          <div class="tw:flex tw:items-start tw:sm:flex-row tw:flex-col tw:!gap-3">
            <!-- Stats Card -->
            <stat-card
              title="Total Referrals"
              :value="totalReferrals.toString()"
              type="conversion"
              icon="fas fa-user-plus"
              :use-currency="false"
              class="tw:sm:w-[260px] tw:w-[220px]"
            />

            <!-- Filter Checkboxes -->
            <div class="tw:flex tw:flex-col tw:items-start">
              <div class="tw:flex tw:flex-row tw:sm:flex-col tw:items-start">
                <v-checkbox
                  v-model="filters.registered"
                  label="Registered"
                  class="tw:sm:!text-sm tw:!text-xs"
                  hide-details
                  density="compact"
                />
                <v-checkbox
                  v-model="filters.deposited"
                  label="Deposited"
                  class="tw:sm:!text-sm tw:!text-xs"
                  hide-details
                  density="compact"
                />
              </div>
              <div
                v-if="hasFilterError"
                class="tw:text-danger tw:text-sm tw:!pr-5"
              >
                Please select at least one to apply filter
              </div>
            </div>
          </div>

          <!-- Items Per Page -->
          <div class="tw:flex tw:justify-end">
            <number-of-items-select
              v-model="itemsPerPage"
              :options="[10, 20, 50, 100]"
            />
          </div>
        </div>

        <!-- Mobile View -->
        <referrals-in-card
          v-if="$vuetify.display.xs"
          :referrals="referrals"
          :pagination-data="paginationData"
          :items-per-page="itemsPerPage"
          @page-change="onPageChange"
        />

        <!-- Desktop View -->
        <referrals-table
          v-else
          :headers="headers"
          :referrals="referrals"
          :pagination-data="paginationData"
          :items-per-page="itemsPerPage"
          @page-change="onPageChange"
        />
      </div>
    </template>
  </internal-container>
</template>

<script setup>
import {computed, defineComponent, ref, watch} from 'vue'
import StatCard from '@/components/shared/StatCard.vue'
import InternalContainer from '@/components/shared/InternalContainer.vue'
import NumberOfItemsSelect from '@/components/shared/NumberOfItemsSelect.vue'
import ReferralsInCard from '@/components/referrals/ReferralsInCard.vue'
import ReferralsTable from '@/components/referrals/ReferralsTable.vue'

defineComponent({
  name: 'ReferralsView'
})

const showFilters = ref(false)

// Filter state
const selectedMonth = defineModel('selectedPeriod', {type: String, default: undefined})
const selectedCategory = ref(null)

const selectedYear = defineModel('selectedYear', { type: String })
const currentYear = new Date().getFullYear()
const years = Array.from({length: 6}, (_, i) => String(currentYear - i))

const allMonths = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
const months = computed(() => {
  const currentYear = new Date().getFullYear().toString()
  if (selectedYear.value === currentYear) {
    const currentMonth = new Date().getMonth()
    return allMonths.slice(0, currentMonth + 1)
  }
  return allMonths
})

const categories = ['Active', 'Inactive']

const filters = ref({
  registered: false,
  deposited: true
})

// Table configuration
const headers = [
  { title: 'Member ID', value: 'memberId' },
  { title: 'Date Registered', value: 'dateRegistered' },
  { title: 'Date Joined', value: 'dateJoined' }
]

// Pagination
const itemsPerPage = ref(10)
const totalReferrals = ref(14)

// Mock data
const referrals = [
  { memberId: '5090', dateRegistered: '11/28/2018', dateJoined: '09/02/2019' },
  { memberId: '5091', dateRegistered: '12/11/2018', dateJoined: '08/11/2023' },
  { memberId: '5092', dateRegistered: '03/06/2019', dateJoined: '03/06/2019' },
  { memberId: '5093', dateRegistered: '02/07/2023', dateJoined: '04/12/2023' },
  { memberId: '5094', dateRegistered: '11/28/2018', dateJoined: '09/02/2019' },
  { memberId: '5095', dateRegistered: '12/11/2018', dateJoined: '08/11/2023' },
  { memberId: '5096', dateRegistered: '03/06/2019', dateJoined: '03/06/2019' },
  { memberId: '5097', dateRegistered: '02/07/2023', dateJoined: '04/12/2023' },
  { memberId: '5098', dateRegistered: '11/28/2018', dateJoined: '09/02/2019' },
  { memberId: '5099', dateRegistered: '12/11/2018', dateJoined: '08/11/2023' }
]

const paginationData = {
  current_page: 1,
  last_page: 6,
  from: 1,
  to: 10,
  total: 54,
  prev_page_url: null,
  next_page_url: 'https://api.example.com/referrals?page=2',
  links: Array.from({ length: 6 }, (_, i) => ({
    label: String(i + 1),
    url: `https://api.example.com/referrals?page=${i + 1}`,
  })),
}

const hasFilterError = ref(false)

const applyFilter = () => {
  console.log('Applying filters:', { selectedYear, selectedMonth, selectedCategory, filters })
}

const resetFilter = () => {
  hasFilterError.value = false;
  selectedYear.value = undefined
  selectedMonth.value = undefined
  selectedCategory.value = undefined
  itemsPerPage.value = 10
  filters.value = { registered: false, deposited: true }
}

watch(() => filters.value, (val) => {
  hasFilterError.value = !val.registered && !val.deposited;
}, { deep: true });

const onPageChange = (newUrl) => {
  console.log('Should fetch from:', newUrl)
  // implement API fetching logic here using newUrl
}
</script>

