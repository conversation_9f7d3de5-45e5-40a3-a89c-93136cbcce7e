<template>
  <internal-container :title="title">
    <template #main>
      <div>
        <v-card class="tw:!mx-auto custom-card-shadow tw:bg-white tw:!p-5 tw:!mb-3">
          <g-auth-status
            :status="setting.status"
            @reset="showResetAuthenticator"
            @status-toggled="statusToggled"
          />
        </v-card>
        <v-card
          v-if="isEnabled && setting.status === 'off'"
          class="tw:!mx-auto custom-card-shadow tw:bg-white tw:!p-5"
        >
          <g-auth-setup
            :show-reset="showReset"
            :qr-code="qrCode"
            :secret-key="secretKey"
            :status="setting.status"
          />
        </v-card>
        <v-card
          v-if="setting.status !== 'off' && showReset"
          class="tw:!mx-auto custom-card-shadow tw:bg-white tw:!p-5"
        >
          <g-auth-reset
            :show-reset="showReset"
            :qr-code="qrCode"
            :secret-key="secretKey"
            :status="setting.status"
          />
        </v-card>
      </div>
    </template>
  </internal-container>
</template>
<script>
import GAuthStatus from '@/components/google-authenticator/GAuthStatus.vue'
import GAuthSetup from '@/components/google-authenticator/GAuthSetup.vue'
import storage from '@/utility/storage'
import { mapGetters } from 'vuex'
import GAuthReset from '@/components/google-authenticator/GAuthReset.vue'
import InternalContainer from '@/components/shared/InternalContainer.vue';

export default {
  components: { GAuthReset, GAuthStatus, GAuthSetup, InternalContainer },
  data () {
    return {
      title: 'Google Authenticator',
      isLoading: true,
      setting: {},
      setup: {},
      showReset: false,
      drawer: false,
      isEnabled: false,
    }
  },
  computed: {
    ...mapGetters('gAuth', ['getSetup']),
    qrCode () {
      const qrCode = this.getSetup['qr_code']
      return qrCode ? `data:image/png;base64,${qrCode}` : ''
    },
    secretKey () {
      return this.getSetup['secret']
    },
  },
  mounted () {
    this.setting = storage.getObject('gauth_setting')

    if (this.setting.status === 'off') {
      this.$store.dispatch('gAuth/setup').then(() => {
        this.isLoading = false
      })
    }
    this.isEnabled = this.setting.status === 'enabled'
  },
  methods: {
    showResetAuthenticator () {
      this.showReset = true
    },
    onCloseDrawer (value) {
      this.drawer = value
    },
    statusToggled () {
      this.isEnabled = !this.isEnabled
      this.setting = storage.getObject('gauth_setting')
    },
    showSetup () {
      console.log((this.isEnabled && this.setting.status === 'off'))
      console.log((this.setting.status !== 'off' && this.showReset))
      return (this.isEnabled && this.setting.status === 'off') || (this.setting.status !== 'off' && this.showReset)
    }
  }
}
</script>

<style scoped>
.container {
  padding: 0 15px !important;
}
</style>
