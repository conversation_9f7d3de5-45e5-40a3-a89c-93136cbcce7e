import Decimal from 'decimal.js';

export function decimal(x) {
  try {
    return new Decimal(x)
  } catch (e) {
    console.error(e)
    return new Decimal('0.00')
  }
}

export function add(x, y) {
  return decimal(x).plus(decimal(y))
}

export function div(x, y) {
  return decimal(x).div(decimal(y))
}

export function mul(x, y) {
  return decimal(x).mul(decimal(y))
}

export const toFixed = (x, decimalPlaces = 2) => {
  return decimal(x).toFixed(decimalPlaces)
}

export const inBetween = (num, start, end) => {
  let value = new decimal(num);
  return end === null || end === '' ? value.greaterThanOrEqualTo(decimal(start)) : value.greaterThanOrEqualTo(decimal(start)) && value.lessThanOrEqualTo(decimal(end));
}