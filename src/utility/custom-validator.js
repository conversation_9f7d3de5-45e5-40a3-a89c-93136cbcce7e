export default {
  length(value, min, max) {
    const length = String(value).length

    return length < min || length > max
  },
  email(value) {
    return /.+@.+\..+/.test(value)
  },
  number(value) {
    return /[0-9]/.test(value)
  },
  letters(value) {
    return /[a-z]/.test(value)
  },
  otp(value) {
    return this.length(value, 6, 6) && this.number(value)
  },
  equal(x, y) {
    return x === y;
  },
  empty(value) {
    return value === '' || value === 0;
  },
  zero(value) {
    return value === 0 || value === 0.00;
  },
  isSocialMedia(value) {
    const socialMediaDomains = ['facebook.com', 'instagram.com', 'twitter.com', 't.me', 'wa.me', 'reddit.com'];
    try {
      let url = new URL(value);
      const domain = url.hostname.replace('www.','');
      return socialMediaDomains.includes(domain);
    } catch (err) {
      console.error(err);
      const domain = value !== null || value !== '' ? value.replace('www.', '') : value;
      return socialMediaDomains.includes(domain);
    }
  },
  isBalanceEnough(balance, amount) {
    return Number(balance) > Number(amount);
  }
}