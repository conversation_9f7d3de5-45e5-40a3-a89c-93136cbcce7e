//import moment from 'moment'
import moment from 'moment-timezone'

moment.tz.setDefault('Etc/GMT+4')

export default {
  moment(date, format) {
    if (format === '') {
      format = 'M/d/YYYY'
    }
    return moment(date).format(format);
  },
  capitalize(value, allWordsIncluded = false) {
    if (!value) return '';
    value = value.toString();
    const splitArray = value.split('_');
    let capitalizedValue = splitArray[0].charAt(0).toUpperCase() + splitArray[0].slice(1);

    if (allWordsIncluded) {
      capitalizedValue = value
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
    }

    return capitalizedValue;
  },
  decimal(value, precision) {
    return isNaN(value) ? 0 : parseFloat(value).toFixed(precision)
  },
  inBetween(value, start, end) {
    const amount = parseFloat(value)
    const amountStart = parseFloat(start)
    const amountEnd = parseFloat(end)

    return amount.valueOf() >= amountStart.valueOf() && value.valueOf() <= amountEnd.valueOf()
  }
}

