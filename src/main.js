import { createApp } from 'vue'
import './plugins/api-client'
import App from './App.vue'
import router from './router'
import store from './stores'
import vuetify from './plugins/vuetify'
import { defineRule, configure, Form, Field, ErrorMessage } from 'vee-validate'
import { required } from '@vee-validate/rules'

const app = createApp(App)

app.config.compatConfig = {
  MODE: 3 // Vue 3 compat mode
}

defineRule('required', required)

app.component('VeeForm', Form)
app.component('VeeField', Field)
app.component('ErrorMessage', ErrorMessage)

configure({
  // Add your global configuration here
})

app.use(router)
app.use(store)
app.use(vuetify)

app.mount('#app')
