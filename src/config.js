const config = {
  api: {
    url: import.meta.env.VITE_API_URL,
  },
  bo: {
    // TODO: should this be exposed to the client?
    accessToken: import.meta.env.VITE_BO_ACCESS_TOKEN,
  },
  jumio: {
    successUrl: import.meta.env.VITE_JUMIO_SUCCESS_URL,
    errorUrl: import.meta.env.VITE_JUMIO_ERROR_URL,
  },
  affiliateService: import.meta.env.VITE_SERVICE_AFFILIATE_INTERNAL_V1,
}

export default config